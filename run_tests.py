#!/usr/bin/env python3
"""
Comprehensive test runner for the crypto trading system.
"""
import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(command)}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📊 Duration: {duration:.2f} seconds")
        
        if result.stdout:
            print(f"\n📝 Output:")
            print(result.stdout)
        
        if result.stderr:
            print(f"\n⚠️  Errors/Warnings:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
        else:
            print(f"❌ {description} - FAILED (exit code: {result.returncode})")
        
        return result.returncode == 0, duration, result.stdout, result.stderr
    
    except Exception as e:
        print(f"❌ {description} - ERROR: {str(e)}")
        return False, 0, "", str(e)

def run_unit_tests():
    """Run unit tests."""
    command = [
        sys.executable, "-m", "pytest",
        "tests/unit/",
        "-v",
        "--tb=short",
        "-m", "unit",
        "--cov=crypto_trading_system",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov/unit"
    ]
    
    return run_command(command, "Unit Tests")

def run_integration_tests():
    """Run integration tests."""
    command = [
        sys.executable, "-m", "pytest",
        "tests/integration/",
        "-v",
        "--tb=short",
        "-m", "integration",
        "--cov=crypto_trading_system",
        "--cov-append",
        "--cov-report=html:htmlcov/integration"
    ]
    
    return run_command(command, "Integration Tests")

def run_e2e_tests():
    """Run end-to-end tests."""
    command = [
        sys.executable, "-m", "pytest",
        "tests/e2e/",
        "-v",
        "--tb=short",
        "-m", "e2e",
        "--cov=crypto_trading_system",
        "--cov-append",
        "--cov-report=html:htmlcov/e2e"
    ]
    
    return run_command(command, "End-to-End Tests")

def run_performance_tests():
    """Run performance tests."""
    command = [
        sys.executable, "-m", "pytest",
        "tests/performance/",
        "-v",
        "--tb=short",
        "-m", "performance",
        "--benchmark-only",
        "--benchmark-sort=mean"
    ]
    
    return run_command(command, "Performance Tests")

def run_all_tests():
    """Run all test suites."""
    command = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--cov=crypto_trading_system",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov/complete",
        "--cov-report=xml:coverage.xml"
    ]
    
    return run_command(command, "All Tests")

def run_specific_tests(test_path):
    """Run specific test file or directory."""
    command = [
        sys.executable, "-m", "pytest",
        test_path,
        "-v",
        "--tb=short"
    ]
    
    return run_command(command, f"Specific Tests: {test_path}")

def check_test_environment():
    """Check if test environment is properly set up."""
    print("🔍 Checking test environment...")
    
    # Check if virtual environment is activated
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment is activated")
    else:
        print("⚠️  Virtual environment may not be activated")
    
    # Check required packages
    required_packages = [
        'pytest',
        'pytest-cov',
        'pytest-asyncio',
        'pytest-benchmark',
        'autogen-core',
        'supabase',
        'anthropic'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} is available")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Please install missing packages before running tests.")
        return False
    
    # Check test directories exist
    test_dirs = ['tests/unit', 'tests/integration', 'tests/e2e', 'tests/performance']
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            print(f"✅ {test_dir} directory exists")
        else:
            print(f"❌ {test_dir} directory is missing")
    
    return len(missing_packages) == 0

def generate_test_report(results):
    """Generate a comprehensive test report."""
    print(f"\n{'='*80}")
    print("📊 TEST EXECUTION SUMMARY")
    print(f"{'='*80}")
    
    total_duration = sum(result[1] for result in results.values())
    passed_tests = sum(1 for result in results.values() if result[0])
    total_tests = len(results)
    
    print(f"📈 Overall Results:")
    print(f"   • Total Test Suites: {total_tests}")
    print(f"   • Passed: {passed_tests}")
    print(f"   • Failed: {total_tests - passed_tests}")
    print(f"   • Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print(f"   • Total Duration: {total_duration:.2f} seconds")
    
    print(f"\n📋 Detailed Results:")
    for test_name, (success, duration, stdout, stderr) in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   • {test_name}: {status} ({duration:.2f}s)")
    
    # Generate HTML report if coverage data exists
    if os.path.exists('htmlcov'):
        print(f"\n📄 Coverage reports generated in 'htmlcov/' directory")
    
    print(f"\n🕐 Test execution completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests == total_tests

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Crypto Trading System Test Runner")
    parser.add_argument(
        '--suite',
        choices=['unit', 'integration', 'e2e', 'performance', 'all'],
        default='all',
        help='Test suite to run (default: all)'
    )
    parser.add_argument(
        '--specific',
        type=str,
        help='Run specific test file or directory'
    )
    parser.add_argument(
        '--check-env',
        action='store_true',
        help='Only check test environment setup'
    )
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='Skip coverage reporting'
    )
    
    args = parser.parse_args()
    
    print("🚀 Crypto Trading System - Test Runner")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check environment first
    if not check_test_environment():
        print("\n❌ Test environment check failed. Please fix issues before running tests.")
        return 1
    
    if args.check_env:
        print("\n✅ Test environment check completed successfully.")
        return 0
    
    results = {}
    
    try:
        if args.specific:
            # Run specific tests
            success, duration, stdout, stderr = run_specific_tests(args.specific)
            results[f"Specific: {args.specific}"] = (success, duration, stdout, stderr)
        
        elif args.suite == 'unit':
            success, duration, stdout, stderr = run_unit_tests()
            results["Unit Tests"] = (success, duration, stdout, stderr)
        
        elif args.suite == 'integration':
            success, duration, stdout, stderr = run_integration_tests()
            results["Integration Tests"] = (success, duration, stdout, stderr)
        
        elif args.suite == 'e2e':
            success, duration, stdout, stderr = run_e2e_tests()
            results["End-to-End Tests"] = (success, duration, stdout, stderr)
        
        elif args.suite == 'performance':
            success, duration, stdout, stderr = run_performance_tests()
            results["Performance Tests"] = (success, duration, stdout, stderr)
        
        elif args.suite == 'all':
            # Run all test suites
            test_functions = [
                ("Unit Tests", run_unit_tests),
                ("Integration Tests", run_integration_tests),
                ("End-to-End Tests", run_e2e_tests),
                ("Performance Tests", run_performance_tests)
            ]
            
            for test_name, test_func in test_functions:
                success, duration, stdout, stderr = test_func()
                results[test_name] = (success, duration, stdout, stderr)
    
    except KeyboardInterrupt:
        print("\n⚠️  Test execution interrupted by user.")
        return 1
    
    except Exception as e:
        print(f"\n❌ Unexpected error during test execution: {str(e)}")
        return 1
    
    # Generate final report
    all_passed = generate_test_report(results)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
