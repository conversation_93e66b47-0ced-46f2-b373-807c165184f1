#!/usr/bin/env python3
"""
OKX Basic Functionality Test
Tests OKX integration without requiring authentication
"""

import os
import sys
import ccxt
from datetime import datetime
from dotenv import load_dotenv

# Load demo environment
load_dotenv('.env.demo')

class OKXBasicTester:
    def __init__(self):
        self.test_results = []
        
    def log_test(self, test_name, success, message="", data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        result = {
            'timestamp': timestamp,
            'test': test_name,
            'status': status,
            'message': message,
            'data': data
        }
        
        self.test_results.append(result)
        print(f"[{timestamp}] {status} - {test_name}")
        if message:
            print(f"    {message}")
        if data and isinstance(data, dict):
            for key, value in data.items():
                print(f"    {key}: {value}")
        print()
    
    def test_okx_initialization(self):
        """Test OKX exchange initialization"""
        try:
            # Initialize OKX without credentials (public endpoints only)
            exchange = ccxt.okx({
                'sandbox': True,
                'enableRateLimit': True,
                'timeout': 30000,
            })
            
            self.log_test("OKX Initialization", True,
                         "OKX exchange initialized successfully",
                         {
                             "exchange_id": exchange.id,
                             "sandbox_mode": exchange.sandbox,
                             "rate_limit": exchange.enableRateLimit
                         })
            return exchange
            
        except Exception as e:
            self.log_test("OKX Initialization", False, f"Error: {str(e)}")
            return None
    
    def test_public_market_data(self, exchange):
        """Test public market data access"""
        try:
            # Test fetching ticker data (public endpoint)
            ticker = exchange.fetch_ticker('BTC/USDT')
            
            required_fields = ['symbol', 'last', 'bid', 'ask']
            missing_fields = [field for field in required_fields if field not in ticker or ticker[field] is None]
            
            if missing_fields:
                self.log_test("Public Market Data", False, 
                             f"Missing fields: {missing_fields}")
                return False
            
            self.log_test("Public Market Data", True,
                         "BTC/USDT ticker retrieved successfully",
                         {
                             "symbol": ticker['symbol'],
                             "price": f"${ticker['last']:,.2f}",
                             "bid": f"${ticker['bid']:,.2f}",
                             "ask": f"${ticker['ask']:,.2f}",
                             "spread": f"${ticker['ask'] - ticker['bid']:.2f}"
                         })
            return True
            
        except Exception as e:
            self.log_test("Public Market Data", False, f"Error: {str(e)}")
            return False
    
    def test_order_book_access(self, exchange):
        """Test order book data access"""
        try:
            # Fetch order book (public endpoint)
            order_book = exchange.fetch_order_book('BTC/USDT', limit=5)
            
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                self.log_test("Order Book Access", False, "Empty order book")
                return False
            
            best_bid = bids[0][0] if bids else 0
            best_ask = asks[0][0] if asks else 0
            spread = best_ask - best_bid
            
            self.log_test("Order Book Access", True,
                         "Order book retrieved successfully",
                         {
                             "best_bid": f"${best_bid:,.2f}",
                             "best_ask": f"${best_ask:,.2f}",
                             "spread": f"${spread:.2f}",
                             "bid_levels": len(bids),
                             "ask_levels": len(asks)
                         })
            return True
            
        except Exception as e:
            self.log_test("Order Book Access", False, f"Error: {str(e)}")
            return False
    
    def test_trading_symbols(self, exchange):
        """Test trading symbols availability"""
        try:
            # Load markets (public endpoint)
            markets = exchange.load_markets()
            
            # Filter for active USDT pairs
            usdt_pairs = [symbol for symbol, market in markets.items() 
                         if market['quote'] == 'USDT' and market['active']]
            
            major_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT']
            available_major_pairs = [pair for pair in major_pairs if pair in usdt_pairs]
            
            self.log_test("Trading Symbols", True,
                         "Markets loaded successfully",
                         {
                             "total_markets": len(markets),
                             "usdt_pairs": len(usdt_pairs),
                             "major_pairs_available": len(available_major_pairs),
                             "sample_pairs": available_major_pairs[:5]
                         })
            return True
            
        except Exception as e:
            self.log_test("Trading Symbols", False, f"Error: {str(e)}")
            return False
    
    def test_credentials_configuration(self):
        """Test credentials configuration (without API calls)"""
        try:
            # Check if credentials are configured
            api_key = os.getenv('OKX_API_KEY')
            secret_key = os.getenv('OKX_SECRET_KEY')
            passphrase = os.getenv('OKX_PASSPHRASE')
            sandbox = os.getenv('OKX_SANDBOX', 'true').lower() == 'true'
            
            credentials_configured = bool(api_key and secret_key and passphrase)
            
            self.log_test("Credentials Configuration", True,
                         "Credentials configuration checked",
                         {
                             "api_key_configured": bool(api_key),
                             "secret_key_configured": bool(secret_key),
                             "passphrase_configured": bool(passphrase),
                             "sandbox_mode": sandbox,
                             "ready_for_auth": credentials_configured
                         })
            return credentials_configured
            
        except Exception as e:
            self.log_test("Credentials Configuration", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all basic tests"""
        print("=" * 60)
        print("🚀 OKX BASIC FUNCTIONALITY TEST")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Initialize exchange
        exchange = self.test_okx_initialization()
        if not exchange:
            print("❌ Cannot proceed without exchange initialization")
            return False
        
        # Run tests
        tests = [
            lambda: self.test_public_market_data(exchange),
            lambda: self.test_order_book_access(exchange),
            lambda: self.test_trading_symbols(exchange),
            self.test_credentials_configuration
        ]
        
        passed = 1  # Exchange initialization already passed
        total = len(tests) + 1  # +1 for initialization
        
        for test in tests:
            if test():
                passed += 1
        
        # Print summary
        print("=" * 60)
        print("📊 BASIC TEST SUMMARY")
        print("=" * 60)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        print()
        
        if passed == total:
            print("🎉 ALL BASIC TESTS PASSED!")
            print("✅ OKX integration is functional")
            print("✅ Public market data access working")
            print("✅ Order book data accessible")
            print("✅ Trading symbols available")
            print("✅ Credentials properly configured")
            print()
            print("📝 Note: Authentication testing requires valid passphrase")
            print("   Current passphrase may need verification with OKX account")
        else:
            print("⚠️  Some basic tests failed")
            print("❌ Review OKX integration setup")
        
        print("=" * 60)
        return passed >= (total - 1)  # Allow auth failure but require other tests to pass

def main():
    """Main execution function"""
    tester = OKXBasicTester()
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
