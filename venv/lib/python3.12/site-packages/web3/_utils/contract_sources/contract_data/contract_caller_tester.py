"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/ContractCallerTester.sol:ContractCallerTester
CONTRACT_CALLER_TESTER_BYTECODE = "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"  # noqa: E501
CONTRACT_CALLER_TESTER_RUNTIME = "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"  # noqa: E501
CONTRACT_CALLER_TESTER_ABI = [
    {
        "inputs": [
            {"internalType": "int256", "name": "a", "type": "int256"},
            {"internalType": "int256", "name": "b", "type": "int256"},
        ],
        "name": "add",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "count",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "counter",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "increment",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "returnMeta",
        "outputs": [
            {"internalType": "address", "name": "", "type": "address"},
            {"internalType": "bytes", "name": "", "type": "bytes"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
        ],
        "stateMutability": "payable",
        "type": "function",
    },
]
CONTRACT_CALLER_TESTER_DATA = {
    "bytecode": CONTRACT_CALLER_TESTER_BYTECODE,
    "bytecode_runtime": CONTRACT_CALLER_TESTER_RUNTIME,
    "abi": CONTRACT_CALLER_TESTER_ABI,
}
