"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/PanicErrorsContract.sol:PanicErrorsContract
PANIC_ERRORS_CONTRACT_BYTECODE = "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"  # noqa: E501
PANIC_ERRORS_CONTRACT_RUNTIME = "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"  # noqa: E501
PANIC_ERRORS_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "emptyArray",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode01",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode11",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "zero", "type": "uint256"}],
        "name": "errorCode12",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "int256", "name": "negativeInt", "type": "int256"}],
        "name": "errorCode21",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode22",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode31",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode32",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode41",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode51",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "x",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "y",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
]
PANIC_ERRORS_CONTRACT_DATA = {
    "bytecode": PANIC_ERRORS_CONTRACT_BYTECODE,
    "bytecode_runtime": PANIC_ERRORS_CONTRACT_RUNTIME,
    "abi": PANIC_ERRORS_CONTRACT_ABI,
}
