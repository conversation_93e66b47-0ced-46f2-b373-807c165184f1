"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/ArraysContract.sol:ArraysContract
ARRAYS_CONTRACT_BYTECODE = "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"  # noqa: E501
ARRAYS_CONTRACT_RUNTIME = "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"  # noqa: E501
ARRAYS_CONTRACT_ABI = [
    {
        "inputs": [
            {"internalType": "bytes32[]", "name": "_bytes32Value", "type": "bytes32[]"},
            {"internalType": "bytes1[]", "name": "_byteValue", "type": "bytes1[]"},
        ],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "byteConstValue",
        "outputs": [{"internalType": "bytes1", "name": "", "type": "bytes1"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "byteValue",
        "outputs": [{"internalType": "bytes1", "name": "", "type": "bytes1"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "bytes32ConstValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "bytes32Value",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getByteConstValue",
        "outputs": [{"internalType": "bytes1[]", "name": "", "type": "bytes1[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getByteValue",
        "outputs": [{"internalType": "bytes1[]", "name": "", "type": "bytes1[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getBytes32ConstValue",
        "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getBytes32Value",
        "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes1[]", "name": "_byteValue", "type": "bytes1[]"}
        ],
        "name": "setByteValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32[]", "name": "_bytes32Value", "type": "bytes32[]"}
        ],
        "name": "setBytes32Value",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
ARRAYS_CONTRACT_DATA = {
    "bytecode": ARRAYS_CONTRACT_BYTECODE,
    "bytecode_runtime": ARRAYS_CONTRACT_RUNTIME,
    "abi": ARRAYS_CONTRACT_ABI,
}
