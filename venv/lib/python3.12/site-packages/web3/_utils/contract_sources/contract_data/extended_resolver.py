"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/ExtendedResolver.sol:ExtendedResolver
EXTENDED_RESOLVER_BYTECODE = "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"  # noqa: E501
EXTENDED_RESOLVER_RUNTIME = "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"  # noqa: E501
EXTENDED_RESOLVER_ABI = [
    {
        "inputs": [{"internalType": "contract ENS", "name": "_ens", "type": "address"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "bytes32",
                "name": "node",
                "type": "bytes32",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "owner",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "target",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "bool",
                "name": "isAuthorised",
                "type": "bool",
            },
        ],
        "name": "AuthorisationChanged",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "", "type": "bytes32"},
            {"internalType": "address", "name": "", "type": "address"},
            {"internalType": "address", "name": "", "type": "address"},
        ],
        "name": "authorisations",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "dnsName", "type": "bytes"},
            {"internalType": "bytes", "name": "data", "type": "bytes"},
        ],
        "name": "resolve",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "node", "type": "bytes32"},
            {"internalType": "address", "name": "target", "type": "address"},
            {"internalType": "bool", "name": "isAuthorised", "type": "bool"},
        ],
        "name": "setAuthorisation",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "pure",
        "type": "function",
    },
]
EXTENDED_RESOLVER_DATA = {
    "bytecode": EXTENDED_RESOLVER_BYTECODE,
    "bytecode_runtime": EXTENDED_RESOLVER_RUNTIME,
    "abi": EXTENDED_RESOLVER_ABI,
}
