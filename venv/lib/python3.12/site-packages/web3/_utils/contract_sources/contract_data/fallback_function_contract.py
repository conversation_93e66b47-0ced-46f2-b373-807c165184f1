"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/FallbackFunctionContract.sol:FallbackFunctionContract  # noqa: E501
FALLBACK_FUNCTION_CONTRACT_BYTECODE = "0x60806040525f5f8190555060b78060155f395ff3fe6080604052348015600e575f5ffd5b50600436106029575f3560e01c80633bc5de3014603257602a565b5b60015f819055005b6038604c565b60405160439190606a565b60405180910390f35b5f5f54905090565b5f819050919050565b6064816054565b82525050565b5f602082019050607b5f830184605d565b9291505056fea264697066735822122046c6693c62c80acbe96048ed262cd69dc31f380a491d7c6dcd21dab372b0f49f64736f6c634300081e0033"  # noqa: E501
FALLBACK_FUNCTION_CONTRACT_RUNTIME = "0x6080604052348015600e575f5ffd5b50600436106029575f3560e01c80633bc5de3014603257602a565b5b60015f819055005b6038604c565b60405160439190606a565b60405180910390f35b5f5f54905090565b5f819050919050565b6064816054565b82525050565b5f602082019050607b5f830184605d565b9291505056fea264697066735822122046c6693c62c80acbe96048ed262cd69dc31f380a491d7c6dcd21dab372b0f49f64736f6c634300081e0033"  # noqa: E501
FALLBACK_FUNCTION_CONTRACT_ABI = [
    {"inputs": [], "stateMutability": "payable", "type": "constructor"},
    {"stateMutability": "nonpayable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getData",
        "outputs": [{"internalType": "uint256", "name": "r", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
]
FALLBACK_FUNCTION_CONTRACT_DATA = {
    "bytecode": FALLBACK_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": FALLBACK_FUNCTION_CONTRACT_RUNTIME,
    "abi": FALLBACK_FUNCTION_CONTRACT_ABI,
}
