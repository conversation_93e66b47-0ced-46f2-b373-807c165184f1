"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/TupleContracts.sol:TupleContract
TUPLE_CONTRACT_BYTECODE = "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"  # noqa: E501
TUPLE_CONTRACT_RUNTIME = "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"  # noqa: E501
TUPLE_CONTRACT_ABI = [
    {
        "inputs": [
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256[]", "name": "b", "type": "uint256[]"},
                    {
                        "components": [
                            {"internalType": "int256", "name": "x", "type": "int256"},
                            {"internalType": "bool[2]", "name": "y", "type": "bool[2]"},
                            {
                                "internalType": "address[]",
                                "name": "z",
                                "type": "address[]",
                            },
                        ],
                        "internalType": "struct TupleContract.T[]",
                        "name": "c",
                        "type": "tuple[]",
                    },
                ],
                "internalType": "struct TupleContract.S",
                "name": "s",
                "type": "tuple",
            }
        ],
        "name": "method",
        "outputs": [
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256[]", "name": "b", "type": "uint256[]"},
                    {
                        "components": [
                            {"internalType": "int256", "name": "x", "type": "int256"},
                            {"internalType": "bool[2]", "name": "y", "type": "bool[2]"},
                            {
                                "internalType": "address[]",
                                "name": "z",
                                "type": "address[]",
                            },
                        ],
                        "internalType": "struct TupleContract.T[]",
                        "name": "c",
                        "type": "tuple[]",
                    },
                ],
                "internalType": "struct TupleContract.S",
                "name": "",
                "type": "tuple",
            }
        ],
        "stateMutability": "pure",
        "type": "function",
    }
]
TUPLE_CONTRACT_DATA = {
    "bytecode": TUPLE_CONTRACT_BYTECODE,
    "bytecode_runtime": TUPLE_CONTRACT_RUNTIME,
    "abi": TUPLE_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/TupleContracts.sol:NestedTupleContract
NESTED_TUPLE_CONTRACT_BYTECODE = "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"  # noqa: E501
NESTED_TUPLE_CONTRACT_RUNTIME = "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"  # noqa: E501
NESTED_TUPLE_CONTRACT_ABI = [
    {
        "inputs": [
            {
                "components": [
                    {
                        "components": [
                            {
                                "components": [
                                    {
                                        "internalType": "int256",
                                        "name": "x",
                                        "type": "int256",
                                    },
                                    {
                                        "internalType": "int256",
                                        "name": "y",
                                        "type": "int256",
                                    },
                                ],
                                "internalType": "struct NestedTupleContract.U[]",
                                "name": "u",
                                "type": "tuple[]",
                            }
                        ],
                        "internalType": "struct NestedTupleContract.T[]",
                        "name": "t",
                        "type": "tuple[]",
                    }
                ],
                "internalType": "struct NestedTupleContract.S",
                "name": "s",
                "type": "tuple",
            }
        ],
        "name": "method",
        "outputs": [
            {
                "components": [
                    {
                        "components": [
                            {
                                "components": [
                                    {
                                        "internalType": "int256",
                                        "name": "x",
                                        "type": "int256",
                                    },
                                    {
                                        "internalType": "int256",
                                        "name": "y",
                                        "type": "int256",
                                    },
                                ],
                                "internalType": "struct NestedTupleContract.U[]",
                                "name": "u",
                                "type": "tuple[]",
                            }
                        ],
                        "internalType": "struct NestedTupleContract.T[]",
                        "name": "t",
                        "type": "tuple[]",
                    }
                ],
                "internalType": "struct NestedTupleContract.S",
                "name": "",
                "type": "tuple",
            }
        ],
        "stateMutability": "pure",
        "type": "function",
    }
]
NESTED_TUPLE_CONTRACT_DATA = {
    "bytecode": NESTED_TUPLE_CONTRACT_BYTECODE,
    "bytecode_runtime": NESTED_TUPLE_CONTRACT_RUNTIME,
    "abi": NESTED_TUPLE_CONTRACT_ABI,
}
