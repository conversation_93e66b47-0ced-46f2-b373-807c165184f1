# Multi-Agent Crypto Trading System

A comprehensive multi-agent AI system for cryptocurrency trading built with Microsoft AutoGen, featuring 16 specialized agents across 5 distinct layers with Supabase database integration.

## 🏗️ System Architecture

### 5-Layer Architecture

1. **Intelligence Collection Layer** (4 agents)
   - WebIntelligenceAgent: Social media and web intelligence
   - WhaleActivityAgent: Large transaction monitoring
   - TVLMonitoringAgent: DeFi protocol TVL tracking
   - MemeTrendAgent: Meme coin trend analysis

2. **Market Analysis Layer** (2 agents)
   - TrendAggregationAgent: Multi-source trend analysis
   - ContractAnalysisAgent: Smart contract security analysis

3. **Strategy Generation & Backtesting Layer** (2 agents)
   - StrategyGenerationAgent: AI-powered strategy creation
   - BacktestingAgent: Historical performance validation

4. **Execution & Risk Management Layer** (5 agents)
   - StrategyDeploymentAgent: Strategy deployment management
   - TradeExecutionAgent: Order execution and management
   - PerformanceTrackingAgent: Real-time performance monitoring
   - RiskManagementAgent: Risk assessment and mitigation
   - MemoryManagementAgent: System memory and data management

5. **Control & Interaction Layer** (3 agents)
   - ParameterOptimizationAgent: Strategy parameter optimization
   - TelegramNotificationAgent: Telegram notifications
   - SlackNotificationAgent: Slack notifications

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- Supabase account and project
- API keys for AI models (Anthropic Claude, Google Gemini, OpenAI GPT-4)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd crypto-trading-system
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   export SUPABASE_ANON_KEY="your_supabase_anonymous_key"
   export ANTHROPIC_API_KEY="your_anthropic_api_key"
   export GOOGLE_API_KEY="your_google_api_key"
   export OPENAI_API_KEY="your_openai_api_key"
   
   # Optional: Notification services
   export TELEGRAM_BOT_TOKEN="your_telegram_bot_token"
   export SLACK_WEBHOOK_URL="your_slack_webhook_url"
   ```

4. **Initialize the database**
   ```bash
   python crypto_trading_system/scripts/init_database.py
   ```

5. **Test the system**
   ```bash
   python crypto_trading_system/scripts/test_system.py
   ```

6. **Run the system**
   ```bash
   # Development mode
   python crypto_trading_system/main.py development
   
   # Production mode
   python crypto_trading_system/main.py production
   ```

## 📊 Database Schema

The system uses Supabase (PostgreSQL) with 18 tables:

### Core System Tables
- `agents`: Agent registration and status
- `messages`: Inter-agent communication logs
- `system_config`: System configuration storage

### Intelligence Collection Tables
- `web_intelligence`: Social media and web data
- `whale_activity`: Large transaction tracking
- `tvl_data`: DeFi protocol TVL data
- `meme_trends`: Meme coin trend data

### Market Analysis Tables
- `trend_signals`: Market trend signals
- `contract_analysis`: Smart contract analysis results

### Strategy Tables
- `strategies`: Trading strategy definitions
- `backtest_results`: Strategy backtesting results

### Execution Tables
- `strategy_deployments`: Active strategy deployments
- `trade_executions`: Trade execution records
- `performance_data`: Performance metrics
- `risk_alerts`: Risk management alerts

### Control Tables
- `optimization_history`: Parameter optimization history
- `notifications`: System notifications
- `user_sessions`: User session management

## 🔧 Configuration

### Agent Configuration

Each agent can be configured in `crypto_trading_system/config/system_config.py`:

```python
"WebIntelligenceAgent": {
    "class": "WebIntelligenceAgent",
    "model": "default",
    "config": {
        "platforms": ["twitter", "reddit", "discord"],
        "update_interval": 300,  # 5 minutes
        "sentiment_analysis": True
    }
}
```

### Model Configuration

Support for multiple AI models:

```python
"model_configs": {
    "default": {
        "provider": "anthropic",
        "model": "claude-3-5-sonnet-20241022",
        "temperature": 0.7
    },
    "gemini": {
        "provider": "google", 
        "model": "gemini-1.5-pro"
    }
}
```

## 🧪 Testing

### Database Initialization Test
```bash
python crypto_trading_system/scripts/init_database.py
```

### Comprehensive System Test
```bash
python crypto_trading_system/scripts/test_system.py
```

### Unit Tests
```bash
pytest tests/
```

## 📈 Features

### Real-time Intelligence Collection
- Social media sentiment analysis
- Whale transaction monitoring
- DeFi protocol TVL tracking
- Meme coin trend detection

### Advanced Market Analysis
- Multi-timeframe trend analysis
- Smart contract security scoring
- Cross-platform signal aggregation

### AI-Powered Strategy Generation
- Automated strategy creation
- Historical backtesting
- Performance optimization
- Risk-adjusted returns

### Professional Execution Engine
- Multi-exchange support
- Advanced order types
- Real-time risk management
- Performance tracking

### Comprehensive Monitoring
- Real-time notifications
- Performance dashboards
- Risk alerts
- System health monitoring

## 🔒 Security Features

- API key encryption
- Rate limiting
- Database connection pooling
- Secure message passing
- Row-level security (RLS)

## 📊 Performance Optimization

- Strategic database indexing
- Connection pooling
- Async/await patterns
- Efficient message queuing
- Memory management

## 🚨 Risk Management

- Position size limits
- Stop-loss automation
- Drawdown monitoring
- Portfolio risk assessment
- Real-time alerts

## 📱 Notifications

### Telegram Integration
- Trade alerts
- Performance updates
- Risk notifications
- System status

### Slack Integration
- Channel-based notifications
- Rich message formatting
- Alert prioritization

## 🔄 Data Flow

1. **Intelligence Collection**: Agents gather data from various sources
2. **Market Analysis**: Raw data is processed into actionable signals
3. **Strategy Generation**: AI creates and backtests trading strategies
4. **Execution**: Approved strategies are deployed and executed
5. **Monitoring**: Performance is tracked and risks are managed

## 🛠️ Development

### Project Structure
```
crypto_trading_system/
├── agents/              # Agent implementations
├── core/               # Core system components
├── config/             # Configuration files
├── scripts/            # Utility scripts
├── tests/              # Test suite
└── main.py            # Application entry point
```

### Adding New Agents

1. Create agent class inheriting from `BaseAgentWithDB`
2. Implement required message handlers
3. Add configuration to `system_config.py`
4. Register in database initialization

### Database Migrations

Database schema changes should be applied through Supabase dashboard or migration scripts.

## 📋 Requirements

See `requirements.txt` for complete dependency list.

### Core Dependencies
- `autogen-core`: Multi-agent framework
- `supabase`: Database integration
- `asyncio`: Asynchronous programming
- `pydantic`: Data validation

### AI Model Dependencies
- `anthropic`: Claude API
- `google-generativeai`: Gemini API
- `openai`: GPT-4 API

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the test scripts for examples

## 🔮 Roadmap

- [ ] Web dashboard interface
- [ ] Mobile app notifications
- [ ] Advanced ML models
- [ ] Multi-chain support expansion
- [ ] Social trading features
- [ ] Advanced analytics dashboard

## 🚀 Deployment

### Production Deployment

1. **Set production environment variables**
   ```bash
   export ENVIRONMENT=production
   export SUPABASE_ANON_KEY="your_production_supabase_key"
   # ... other production API keys
   ```

2. **Run production initialization**
   ```bash
   python crypto_trading_system/scripts/init_database.py
   ```

3. **Start the system**
   ```bash
   python crypto_trading_system/main.py production
   ```

### Docker Deployment (Optional)

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "crypto_trading_system/main.py", "production"]
```

---

**⚠️ Disclaimer**: This system is for educational and research purposes. Cryptocurrency trading involves significant risk. Always do your own research and never invest more than you can afford to lose.
