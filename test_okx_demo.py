#!/usr/bin/env python3
"""
OKX Demo Account Testing Script
Tests connection and basic functionality with OKX demo/sandbox environment
"""

import os
import sys
import ccxt
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# Load demo environment
load_dotenv('.env.demo')

class OKXDemoTester:
    def __init__(self):
        self.exchange = None
        self.test_results = []
        
    def log_test(self, test_name, success, message="", data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        result = {
            'timestamp': timestamp,
            'test': test_name,
            'status': status,
            'message': message,
            'data': data
        }
        
        self.test_results.append(result)
        print(f"[{timestamp}] {status} - {test_name}")
        if message:
            print(f"    {message}")
        if data and isinstance(data, dict):
            for key, value in data.items():
                print(f"    {key}: {value}")
        print()
    
    def setup_exchange(self):
        """Initialize OKX exchange connection"""
        try:
            # Get credentials from environment
            api_key = os.getenv('OKX_API_KEY')
            secret_key = os.getenv('OKX_SECRET_KEY')
            passphrase = os.getenv('OKX_PASSPHRASE', 'demo_passphrase')
            sandbox = os.getenv('OKX_SANDBOX', 'true').lower() == 'true'
            
            if not api_key or not secret_key:
                self.log_test("Exchange Setup", False, "Missing API credentials")
                return False
            
            # Initialize exchange
            self.exchange = ccxt.okx({
                'apiKey': api_key,
                'secret': secret_key,
                'password': passphrase,
                'sandbox': sandbox,
                'enableRateLimit': True,
                'timeout': 30000,
            })
            
            self.log_test("Exchange Setup", True, f"OKX initialized (sandbox: {sandbox})")
            return True
            
        except Exception as e:
            self.log_test("Exchange Setup", False, f"Error: {str(e)}")
            return False
    
    def test_connection(self):
        """Test basic connection to OKX"""
        try:
            # Test connection by fetching server time
            server_time = self.exchange.fetch_time()
            local_time = datetime.now().timestamp() * 1000
            time_diff = abs(server_time - local_time)
            
            self.log_test("Connection Test", True, 
                         f"Server time sync successful",
                         {"time_diff_ms": f"{time_diff:.0f}"})
            return True
            
        except Exception as e:
            self.log_test("Connection Test", False, f"Connection failed: {str(e)}")
            return False
    
    def test_market_data(self):
        """Test market data retrieval"""
        try:
            # Test fetching ticker data
            ticker = self.exchange.fetch_ticker('BTC/USDT')
            
            required_fields = ['symbol', 'last', 'bid', 'ask', 'volume']
            missing_fields = [field for field in required_fields if field not in ticker or ticker[field] is None]
            
            if missing_fields:
                self.log_test("Market Data Test", False, 
                             f"Missing fields: {missing_fields}")
                return False
            
            self.log_test("Market Data Test", True,
                         "BTC/USDT ticker retrieved successfully",
                         {
                             "price": f"${ticker['last']:,.2f}",
                             "volume": f"{ticker['volume']:,.2f}",
                             "spread": f"${ticker['ask'] - ticker['bid']:.2f}"
                         })
            return True
            
        except Exception as e:
            self.log_test("Market Data Test", False, f"Error: {str(e)}")
            return False
    
    def test_account_info(self):
        """Test account information retrieval"""
        try:
            # Fetch account balance
            balance = self.exchange.fetch_balance()
            
            # Check if we have demo funds
            total_balance = balance.get('total', {})
            usdt_balance = total_balance.get('USDT', 0)
            
            self.log_test("Account Info Test", True,
                         "Account balance retrieved",
                         {
                             "USDT_balance": f"${usdt_balance:,.2f}",
                             "total_currencies": len(total_balance)
                         })
            return True
            
        except Exception as e:
            self.log_test("Account Info Test", False, f"Error: {str(e)}")
            return False
    
    def test_order_book(self):
        """Test order book data"""
        try:
            # Fetch order book
            order_book = self.exchange.fetch_order_book('BTC/USDT', limit=10)
            
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                self.log_test("Order Book Test", False, "Empty order book")
                return False
            
            best_bid = bids[0][0] if bids else 0
            best_ask = asks[0][0] if asks else 0
            spread = best_ask - best_bid
            
            self.log_test("Order Book Test", True,
                         "Order book retrieved successfully",
                         {
                             "best_bid": f"${best_bid:,.2f}",
                             "best_ask": f"${best_ask:,.2f}",
                             "spread": f"${spread:.2f}",
                             "bid_levels": len(bids),
                             "ask_levels": len(asks)
                         })
            return True
            
        except Exception as e:
            self.log_test("Order Book Test", False, f"Error: {str(e)}")
            return False
    
    def test_trading_pairs(self):
        """Test available trading pairs"""
        try:
            # Load markets
            markets = self.exchange.load_markets()
            
            # Filter for active USDT pairs
            usdt_pairs = [symbol for symbol, market in markets.items() 
                         if market['quote'] == 'USDT' and market['active']]
            
            major_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT']
            available_major_pairs = [pair for pair in major_pairs if pair in usdt_pairs]
            
            self.log_test("Trading Pairs Test", True,
                         "Markets loaded successfully",
                         {
                             "total_markets": len(markets),
                             "usdt_pairs": len(usdt_pairs),
                             "major_pairs_available": len(available_major_pairs)
                         })
            return True
            
        except Exception as e:
            self.log_test("Trading Pairs Test", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all demo tests"""
        print("=" * 60)
        print("🚀 OKX DEMO ACCOUNT TESTING")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Run tests in sequence
        tests = [
            self.setup_exchange,
            self.test_connection,
            self.test_market_data,
            self.test_account_info,
            self.test_order_book,
            self.test_trading_pairs
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            else:
                # Stop on critical failures
                if test == self.setup_exchange:
                    break
        
        # Print summary
        print("=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        print()
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - OKX Demo Ready!")
            print("✅ System is ready for live trading integration")
        else:
            print("⚠️  Some tests failed - Review configuration")
            print("❌ System needs attention before live integration")
        
        print("=" * 60)
        return passed == total

def main():
    """Main execution function"""
    tester = OKXDemoTester()
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
