#!/usr/bin/env python3
"""
Crypto Trading System Integration Test
Tests core system functionality and integration readiness
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load demo environment
load_dotenv('.env.demo')

class SystemIntegrationTester:
    def __init__(self):
        self.test_results = []
        
    def log_test(self, test_name, success, message="", data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        result = {
            'timestamp': timestamp,
            'test': test_name,
            'status': status,
            'message': message,
            'data': data
        }
        
        self.test_results.append(result)
        print(f"[{timestamp}] {status} - {test_name}")
        if message:
            print(f"    {message}")
        if data and isinstance(data, dict):
            for key, value in data.items():
                print(f"    {key}: {value}")
        print()
    
    def test_environment_setup(self):
        """Test environment configuration"""
        try:
            # Check critical environment variables
            required_vars = ['ENVIRONMENT', 'DEBUG', 'LOG_LEVEL']
            missing_vars = [var for var in required_vars if not os.getenv(var)]
            
            if missing_vars:
                self.log_test("Environment Setup", False, 
                             f"Missing variables: {missing_vars}")
                return False
            
            env_data = {
                "environment": os.getenv('ENVIRONMENT'),
                "debug_mode": os.getenv('DEBUG'),
                "log_level": os.getenv('LOG_LEVEL'),
                "trading_mode": os.getenv('TRADING_MODE', 'demo')
            }
            
            self.log_test("Environment Setup", True,
                         "Environment variables loaded successfully",
                         env_data)
            return True
            
        except Exception as e:
            self.log_test("Environment Setup", False, f"Error: {str(e)}")
            return False
    
    def test_core_dependencies(self):
        """Test core dependencies"""
        try:
            # Test critical imports
            import pandas as pd
            import numpy as np
            import ccxt
            import pytest
            import asyncio
            
            self.log_test("Core Dependencies", True,
                         "All core dependencies available",
                         {
                             "pandas_version": pd.__version__,
                             "numpy_version": np.__version__,
                             "ccxt_version": ccxt.__version__,
                             "python_version": sys.version.split()[0]
                         })
            return True
            
        except ImportError as e:
            self.log_test("Core Dependencies", False, f"Import error: {str(e)}")
            return False
        except Exception as e:
            self.log_test("Core Dependencies", False, f"Error: {str(e)}")
            return False
    
    def test_message_system(self):
        """Test message system"""
        try:
            from crypto_trading_system.core.messages import (
                BaseMessage, TradeSignal, TrendSignal, 
                TelegramNotification, PerformanceUpdate
            )
            
            # Create test messages
            trade_signal = TradeSignal(
                timestamp=datetime.now(),
                source_agent="test_agent",
                message_id="test_001",
                strategy_id="test_strategy",
                symbol="BTC/USDT",
                action="buy",
                quantity=0.1,
                price=50000.0,
                order_type="market",
                urgency="medium"
            )
            
            trend_signal = TrendSignal(
                timestamp=datetime.now(),
                source_agent="trend_agent",
                message_id="test_002",
                signal_type="bullish",
                strength=0.8,
                timeframe="short",
                supporting_data=["whale_activity", "social_sentiment"],
                confidence_level=0.85,
                target_assets=["BTC", "ETH"]
            )
            
            # Verify message properties
            if trade_signal.symbol != "BTC/USDT" or trade_signal.action != "buy":
                self.log_test("Message System", False, "Message properties not set correctly")
                return False
            
            self.log_test("Message System", True,
                         "Message system working correctly",
                         {
                             "trade_signal_symbol": trade_signal.symbol,
                             "trade_signal_action": trade_signal.action,
                             "trend_signal_type": trend_signal.signal_type,
                             "trend_signal_strength": trend_signal.strength
                         })
            return True
            
        except Exception as e:
            self.log_test("Message System", False, f"Error: {str(e)}")
            return False
    
    def test_agent_modules(self):
        """Test agent module imports"""
        try:
            # Test agent imports
            from crypto_trading_system.agents.intelligence import web_intelligence_agent
            from crypto_trading_system.agents.analysis import trend_aggregation_agent
            from crypto_trading_system.agents.execution import performance_analysis_agent
            
            agent_modules = [
                ("web_intelligence_agent", web_intelligence_agent),
                ("trend_aggregation_agent", trend_aggregation_agent),
                ("performance_analysis_agent", performance_analysis_agent)
            ]
            
            available_modules = []
            for name, module in agent_modules:
                if hasattr(module, '__file__'):
                    available_modules.append(name)
            
            self.log_test("Agent Modules", True,
                         "Agent modules accessible",
                         {
                             "available_modules": len(available_modules),
                             "modules": available_modules
                         })
            return True
            
        except Exception as e:
            self.log_test("Agent Modules", False, f"Error: {str(e)}")
            return False
    
    def test_market_data_processing(self):
        """Test market data processing functions"""
        try:
            import pandas as pd
            import numpy as np
            
            # Create sample market data
            dates = pd.date_range(start='2024-01-01', periods=100, freq='1h')
            prices = np.random.normal(50000, 1000, 100)
            volumes = np.random.normal(1000, 100, 100)
            
            df = pd.DataFrame({
                'timestamp': dates,
                'price': prices,
                'volume': volumes
            })
            
            # Test basic calculations
            sma_20 = df['price'].rolling(window=20).mean()
            rsi = self.calculate_rsi(df['price'])
            volatility = df['price'].pct_change().std() * np.sqrt(24)  # 24h volatility
            
            if len(sma_20.dropna()) == 0:
                self.log_test("Market Data Processing", False, "SMA calculation failed")
                return False
            
            self.log_test("Market Data Processing", True,
                         "Market data processing working",
                         {
                             "data_points": len(df),
                             "sma_values": len(sma_20.dropna()),
                             "rsi_values": len(rsi.dropna()),
                             "avg_price": f"${df['price'].mean():.2f}",
                             "volatility": f"{volatility:.2%}"
                         })
            return True
            
        except Exception as e:
            self.log_test("Market Data Processing", False, f"Error: {str(e)}")
            return False
    
    def test_exchange_connectivity(self):
        """Test exchange connectivity (demo mode)"""
        try:
            import ccxt
            
            # Test basic exchange initialization (without credentials)
            exchanges = ['binance', 'okx', 'coinbase']
            available_exchanges = []
            
            for exchange_name in exchanges:
                try:
                    exchange_class = getattr(ccxt, exchange_name)
                    exchange = exchange_class({
                        'sandbox': True,
                        'enableRateLimit': True,
                    })
                    available_exchanges.append(exchange_name)
                except Exception:
                    pass
            
            if not available_exchanges:
                self.log_test("Exchange Connectivity", False, "No exchanges available")
                return False
            
            self.log_test("Exchange Connectivity", True,
                         "Exchange classes accessible",
                         {
                             "available_exchanges": len(available_exchanges),
                             "exchanges": available_exchanges
                         })
            return True
            
        except Exception as e:
            self.log_test("Exchange Connectivity", False, f"Error: {str(e)}")
            return False
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def run_all_tests(self):
        """Run all integration tests"""
        print("=" * 60)
        print("🚀 CRYPTO TRADING SYSTEM INTEGRATION TEST")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Run tests in sequence
        tests = [
            self.test_environment_setup,
            self.test_core_dependencies,
            self.test_message_system,
            self.test_agent_modules,
            self.test_market_data_processing,
            self.test_exchange_connectivity
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Print summary
        print("=" * 60)
        print("📊 INTEGRATION TEST SUMMARY")
        print("=" * 60)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        print()
        
        if passed == total:
            print("🎉 ALL INTEGRATION TESTS PASSED!")
            print("✅ System is ready for live testing")
            print("✅ Core components are functional")
            print("✅ Message system is working")
            print("✅ Agent modules are accessible")
        else:
            print("⚠️  Some integration tests failed")
            print("❌ System needs attention before live testing")
        
        print("=" * 60)
        return passed == total

def main():
    """Main execution function"""
    tester = SystemIntegrationTester()
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
