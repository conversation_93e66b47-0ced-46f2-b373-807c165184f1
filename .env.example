# Multi-Agent Crypto Trading System Environment Configuration
# Copy this file to .env and fill in your actual API keys

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
# Options: development, production

# =============================================================================
# SUPABASE DATABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://bvqfowfiluzywduolpxh.supabase.co
SUPABASE_ANON_KEY=your_supabase_anonymous_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# =============================================================================
# AI MODEL API KEYS
# =============================================================================

# Anthropic Claude API (REQUIRED)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini API (OPTIONAL - for additional model diversity)
GOOGLE_API_KEY=your_google_api_key_here

# OpenAI GPT-4 API (OPTIONAL - for additional model diversity)
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# EXCHANGE API KEYS (FOR LIVE TRADING)
# =============================================================================

# Binance API (for crypto trading)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true
# Set to false for live trading, true for testnet

# Interactive Brokers (for traditional assets)
IBKR_USERNAME=your_ibkr_username_here
IBKR_PASSWORD=your_ibkr_password_here
IBKR_ACCOUNT=your_ibkr_account_number_here
IBKR_GATEWAY_PORT=4001
# Default TWS API port

# =============================================================================
# BLOCKCHAIN DATA PROVIDERS
# =============================================================================

# Ethereum/DeFi Data
ALCHEMY_API_KEY=your_alchemy_api_key_here
INFURA_PROJECT_ID=your_infura_project_id_here
MORALIS_API_KEY=your_moralis_api_key_here

# Multi-chain Data
COVALENT_API_KEY=your_covalent_api_key_here
DUNE_API_KEY=your_dune_analytics_api_key_here

# =============================================================================
# SOCIAL MEDIA & WEB INTELLIGENCE
# =============================================================================

# Twitter/X API (for social sentiment)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here

# Reddit API (for community sentiment)
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=CryptoTradingBot/1.0

# Discord Bot (for community monitoring)
DISCORD_BOT_TOKEN=your_discord_bot_token_here

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Telegram Bot (REQUIRED for mobile control)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Slack Notifications (OPTIONAL)
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
SLACK_CHANNEL=#crypto-trading

# Email Notifications (OPTIONAL)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Logging Level
LOG_LEVEL=INFO
# Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# System Monitoring
MONITORING_INTERVAL=60
# Monitoring interval in seconds

# Risk Management
MAX_POSITION_SIZE=10000
# Maximum position size in USD
MAX_DAILY_LOSS=1000
# Maximum daily loss in USD
RISK_TOLERANCE=0.02
# Risk tolerance as decimal (2%)

# =============================================================================
# DEVELOPMENT/TESTING SETTINGS
# =============================================================================

# Test Mode (disables live trading)
TEST_MODE=true

# Database Connection Pool
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Rate Limiting
API_RATE_LIMIT=100
# Requests per minute

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Encryption Key (generate with: python -c "import secrets; print(secrets.token_hex(32))")
ENCRYPTION_KEY=your_32_byte_hex_encryption_key_here

# JWT Secret (for API authentication)
JWT_SECRET=your_jwt_secret_key_here

# API Access Control
ALLOWED_IPS=127.0.0.1,localhost
# Comma-separated list of allowed IP addresses
