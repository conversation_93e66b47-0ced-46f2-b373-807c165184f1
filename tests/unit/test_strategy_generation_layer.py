"""
Unit tests for Strategy Generation & Backtesting Layer agents.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from crypto_trading_system.agents.strategy_generation.signal_generator import SignalGeneratorAgent
from crypto_trading_system.agents.strategy_generation.backtesting_engine import BacktestingEngineAgent
from crypto_trading_system.agents.strategy_generation.strategy_optimizer import StrategyOptimizerAgent
from crypto_trading_system.agents.strategy_generation.risk_calculator import RiskCalculatorAgent


@pytest.mark.unit
class TestSignalGeneratorAgent:
    """Test cases for SignalGeneratorAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_ai_clients, mock_supabase_client):
        """Create SignalGeneratorAgent instance for testing."""
        agent = SignalGeneratorAgent(
            runtime=mock_runtime,
            ai_clients=mock_ai_clients,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_generate_buy_signal(self, agent, sample_market_data):
        """Test buy signal generation."""
        # Setup market conditions favoring buy signal
        market_analysis = {
            'rsi': 25,  # Oversold
            'macd_signal': 'bullish_crossover',
            'trend': 'upward',
            'support_level': 48000,
            'current_price': 48500
        }
        
        # Mock AI response for buy signal
        agent.ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "BUY", "confidence": 0.85, "reasoning": "Oversold RSI with bullish MACD crossover"}')])
        )
        
        # Generate signal
        result = await agent.generate_trading_signals(market_analysis)
        
        assert result is not None
        assert 'signals' in result
        if result['signals']:
            signal = result['signals'][0]
            assert signal['action'] == 'BUY'
            assert 0 <= signal['confidence'] <= 1
            assert 'reasoning' in signal
    
    @pytest.mark.asyncio
    async def test_generate_sell_signal(self, agent):
        """Test sell signal generation."""
        # Setup market conditions favoring sell signal
        market_analysis = {
            'rsi': 80,  # Overbought
            'macd_signal': 'bearish_crossover',
            'trend': 'downward',
            'resistance_level': 52000,
            'current_price': 51500
        }
        
        # Mock AI response for sell signal
        agent.ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "SELL", "confidence": 0.78, "reasoning": "Overbought RSI with bearish MACD crossover"}')])
        )
        
        # Generate signal
        result = await agent.generate_trading_signals(market_analysis)
        
        assert result is not None
        if result['signals']:
            signal = result['signals'][0]
            assert signal['action'] == 'SELL'
            assert signal['confidence'] > 0
    
    @pytest.mark.asyncio
    async def test_hold_signal(self, agent):
        """Test hold signal generation."""
        # Setup neutral market conditions
        market_analysis = {
            'rsi': 50,  # Neutral
            'macd_signal': 'neutral',
            'trend': 'sideways',
            'current_price': 50000
        }
        
        # Mock AI response for hold signal
        agent.ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "HOLD", "confidence": 0.60, "reasoning": "Neutral market conditions"}')])
        )
        
        # Generate signal
        result = await agent.generate_trading_signals(market_analysis)
        
        assert result is not None
        if result['signals']:
            signal = result['signals'][0]
            assert signal['action'] == 'HOLD'
    
    def test_signal_validation(self, agent):
        """Test signal validation logic."""
        # Test valid signal
        valid_signal = {
            'action': 'BUY',
            'confidence': 0.8,
            'symbol': 'BTC/USDT',
            'timestamp': datetime.now()
        }
        
        assert agent.validate_signal(valid_signal) is True
        
        # Test invalid signal - missing required fields
        invalid_signal = {
            'action': 'BUY',
            'confidence': 0.8
            # Missing symbol and timestamp
        }
        
        assert agent.validate_signal(invalid_signal) is False
        
        # Test invalid confidence range
        invalid_confidence = {
            'action': 'BUY',
            'confidence': 1.5,  # Invalid confidence > 1
            'symbol': 'BTC/USDT',
            'timestamp': datetime.now()
        }
        
        assert agent.validate_signal(invalid_confidence) is False


@pytest.mark.unit
class TestBacktestingEngineAgent:
    """Test cases for BacktestingEngineAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create BacktestingEngineAgent instance for testing."""
        agent = BacktestingEngineAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.fixture
    def sample_strategy(self):
        """Create sample trading strategy for testing."""
        return {
            'name': 'RSI_MACD_Strategy',
            'parameters': {
                'rsi_period': 14,
                'rsi_oversold': 30,
                'rsi_overbought': 70,
                'macd_fast': 12,
                'macd_slow': 26,
                'macd_signal': 9
            },
            'entry_conditions': ['rsi < 30', 'macd_crossover_bullish'],
            'exit_conditions': ['rsi > 70', 'macd_crossover_bearish']
        }
    
    @pytest.mark.asyncio
    async def test_run_backtest(self, agent, sample_strategy, sample_market_data):
        """Test backtest execution."""
        backtest_config = {
            'initial_balance': 10000,
            'commission': 0.001,
            'slippage': 0.0005,
            'start_date': '2024-01-01',
            'end_date': '2024-01-31'
        }
        
        # Run backtest
        result = await agent.run_backtest(
            strategy=sample_strategy,
            market_data=sample_market_data,
            config=backtest_config
        )
        
        # Verify backtest results
        assert result is not None
        assert 'total_return' in result
        assert 'sharpe_ratio' in result
        assert 'max_drawdown' in result
        assert 'total_trades' in result
        assert 'win_rate' in result
        assert 'profit_factor' in result
        
        # Verify metrics are reasonable
        assert isinstance(result['total_trades'], int)
        assert result['total_trades'] >= 0
        assert 0 <= result['win_rate'] <= 1
        assert result['max_drawdown'] <= 0  # Drawdown should be negative or zero
    
    def test_calculate_performance_metrics(self, agent):
        """Test performance metrics calculation."""
        # Sample trade results
        trades = [
            {'profit_loss': 100, 'return_pct': 0.02},
            {'profit_loss': -50, 'return_pct': -0.01},
            {'profit_loss': 200, 'return_pct': 0.04},
            {'profit_loss': -25, 'return_pct': -0.005},
            {'profit_loss': 150, 'return_pct': 0.03}
        ]
        
        metrics = agent.calculate_performance_metrics(trades, initial_balance=10000)
        
        assert 'total_return' in metrics
        assert 'win_rate' in metrics
        assert 'profit_factor' in metrics
        assert 'sharpe_ratio' in metrics
        
        # Verify calculations
        winning_trades = [t for t in trades if t['profit_loss'] > 0]
        expected_win_rate = len(winning_trades) / len(trades)
        assert abs(metrics['win_rate'] - expected_win_rate) < 0.01
    
    def test_calculate_drawdown(self, agent):
        """Test maximum drawdown calculation."""
        # Sample equity curve
        equity_curve = [10000, 10200, 9800, 9500, 9700, 10100, 9900, 10300]
        
        max_drawdown = agent.calculate_max_drawdown(equity_curve)
        
        # Maximum drawdown should be from peak (10200) to trough (9500)
        expected_drawdown = (9500 - 10200) / 10200
        assert abs(max_drawdown - expected_drawdown) < 0.01
    
    def test_calculate_sharpe_ratio(self, agent):
        """Test Sharpe ratio calculation."""
        returns = [0.02, -0.01, 0.04, -0.005, 0.03, 0.01, -0.02, 0.025]
        
        sharpe_ratio = agent.calculate_sharpe_ratio(returns, risk_free_rate=0.02)
        
        assert isinstance(sharpe_ratio, (int, float))
        # Sharpe ratio should be reasonable for this return series
        assert -5 <= sharpe_ratio <= 5


@pytest.mark.unit
class TestStrategyOptimizerAgent:
    """Test cases for StrategyOptimizerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create StrategyOptimizerAgent instance for testing."""
        agent = StrategyOptimizerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_optimize_parameters(self, agent, sample_market_data):
        """Test parameter optimization."""
        # Define parameter ranges
        param_ranges = {
            'rsi_period': [10, 14, 20],
            'rsi_oversold': [20, 30, 35],
            'rsi_overbought': [65, 70, 80]
        }
        
        # Mock backtest results for different parameter combinations
        def mock_backtest(strategy, market_data, config):
            # Return different results based on parameters
            rsi_period = strategy['parameters']['rsi_period']
            return {
                'total_return': 0.1 + (rsi_period - 10) * 0.01,  # Better performance with higher RSI period
                'sharpe_ratio': 1.0 + (rsi_period - 10) * 0.1,
                'max_drawdown': -0.1 - (rsi_period - 10) * 0.005,
                'total_trades': 50,
                'win_rate': 0.6
            }
        
        with patch.object(agent, 'run_single_backtest', side_effect=mock_backtest):
            # Run optimization
            result = await agent.optimize_parameters(
                base_strategy={'parameters': {}},
                param_ranges=param_ranges,
                market_data=sample_market_data,
                optimization_metric='sharpe_ratio'
            )
            
            # Verify optimization results
            assert result is not None
            assert 'best_parameters' in result
            assert 'best_performance' in result
            assert 'optimization_results' in result
            
            # Best parameters should be the ones with highest RSI period (20)
            assert result['best_parameters']['rsi_period'] == 20
    
    def test_parameter_grid_generation(self, agent):
        """Test parameter grid generation."""
        param_ranges = {
            'param1': [1, 2, 3],
            'param2': [0.1, 0.2],
            'param3': [True, False]
        }
        
        grid = agent.generate_parameter_grid(param_ranges)
        
        # Should generate all combinations: 3 * 2 * 2 = 12
        assert len(grid) == 12
        
        # Verify all combinations are present
        param1_values = set(combo['param1'] for combo in grid)
        assert param1_values == {1, 2, 3}
        
        param2_values = set(combo['param2'] for combo in grid)
        assert param2_values == {0.1, 0.2}
    
    def test_performance_ranking(self, agent):
        """Test performance ranking logic."""
        results = [
            {'parameters': {'a': 1}, 'sharpe_ratio': 1.5, 'total_return': 0.2},
            {'parameters': {'a': 2}, 'sharpe_ratio': 2.0, 'total_return': 0.15},
            {'parameters': {'a': 3}, 'sharpe_ratio': 1.2, 'total_return': 0.25}
        ]
        
        # Rank by Sharpe ratio
        ranked = agent.rank_results(results, metric='sharpe_ratio')
        assert ranked[0]['parameters']['a'] == 2  # Highest Sharpe ratio
        
        # Rank by total return
        ranked = agent.rank_results(results, metric='total_return')
        assert ranked[0]['parameters']['a'] == 3  # Highest total return


@pytest.mark.unit
class TestRiskCalculatorAgent:
    """Test cases for RiskCalculatorAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create RiskCalculatorAgent instance for testing."""
        agent = RiskCalculatorAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_calculate_position_size(self, agent):
        """Test position size calculation."""
        # Test with fixed risk amount
        position_size = agent.calculate_position_size(
            account_balance=10000,
            risk_per_trade=0.02,  # 2% risk
            entry_price=50000,
            stop_loss_price=48000
        )
        
        # Risk amount = 10000 * 0.02 = 200
        # Risk per unit = 50000 - 48000 = 2000
        # Position size = 200 / 2000 = 0.1
        expected_size = 0.1
        assert abs(position_size - expected_size) < 0.01
    
    def test_calculate_var(self, agent):
        """Test Value at Risk calculation."""
        # Sample returns
        returns = np.array([0.02, -0.01, 0.03, -0.02, 0.01, -0.015, 0.025, -0.005])
        
        # Calculate 95% VaR
        var_95 = agent.calculate_var(returns, confidence_level=0.95)
        
        assert var_95 < 0  # VaR should be negative (loss)
        assert isinstance(var_95, (int, float))
        
        # 99% VaR should be more extreme than 95% VaR
        var_99 = agent.calculate_var(returns, confidence_level=0.99)
        assert var_99 <= var_95
    
    def test_calculate_portfolio_risk(self, agent):
        """Test portfolio risk calculation."""
        positions = {
            'BTC/USDT': {'value': 5000, 'volatility': 0.04},
            'ETH/USDT': {'value': 3000, 'volatility': 0.05},
            'ADA/USDT': {'value': 2000, 'volatility': 0.06}
        }
        
        # Correlation matrix
        correlations = pd.DataFrame({
            'BTC/USDT': [1.0, 0.8, 0.6],
            'ETH/USDT': [0.8, 1.0, 0.7],
            'ADA/USDT': [0.6, 0.7, 1.0]
        }, index=['BTC/USDT', 'ETH/USDT', 'ADA/USDT'])
        
        portfolio_risk = agent.calculate_portfolio_risk(positions, correlations)
        
        assert portfolio_risk > 0
        assert isinstance(portfolio_risk, (int, float))
        
        # Portfolio risk should be less than sum of individual risks due to diversification
        individual_risks_sum = sum(pos['value'] * pos['volatility'] for pos in positions.values())
        assert portfolio_risk < individual_risks_sum
    
    def test_risk_metrics(self, agent):
        """Test various risk metrics calculation."""
        returns = np.array([0.02, -0.01, 0.03, -0.02, 0.01, -0.015, 0.025, -0.005])
        
        metrics = agent.calculate_risk_metrics(returns)
        
        assert 'volatility' in metrics
        assert 'var_95' in metrics
        assert 'var_99' in metrics
        assert 'cvar_95' in metrics
        assert 'max_drawdown' in metrics
        
        # Verify volatility calculation
        expected_volatility = np.std(returns)
        assert abs(metrics['volatility'] - expected_volatility) < 0.001
        
        # CVaR should be more extreme than VaR
        assert metrics['cvar_95'] <= metrics['var_95']
    
    @pytest.mark.asyncio
    async def test_risk_assessment(self, agent):
        """Test comprehensive risk assessment."""
        portfolio_data = {
            'total_value': 10000,
            'positions': {
                'BTC/USDT': {'value': 6000, 'entry_price': 50000, 'current_price': 52000},
                'ETH/USDT': {'value': 4000, 'entry_price': 3000, 'current_price': 3100}
            },
            'historical_returns': [0.02, -0.01, 0.03, -0.02, 0.01]
        }
        
        risk_assessment = await agent.assess_portfolio_risk(portfolio_data)
        
        assert risk_assessment is not None
        assert 'overall_risk_score' in risk_assessment
        assert 'risk_metrics' in risk_assessment
        assert 'recommendations' in risk_assessment
        
        # Risk score should be between 0 and 1
        assert 0 <= risk_assessment['overall_risk_score'] <= 1
