"""
Unit tests for Execution & Risk Management Layer agents.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np


@pytest.mark.unit
class TestExecutionLayerCore:
    """Test cases for Execution Layer core functionality."""

    def test_trade_execution_logic(self):
        """Test trade execution logic."""
        # Sample trade order
        trade_order = {
            'symbol': 'BTC/USDT',
            'side': 'buy',
            'amount': 0.1,
            'price': 50000,
            'order_type': 'limit',
            'timestamp': datetime.now()
        }

        # Validate trade order
        def validate_trade_order(order):
            required_fields = ['symbol', 'side', 'amount', 'price', 'order_type']
            return all(field in order for field in required_fields)

        assert validate_trade_order(trade_order) is True
        assert trade_order['side'] in ['buy', 'sell']
        assert trade_order['amount'] > 0
        assert trade_order['price'] > 0
    
    @pytest.mark.asyncio
    async def test_execute_buy_order(self, agent, mock_exchange_client):
        """Test buy order execution."""
        # Setup mock exchange response
        mock_exchange_client.create_market_buy_order.return_value = {
            'id': 'test_order_123',
            'symbol': 'BTC/USDT',
            'amount': 0.1,
            'price': 50000.0,
            'status': 'closed',
            'filled': 0.1,
            'timestamp': datetime.now().timestamp() * 1000,
            'fee': {'cost': 5.0, 'currency': 'USDT'}
        }
        
        # Execute buy order
        order_request = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 0.1,
            'order_type': 'market'
        }
        
        result = await agent.execute_order(order_request)
        
        # Verify order execution
        assert result is not None
        assert result['status'] == 'executed'
        assert result['order_id'] == 'test_order_123'
        assert result['filled_amount'] == 0.1
        assert result['execution_price'] == 50000.0
        
        # Verify exchange method was called
        mock_exchange_client.create_market_buy_order.assert_called_once_with('BTC/USDT', 0.1)
    
    @pytest.mark.asyncio
    async def test_execute_sell_order(self, agent, mock_exchange_client):
        """Test sell order execution."""
        # Setup mock exchange response
        mock_exchange_client.create_market_sell_order.return_value = {
            'id': 'test_sell_order_456',
            'symbol': 'BTC/USDT',
            'amount': 0.05,
            'price': 52000.0,
            'status': 'closed',
            'filled': 0.05,
            'timestamp': datetime.now().timestamp() * 1000,
            'fee': {'cost': 2.6, 'currency': 'USDT'}
        }
        
        # Execute sell order
        order_request = {
            'symbol': 'BTC/USDT',
            'action': 'SELL',
            'amount': 0.05,
            'order_type': 'market'
        }
        
        result = await agent.execute_order(order_request)
        
        # Verify order execution
        assert result is not None
        assert result['status'] == 'executed'
        assert result['order_id'] == 'test_sell_order_456'
        assert result['filled_amount'] == 0.05
        assert result['execution_price'] == 52000.0
        
        # Verify exchange method was called
        mock_exchange_client.create_market_sell_order.assert_called_once_with('BTC/USDT', 0.05)
    
    @pytest.mark.asyncio
    async def test_execute_limit_order(self, agent, mock_exchange_client):
        """Test limit order execution."""
        # Setup mock exchange response
        mock_exchange_client.create_limit_buy_order.return_value = {
            'id': 'test_limit_order_789',
            'symbol': 'BTC/USDT',
            'amount': 0.1,
            'price': 49000.0,
            'status': 'open',
            'filled': 0.0,
            'timestamp': datetime.now().timestamp() * 1000
        }
        
        # Execute limit order
        order_request = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 0.1,
            'order_type': 'limit',
            'price': 49000.0
        }
        
        result = await agent.execute_order(order_request)
        
        # Verify order placement
        assert result is not None
        assert result['status'] == 'pending'
        assert result['order_id'] == 'test_limit_order_789'
        
        # Verify exchange method was called
        mock_exchange_client.create_limit_buy_order.assert_called_once_with('BTC/USDT', 0.1, 49000.0)
    
    @pytest.mark.asyncio
    async def test_order_validation(self, agent):
        """Test order validation logic."""
        # Valid order
        valid_order = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 0.1,
            'order_type': 'market'
        }
        
        assert agent.validate_order(valid_order) is True
        
        # Invalid order - missing required fields
        invalid_order = {
            'symbol': 'BTC/USDT',
            'action': 'BUY'
            # Missing amount and order_type
        }
        
        assert agent.validate_order(invalid_order) is False
        
        # Invalid order - negative amount
        negative_amount_order = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': -0.1,
            'order_type': 'market'
        }
        
        assert agent.validate_order(negative_amount_order) is False
    
    @pytest.mark.asyncio
    async def test_order_error_handling(self, agent, mock_exchange_client):
        """Test error handling during order execution."""
        # Setup mock to raise exception
        mock_exchange_client.create_market_buy_order.side_effect = Exception("Insufficient balance")
        
        # Execute order that will fail
        order_request = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 10.0,  # Large amount to trigger error
            'order_type': 'market'
        }
        
        result = await agent.execute_order(order_request)
        
        # Verify error handling
        assert result is not None
        assert result['status'] == 'failed'
        assert 'error' in result
        assert 'Insufficient balance' in result['error']


@pytest.mark.unit
class TestPortfolioManagerAgent:
    """Test cases for PortfolioManagerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create PortfolioManagerAgent instance for testing."""
        agent = PortfolioManagerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_update_position_buy(self, agent):
        """Test position update after buy order."""
        # Initial portfolio state
        initial_portfolio = {
            'balance': 10000.0,
            'positions': {}
        }
        
        # Buy order execution
        buy_order = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 0.1,
            'price': 50000.0,
            'fee': 5.0
        }
        
        updated_portfolio = await agent.update_position(initial_portfolio, buy_order)
        
        # Verify portfolio update
        assert updated_portfolio['balance'] == 10000.0 - (0.1 * 50000.0) - 5.0  # 5000 - 5 = 4995
        assert 'BTC/USDT' in updated_portfolio['positions']
        
        position = updated_portfolio['positions']['BTC/USDT']
        assert position['amount'] == 0.1
        assert position['avg_price'] == 50000.0
        assert position['total_cost'] == 5005.0  # Including fee
    
    @pytest.mark.asyncio
    async def test_update_position_sell(self, agent):
        """Test position update after sell order."""
        # Initial portfolio with existing position
        initial_portfolio = {
            'balance': 5000.0,
            'positions': {
                'BTC/USDT': {
                    'amount': 0.1,
                    'avg_price': 50000.0,
                    'total_cost': 5000.0
                }
            }
        }
        
        # Sell order execution
        sell_order = {
            'symbol': 'BTC/USDT',
            'action': 'SELL',
            'amount': 0.05,
            'price': 52000.0,
            'fee': 2.6
        }
        
        updated_portfolio = await agent.update_position(initial_portfolio, sell_order)
        
        # Verify portfolio update
        expected_balance = 5000.0 + (0.05 * 52000.0) - 2.6  # 5000 + 2600 - 2.6 = 7597.4
        assert abs(updated_portfolio['balance'] - expected_balance) < 0.01
        
        # Verify remaining position
        position = updated_portfolio['positions']['BTC/USDT']
        assert position['amount'] == 0.05
        assert position['avg_price'] == 50000.0  # Avg price unchanged
    
    def test_calculate_portfolio_value(self, agent):
        """Test portfolio value calculation."""
        portfolio = {
            'balance': 2000.0,
            'positions': {
                'BTC/USDT': {'amount': 0.1, 'current_price': 52000.0},
                'ETH/USDT': {'amount': 1.0, 'current_price': 3100.0}
            }
        }
        
        total_value = agent.calculate_portfolio_value(portfolio)
        
        # Expected: 2000 + (0.1 * 52000) + (1.0 * 3100) = 2000 + 5200 + 3100 = 10300
        expected_value = 10300.0
        assert abs(total_value - expected_value) < 0.01
    
    def test_calculate_position_pnl(self, agent):
        """Test position P&L calculation."""
        position = {
            'amount': 0.1,
            'avg_price': 50000.0,
            'current_price': 52000.0
        }
        
        pnl = agent.calculate_position_pnl(position)
        
        # Expected P&L: 0.1 * (52000 - 50000) = 200
        expected_pnl = 200.0
        assert abs(pnl - expected_pnl) < 0.01
        
        # Test with loss
        losing_position = {
            'amount': 0.1,
            'avg_price': 50000.0,
            'current_price': 48000.0
        }
        
        loss_pnl = agent.calculate_position_pnl(losing_position)
        expected_loss = -200.0
        assert abs(loss_pnl - expected_loss) < 0.01
    
    @pytest.mark.asyncio
    async def test_rebalance_portfolio(self, agent):
        """Test portfolio rebalancing."""
        current_portfolio = {
            'balance': 1000.0,
            'positions': {
                'BTC/USDT': {'amount': 0.15, 'current_price': 50000.0, 'target_weight': 0.6},
                'ETH/USDT': {'amount': 2.0, 'current_price': 3000.0, 'target_weight': 0.4}
            }
        }
        
        # Total value: 1000 + (0.15 * 50000) + (2.0 * 3000) = 1000 + 7500 + 6000 = 14500
        # Target BTC value: 14500 * 0.6 = 8700
        # Target ETH value: 14500 * 0.4 = 5800
        
        rebalance_orders = await agent.calculate_rebalance_orders(current_portfolio)
        
        assert rebalance_orders is not None
        assert isinstance(rebalance_orders, list)
        
        # Should generate orders to adjust positions toward target weights
        if rebalance_orders:
            for order in rebalance_orders:
                assert 'symbol' in order
                assert 'action' in order
                assert 'amount' in order


@pytest.mark.unit
class TestRiskManagerAgent:
    """Test cases for RiskManagerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create RiskManagerAgent instance for testing."""
        agent = RiskManagerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_check_stop_loss(self, agent):
        """Test stop-loss checking."""
        position = {
            'symbol': 'BTC/USDT',
            'amount': 0.1,
            'entry_price': 50000.0,
            'stop_loss': 47500.0,
            'current_price': 47000.0  # Below stop-loss
        }
        
        should_trigger = await agent.check_stop_loss(position)
        
        assert should_trigger is True
        
        # Test position above stop-loss
        safe_position = position.copy()
        safe_position['current_price'] = 49000.0  # Above stop-loss
        
        should_not_trigger = await agent.check_stop_loss(safe_position)
        assert should_not_trigger is False
    
    @pytest.mark.asyncio
    async def test_check_take_profit(self, agent):
        """Test take-profit checking."""
        position = {
            'symbol': 'BTC/USDT',
            'amount': 0.1,
            'entry_price': 50000.0,
            'take_profit': 55000.0,
            'current_price': 56000.0  # Above take-profit
        }
        
        should_trigger = await agent.check_take_profit(position)
        
        assert should_trigger is True
        
        # Test position below take-profit
        safe_position = position.copy()
        safe_position['current_price'] = 54000.0  # Below take-profit
        
        should_not_trigger = await agent.check_take_profit(safe_position)
        assert should_not_trigger is False
    
    @pytest.mark.asyncio
    async def test_position_size_validation(self, agent):
        """Test position size validation."""
        portfolio = {
            'total_value': 10000.0,
            'positions': {
                'BTC/USDT': {'value': 3000.0},
                'ETH/USDT': {'value': 2000.0}
            }
        }
        
        # Test valid position size (within limits)
        new_position_value = 1000.0  # 10% of portfolio
        is_valid = await agent.validate_position_size(
            portfolio, 
            new_position_value, 
            max_position_size=0.2  # 20% limit
        )
        
        assert is_valid is True
        
        # Test invalid position size (exceeds limits)
        large_position_value = 3000.0  # 30% of portfolio
        is_invalid = await agent.validate_position_size(
            portfolio, 
            large_position_value, 
            max_position_size=0.2  # 20% limit
        )
        
        assert is_invalid is False
    
    @pytest.mark.asyncio
    async def test_portfolio_risk_check(self, agent):
        """Test overall portfolio risk assessment."""
        portfolio = {
            'total_value': 10000.0,
            'positions': {
                'BTC/USDT': {
                    'value': 6000.0,
                    'volatility': 0.04,
                    'correlation_to_market': 1.0
                },
                'ETH/USDT': {
                    'value': 3000.0,
                    'volatility': 0.05,
                    'correlation_to_market': 0.8
                }
            }
        }
        
        risk_assessment = await agent.assess_portfolio_risk(portfolio)
        
        assert risk_assessment is not None
        assert 'risk_score' in risk_assessment
        assert 'concentration_risk' in risk_assessment
        assert 'volatility_risk' in risk_assessment
        
        # Risk score should be between 0 and 1
        assert 0 <= risk_assessment['risk_score'] <= 1
        
        # High concentration in BTC should trigger concentration risk warning
        assert risk_assessment['concentration_risk'] > 0.5  # 60% in BTC is high concentration


@pytest.mark.unit
class TestPerformanceTrackerAgent:
    """Test cases for PerformanceTrackerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create PerformanceTrackerAgent instance for testing."""
        agent = PerformanceTrackerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_calculate_returns(self, agent):
        """Test return calculation."""
        portfolio_values = [10000, 10200, 9800, 10100, 10500]
        
        returns = agent.calculate_returns(portfolio_values)
        
        # Expected returns: [0.02, -0.0392, 0.0306, 0.0396]
        expected_returns = [0.02, -0.0392156862745098, 0.030612244897959183, 0.039603960396039604]
        
        assert len(returns) == len(portfolio_values) - 1
        for i, expected in enumerate(expected_returns):
            assert abs(returns[i] - expected) < 0.001
    
    def test_calculate_sharpe_ratio(self, agent):
        """Test Sharpe ratio calculation."""
        returns = [0.02, -0.01, 0.03, -0.005, 0.025, 0.01, -0.015, 0.02]
        
        sharpe_ratio = agent.calculate_sharpe_ratio(returns, risk_free_rate=0.02)
        
        assert isinstance(sharpe_ratio, (int, float))
        # Should be reasonable for this return series
        assert -5 <= sharpe_ratio <= 5
    
    def test_calculate_max_drawdown(self, agent):
        """Test maximum drawdown calculation."""
        portfolio_values = [10000, 10500, 9500, 9000, 9200, 10800, 10200]
        
        max_drawdown = agent.calculate_max_drawdown(portfolio_values)
        
        # Maximum drawdown from peak (10500) to trough (9000)
        expected_drawdown = (9000 - 10500) / 10500
        assert abs(max_drawdown - expected_drawdown) < 0.01
    
    @pytest.mark.asyncio
    async def test_generate_performance_report(self, agent):
        """Test performance report generation."""
        portfolio_history = [
            {'timestamp': datetime.now() - timedelta(days=i), 'value': 10000 + i * 100}
            for i in range(30, 0, -1)
        ]
        
        trades = [
            {'symbol': 'BTC/USDT', 'profit_loss': 200, 'return_pct': 0.02},
            {'symbol': 'ETH/USDT', 'profit_loss': -50, 'return_pct': -0.01},
            {'symbol': 'BTC/USDT', 'profit_loss': 300, 'return_pct': 0.03}
        ]
        
        report = await agent.generate_performance_report(portfolio_history, trades)
        
        assert report is not None
        assert 'total_return' in report
        assert 'sharpe_ratio' in report
        assert 'max_drawdown' in report
        assert 'win_rate' in report
        assert 'profit_factor' in report
        assert 'total_trades' in report
        
        # Verify calculations
        assert report['total_trades'] == len(trades)
        winning_trades = [t for t in trades if t['profit_loss'] > 0]
        expected_win_rate = len(winning_trades) / len(trades)
        assert abs(report['win_rate'] - expected_win_rate) < 0.01
