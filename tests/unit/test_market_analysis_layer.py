"""
Unit tests for Market Analysis Layer agents.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from crypto_trading_system.agents.market_analysis.technical_analyst import TechnicalAnalystAgent
from crypto_trading_system.agents.market_analysis.pattern_recognition import PatternRecognitionAgent
from crypto_trading_system.agents.market_analysis.market_regime_detector import MarketRegimeDetectorAgent
from crypto_trading_system.agents.market_analysis.correlation_analyzer import CorrelationAnalyzerAgent


@pytest.mark.unit
class TestTechnicalAnalystAgent:
    """Test cases for TechnicalAnalystAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create TechnicalAnalystAgent instance for testing."""
        agent = TechnicalAnalystAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_calculate_sma(self, agent, sample_market_data):
        """Test Simple Moving Average calculation."""
        prices = sample_market_data['close'].values
        sma_20 = agent.calculate_sma(prices, period=20)
        
        assert len(sma_20) == len(prices)
        assert not np.isnan(sma_20[-1])  # Last value should not be NaN
        assert sma_20[19] == np.mean(prices[:20])  # Check calculation
    
    def test_calculate_ema(self, agent, sample_market_data):
        """Test Exponential Moving Average calculation."""
        prices = sample_market_data['close'].values
        ema_12 = agent.calculate_ema(prices, period=12)
        
        assert len(ema_12) == len(prices)
        assert not np.isnan(ema_12[-1])
        # EMA should be more responsive than SMA
        sma_12 = agent.calculate_sma(prices, period=12)
        assert abs(ema_12[-1] - prices[-1]) <= abs(sma_12[-1] - prices[-1])
    
    def test_calculate_rsi(self, agent, sample_market_data):
        """Test Relative Strength Index calculation."""
        prices = sample_market_data['close'].values
        rsi = agent.calculate_rsi(prices, period=14)
        
        assert len(rsi) == len(prices)
        assert 0 <= rsi[-1] <= 100  # RSI should be between 0 and 100
        assert not np.isnan(rsi[-1])
    
    def test_calculate_macd(self, agent, sample_market_data):
        """Test MACD calculation."""
        prices = sample_market_data['close'].values
        macd_line, signal_line, histogram = agent.calculate_macd(prices)
        
        assert len(macd_line) == len(prices)
        assert len(signal_line) == len(prices)
        assert len(histogram) == len(prices)
        assert not np.isnan(macd_line[-1])
        assert not np.isnan(signal_line[-1])
    
    def test_calculate_bollinger_bands(self, agent, sample_market_data):
        """Test Bollinger Bands calculation."""
        prices = sample_market_data['close'].values
        upper, middle, lower = agent.calculate_bollinger_bands(prices, period=20, std_dev=2)
        
        assert len(upper) == len(prices)
        assert len(middle) == len(prices)
        assert len(lower) == len(prices)
        assert upper[-1] > middle[-1] > lower[-1]  # Proper band ordering
    
    @pytest.mark.asyncio
    async def test_generate_signals(self, agent, sample_market_data):
        """Test signal generation from technical indicators."""
        result = await agent.analyze_market_data(sample_market_data)
        
        assert result is not None
        assert 'signals' in result
        assert 'indicators' in result
        assert 'confidence' in result
        assert 0 <= result['confidence'] <= 1


@pytest.mark.unit
class TestPatternRecognitionAgent:
    """Test cases for PatternRecognitionAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create PatternRecognitionAgent instance for testing."""
        agent = PatternRecognitionAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_detect_support_resistance(self, agent, sample_market_data):
        """Test support and resistance level detection."""
        highs = sample_market_data['high'].values
        lows = sample_market_data['low'].values
        
        support_levels = agent.detect_support_levels(lows)
        resistance_levels = agent.detect_resistance_levels(highs)
        
        assert isinstance(support_levels, list)
        assert isinstance(resistance_levels, list)
        assert all(isinstance(level, (int, float)) for level in support_levels)
        assert all(isinstance(level, (int, float)) for level in resistance_levels)
    
    def test_detect_head_and_shoulders(self, agent):
        """Test head and shoulders pattern detection."""
        # Create synthetic head and shoulders pattern
        prices = np.array([100, 105, 110, 105, 115, 105, 110, 105, 100])
        
        pattern = agent.detect_head_and_shoulders(prices)
        
        assert isinstance(pattern, dict)
        assert 'pattern_found' in pattern
        assert 'confidence' in pattern
        if pattern['pattern_found']:
            assert 'left_shoulder' in pattern
            assert 'head' in pattern
            assert 'right_shoulder' in pattern
    
    def test_detect_double_top_bottom(self, agent):
        """Test double top/bottom pattern detection."""
        # Create synthetic double top pattern
        prices = np.array([100, 110, 105, 110, 100])
        
        pattern = agent.detect_double_top(prices)
        
        assert isinstance(pattern, dict)
        assert 'pattern_found' in pattern
        assert 'confidence' in pattern
    
    def test_detect_triangles(self, agent, sample_market_data):
        """Test triangle pattern detection."""
        highs = sample_market_data['high'].values[-50:]  # Use recent data
        lows = sample_market_data['low'].values[-50:]
        
        triangle = agent.detect_triangle_pattern(highs, lows)
        
        assert isinstance(triangle, dict)
        assert 'pattern_type' in triangle  # ascending, descending, symmetric
        assert 'confidence' in triangle
    
    @pytest.mark.asyncio
    async def test_pattern_analysis(self, agent, sample_market_data):
        """Test comprehensive pattern analysis."""
        result = await agent.analyze_patterns(sample_market_data)
        
        assert result is not None
        assert 'patterns' in result
        assert 'support_resistance' in result
        assert 'trend_analysis' in result


@pytest.mark.unit
class TestMarketRegimeDetectorAgent:
    """Test cases for MarketRegimeDetectorAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create MarketRegimeDetectorAgent instance for testing."""
        agent = MarketRegimeDetectorAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_calculate_volatility(self, agent, sample_market_data):
        """Test volatility calculation."""
        returns = sample_market_data['close'].pct_change().dropna()
        volatility = agent.calculate_volatility(returns, window=20)
        
        assert len(volatility) == len(returns)
        assert all(vol >= 0 for vol in volatility if not np.isnan(vol))
    
    def test_detect_trend_regime(self, agent, sample_market_data):
        """Test trend regime detection."""
        prices = sample_market_data['close'].values
        regime = agent.detect_trend_regime(prices)
        
        assert regime in ['bullish', 'bearish', 'sideways']
    
    def test_detect_volatility_regime(self, agent, sample_market_data):
        """Test volatility regime detection."""
        returns = sample_market_data['close'].pct_change().dropna()
        regime = agent.detect_volatility_regime(returns)
        
        assert regime in ['low_vol', 'normal_vol', 'high_vol']
    
    def test_regime_transition_detection(self, agent):
        """Test regime transition detection."""
        # Create synthetic data with regime change
        regime_history = ['bullish'] * 20 + ['bearish'] * 20
        
        transitions = agent.detect_regime_transitions(regime_history)
        
        assert isinstance(transitions, list)
        assert len(transitions) >= 1  # Should detect at least one transition
    
    @pytest.mark.asyncio
    async def test_market_regime_analysis(self, agent, sample_market_data):
        """Test comprehensive market regime analysis."""
        result = await agent.analyze_market_regime(sample_market_data)
        
        assert result is not None
        assert 'current_regime' in result
        assert 'trend_regime' in result
        assert 'volatility_regime' in result
        assert 'confidence' in result


@pytest.mark.unit
class TestCorrelationAnalyzerAgent:
    """Test cases for CorrelationAnalyzerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create CorrelationAnalyzerAgent instance for testing."""
        agent = CorrelationAnalyzerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_calculate_correlation_matrix(self, agent):
        """Test correlation matrix calculation."""
        # Create sample price data for multiple assets
        np.random.seed(42)
        data = {
            'BTC': np.random.randn(100).cumsum() + 50000,
            'ETH': np.random.randn(100).cumsum() + 3000,
            'ADA': np.random.randn(100).cumsum() + 1
        }
        df = pd.DataFrame(data)
        
        corr_matrix = agent.calculate_correlation_matrix(df)
        
        assert corr_matrix.shape == (3, 3)
        assert np.allclose(np.diag(corr_matrix), 1.0)  # Diagonal should be 1
        assert np.allclose(corr_matrix, corr_matrix.T)  # Should be symmetric
    
    def test_rolling_correlation(self, agent):
        """Test rolling correlation calculation."""
        np.random.seed(42)
        x = np.random.randn(100).cumsum()
        y = np.random.randn(100).cumsum()
        
        rolling_corr = agent.calculate_rolling_correlation(x, y, window=20)
        
        assert len(rolling_corr) == len(x)
        assert all(-1 <= corr <= 1 for corr in rolling_corr if not np.isnan(corr))
    
    def test_lead_lag_analysis(self, agent):
        """Test lead-lag relationship analysis."""
        # Create synthetic data with known lead-lag relationship
        np.random.seed(42)
        leader = np.random.randn(100).cumsum()
        follower = np.roll(leader, 3) + np.random.randn(100) * 0.1  # 3-period lag
        
        lead_lag = agent.analyze_lead_lag_relationship(leader, follower, max_lag=10)
        
        assert 'optimal_lag' in lead_lag
        assert 'correlation' in lead_lag
        assert isinstance(lead_lag['optimal_lag'], int)
    
    @pytest.mark.asyncio
    async def test_cross_asset_analysis(self, agent):
        """Test cross-asset correlation analysis."""
        # Create sample multi-asset data
        np.random.seed(42)
        market_data = {
            'BTC/USDT': pd.DataFrame({
                'close': np.random.randn(100).cumsum() + 50000,
                'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H')
            }),
            'ETH/USDT': pd.DataFrame({
                'close': np.random.randn(100).cumsum() + 3000,
                'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H')
            })
        }
        
        result = await agent.analyze_cross_asset_correlations(market_data)
        
        assert result is not None
        assert 'correlation_matrix' in result
        assert 'strongest_correlations' in result
        assert 'market_relationships' in result
