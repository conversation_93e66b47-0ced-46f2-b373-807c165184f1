"""
Unit tests for Market Analysis Layer core functionality.
"""
import pytest
import numpy as np
from datetime import datetime, timedelta


@pytest.mark.unit
class TestMarketAnalysisCore:
    """Test cases for Market Analysis core functionality."""

    def test_calculate_sma(self):
        """Test Simple Moving Average calculation."""
        def calculate_sma(prices, period):
            """Calculate Simple Moving Average."""
            if len(prices) < period:
                return np.full(len(prices), np.nan)

            sma = np.full(len(prices), np.nan)
            for i in range(period - 1, len(prices)):
                sma[i] = np.mean(prices[i - period + 1:i + 1])
            return sma

        prices = np.array([100, 102, 104, 103, 105, 107, 106, 108, 110, 109])
        sma_5 = calculate_sma(prices, period=5)

        assert len(sma_5) == len(prices)
        assert not np.isnan(sma_5[-1])  # Last value should not be NaN
        assert sma_5[4] == np.mean(prices[:5])  # Check calculation

    def test_calculate_rsi(self):
        """Test Relative Strength Index calculation."""
        def calculate_rsi(prices, period=14):
            """Calculate RSI."""
            if len(prices) < period + 1:
                return np.full(len(prices), 50.0)

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[:period])
            avg_loss = np.mean(losses[:period])

            if avg_loss == 0:
                return np.full(len(prices), 100.0)

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi

        prices = np.array([44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 47.25, 47.92])
        rsi = calculate_rsi(prices, period=5)

        assert 0 <= rsi <= 100  # RSI should be between 0 and 100
        assert isinstance(rsi, (int, float, np.number))

    def test_detect_support_resistance(self):
        """Test support and resistance level detection."""
        def detect_support_resistance(prices, window=5):
            """Detect support and resistance levels."""
            support_levels = []
            resistance_levels = []

            for i in range(window, len(prices) - window):
                # Check for local minima (support)
                if all(prices[i] <= prices[i-j] for j in range(1, window+1)) and \
                   all(prices[i] <= prices[i+j] for j in range(1, window+1)):
                    support_levels.append(prices[i])

                # Check for local maxima (resistance)
                if all(prices[i] >= prices[i-j] for j in range(1, window+1)) and \
                   all(prices[i] >= prices[i+j] for j in range(1, window+1)):
                    resistance_levels.append(prices[i])

            return {
                'support_levels': support_levels,
                'resistance_levels': resistance_levels,
                'confidence': 0.8 if support_levels or resistance_levels else 0.3
            }

        prices = np.array([100, 95, 90, 95, 100, 105, 110, 105, 100, 95, 100])
        result = detect_support_resistance(prices, window=2)

        assert 'support_levels' in result
        assert 'resistance_levels' in result
        assert isinstance(result['support_levels'], list)
        assert isinstance(result['resistance_levels'], list)
        assert 'confidence' in result
        assert 0 <= result['confidence'] <= 1

    def test_pattern_recognition_logic(self):
        """Test basic pattern recognition logic."""
        def detect_double_top(prices, tolerance=0.02):
            """Detect double top pattern."""
            if len(prices) < 5:
                return {'pattern_found': False, 'confidence': 0.0}

            # Find peaks
            peaks = []
            for i in range(1, len(prices) - 1):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    peaks.append((i, prices[i]))

            if len(peaks) < 2:
                return {'pattern_found': False, 'confidence': 0.0}

            # Check if last two peaks are similar height
            peak1_price = peaks[-2][1]
            peak2_price = peaks[-1][1]

            if abs(peak1_price - peak2_price) / peak1_price <= tolerance:
                return {'pattern_found': True, 'confidence': 0.75}

            return {'pattern_found': False, 'confidence': 0.2}

        # Create double top pattern
        prices = np.array([100, 110, 105, 108, 100, 95, 100])
        result = detect_double_top(prices)

        assert 'pattern_found' in result
        assert 'confidence' in result
        assert isinstance(result['pattern_found'], bool)
        assert 0 <= result['confidence'] <= 1

    def test_correlation_calculation(self):
        """Test correlation calculation between price series."""
        def calculate_correlation(series1, series2):
            """Calculate Pearson correlation coefficient."""
            if len(series1) != len(series2) or len(series1) < 2:
                return 0.0

            return np.corrcoef(series1, series2)[0, 1]

        # Perfectly correlated series
        series1 = np.array([1, 2, 3, 4, 5])
        series2 = np.array([2, 4, 6, 8, 10])

        correlation = calculate_correlation(series1, series2)

        assert abs(correlation - 1.0) < 0.01  # Should be close to 1
        assert -1 <= correlation <= 1  # Correlation should be between -1 and 1

    def test_volatility_calculation(self):
        """Test volatility calculation."""
        def calculate_volatility(prices, window=20):
            """Calculate rolling volatility."""
            if len(prices) < 2:
                return 0.0

            returns = np.diff(np.log(prices))
            if len(returns) < window:
                return np.std(returns)

            return np.std(returns[-window:])

        prices = np.array([100, 102, 98, 105, 95, 110, 90, 115, 85, 120])
        volatility = calculate_volatility(prices, window=5)

        assert volatility >= 0  # Volatility should be non-negative
        assert isinstance(volatility, (int, float, np.number))

    def test_market_regime_detection(self):
        """Test market regime detection logic."""
        def detect_market_regime(prices, lookback=20):
            """Detect current market regime."""
            if len(prices) < lookback:
                return {'regime': 'unknown', 'confidence': 0.0}

            recent_prices = prices[-lookback:]
            price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            volatility = np.std(np.diff(recent_prices) / recent_prices[:-1])

            if price_change > 0.1 and volatility < 0.05:
                return {'regime': 'bull', 'confidence': 0.8}
            elif price_change < -0.1 and volatility < 0.05:
                return {'regime': 'bear', 'confidence': 0.8}
            elif volatility > 0.1:
                return {'regime': 'volatile', 'confidence': 0.7}
            else:
                return {'regime': 'sideways', 'confidence': 0.6}

        # Bull market data
        bull_prices = np.array([100 + i * 2 for i in range(25)])  # Steady uptrend
        result = detect_market_regime(bull_prices)

        assert 'regime' in result
        assert result['regime'] in ['bull', 'bear', 'sideways', 'volatile', 'unknown']
        assert 'confidence' in result
        assert 0 <= result['confidence'] <= 1


