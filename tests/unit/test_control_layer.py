"""
Unit tests for Control & Interaction Layer agents.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import json

from crypto_trading_system.agents.control.system_coordinator import SystemCoordinatorAgent
from crypto_trading_system.agents.control.telegram_interface import TelegramInterfaceAgent
from crypto_trading_system.agents.control.database_manager import DatabaseManagerAgent


@pytest.mark.unit
class TestSystemCoordinatorAgent:
    """Test cases for SystemCoordinatorAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create SystemCoordinatorAgent instance for testing."""
        agent = SystemCoordinatorAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_coordinate_trading_cycle(self, agent):
        """Test trading cycle coordination."""
        # Mock agent responses
        mock_agents = {
            'market_data_collector': <PERSON><PERSON>(),
            'technical_analyst': <PERSON><PERSON>(),
            'signal_generator': <PERSON><PERSON>(),
            'trade_executor': <PERSON><PERSON>()
        }
        
        # Setup mock responses
        mock_agents['market_data_collector'].collect_data = AsyncMock(
            return_value={'BTC/USDT': {'price': 50000, 'volume': 1000}}
        )
        
        mock_agents['technical_analyst'].analyze = AsyncMock(
            return_value={'signals': [{'action': 'BUY', 'confidence': 0.8}]}
        )
        
        mock_agents['signal_generator'].generate_signals = AsyncMock(
            return_value={'signals': [{'symbol': 'BTC/USDT', 'action': 'BUY', 'amount': 0.1}]}
        )
        
        mock_agents['trade_executor'].execute_trades = AsyncMock(
            return_value={'executed_orders': [{'order_id': '123', 'status': 'filled'}]}
        )
        
        # Set mock agents
        agent.agents = mock_agents
        
        # Execute trading cycle
        result = await agent.coordinate_trading_cycle(['BTC/USDT'])
        
        # Verify coordination
        assert result is not None
        assert 'market_data' in result
        assert 'analysis' in result
        assert 'signals' in result
        assert 'execution_results' in result
        
        # Verify all agents were called
        mock_agents['market_data_collector'].collect_data.assert_called_once()
        mock_agents['technical_analyst'].analyze.assert_called_once()
        mock_agents['signal_generator'].generate_signals.assert_called_once()
        mock_agents['trade_executor'].execute_trades.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_handle_system_command(self, agent):
        """Test system command handling."""
        # Test start command
        start_command = {
            'command': 'start',
            'parameters': {'symbols': ['BTC/USDT', 'ETH/USDT']}
        }
        
        with patch.object(agent, 'start_trading_system') as mock_start:
            mock_start.return_value = {'status': 'started', 'symbols': ['BTC/USDT', 'ETH/USDT']}
            
            result = await agent.handle_system_command(start_command)
            
            assert result['status'] == 'started'
            mock_start.assert_called_once_with(['BTC/USDT', 'ETH/USDT'])
        
        # Test stop command
        stop_command = {'command': 'stop'}
        
        with patch.object(agent, 'stop_trading_system') as mock_stop:
            mock_stop.return_value = {'status': 'stopped'}
            
            result = await agent.handle_system_command(stop_command)
            
            assert result['status'] == 'stopped'
            mock_stop.assert_called_once()
        
        # Test status command
        status_command = {'command': 'status'}
        
        with patch.object(agent, 'get_system_status') as mock_status:
            mock_status.return_value = {
                'status': 'running',
                'active_symbols': ['BTC/USDT'],
                'uptime': '2 hours'
            }
            
            result = await agent.handle_system_command(status_command)
            
            assert result['status'] == 'running'
            assert 'active_symbols' in result
            mock_status.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, agent):
        """Test error handling in coordination."""
        # Mock agent that raises exception
        mock_agents = {
            'market_data_collector': Mock(),
            'technical_analyst': Mock()
        }
        
        # Setup failing agent
        mock_agents['market_data_collector'].collect_data = AsyncMock(
            side_effect=Exception("API Error")
        )
        
        agent.agents = mock_agents
        
        # Execute trading cycle with error
        result = await agent.coordinate_trading_cycle(['BTC/USDT'])
        
        # Verify error handling
        assert result is not None
        assert 'error' in result or result.get('status') == 'error'
    
    def test_validate_command(self, agent):
        """Test command validation."""
        # Valid command
        valid_command = {
            'command': 'start',
            'parameters': {'symbols': ['BTC/USDT']}
        }
        
        assert agent.validate_command(valid_command) is True
        
        # Invalid command - missing command field
        invalid_command = {
            'parameters': {'symbols': ['BTC/USDT']}
        }
        
        assert agent.validate_command(invalid_command) is False
        
        # Invalid command - unknown command
        unknown_command = {
            'command': 'unknown_command'
        }
        
        assert agent.validate_command(unknown_command) is False


@pytest.mark.unit
class TestTelegramInterfaceAgent:
    """Test cases for TelegramInterfaceAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create TelegramInterfaceAgent instance for testing."""
        with patch('crypto_trading_system.agents.control.telegram_interface.Bot') as mock_bot:
            mock_telegram_bot = Mock()
            mock_bot.return_value = mock_telegram_bot
            
            agent = TelegramInterfaceAgent(
                runtime=mock_runtime,
                supabase_client=mock_supabase_client,
                bot_token='test_token'
            )
            agent.bot = mock_telegram_bot
            
            return agent
    
    @pytest.mark.asyncio
    async def test_send_notification(self, agent):
        """Test sending notifications via Telegram."""
        # Setup mock bot
        agent.bot.send_message = AsyncMock(return_value=Mock(message_id=123))
        
        # Send notification
        message = "🚀 BTC/USDT Buy signal generated with 85% confidence"
        chat_id = "test_chat_id"
        
        result = await agent.send_notification(chat_id, message)
        
        # Verify message was sent
        assert result is not None
        assert result.message_id == 123
        agent.bot.send_message.assert_called_once_with(chat_id=chat_id, text=message)
    
    @pytest.mark.asyncio
    async def test_handle_start_command(self, agent):
        """Test /start command handling."""
        # Mock update object
        mock_update = Mock()
        mock_update.message.chat.id = "test_chat_id"
        mock_update.message.from_user.username = "test_user"
        
        # Mock context
        mock_context = Mock()
        mock_context.bot.send_message = AsyncMock()
        
        # Handle start command
        await agent.handle_start_command(mock_update, mock_context)
        
        # Verify welcome message was sent
        mock_context.bot.send_message.assert_called_once()
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == "test_chat_id"
        assert "Welcome" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_handle_status_command(self, agent):
        """Test /status command handling."""
        # Mock system status
        with patch.object(agent, 'get_system_status') as mock_status:
            mock_status.return_value = {
                'status': 'running',
                'active_symbols': ['BTC/USDT', 'ETH/USDT'],
                'total_trades': 15,
                'profit_loss': 250.50
            }
            
            # Mock update and context
            mock_update = Mock()
            mock_update.message.chat.id = "test_chat_id"
            
            mock_context = Mock()
            mock_context.bot.send_message = AsyncMock()
            
            # Handle status command
            await agent.handle_status_command(mock_update, mock_context)
            
            # Verify status message was sent
            mock_context.bot.send_message.assert_called_once()
            call_args = mock_context.bot.send_message.call_args
            assert "Status: running" in call_args[1]['text']
            assert "BTC/USDT" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_handle_portfolio_command(self, agent):
        """Test /portfolio command handling."""
        # Mock portfolio data
        with patch.object(agent, 'get_portfolio_summary') as mock_portfolio:
            mock_portfolio.return_value = {
                'total_value': 12500.75,
                'total_pnl': 2500.75,
                'positions': {
                    'BTC/USDT': {'amount': 0.15, 'value': 7500.0, 'pnl': 1500.0},
                    'ETH/USDT': {'amount': 1.5, 'value': 4500.0, 'pnl': 900.0}
                }
            }
            
            # Mock update and context
            mock_update = Mock()
            mock_update.message.chat.id = "test_chat_id"
            
            mock_context = Mock()
            mock_context.bot.send_message = AsyncMock()
            
            # Handle portfolio command
            await agent.handle_portfolio_command(mock_update, mock_context)
            
            # Verify portfolio message was sent
            mock_context.bot.send_message.assert_called_once()
            call_args = mock_context.bot.send_message.call_args
            assert "12500.75" in call_args[1]['text']  # Total value
            assert "BTC/USDT" in call_args[1]['text']
    
    def test_format_trading_signal(self, agent):
        """Test trading signal formatting."""
        signal = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'confidence': 0.85,
            'price': 50000.0,
            'reasoning': 'RSI oversold with bullish MACD crossover'
        }
        
        formatted_message = agent.format_trading_signal(signal)
        
        assert "🚀" in formatted_message  # Buy emoji
        assert "BTC/USDT" in formatted_message
        assert "BUY" in formatted_message
        assert "85%" in formatted_message  # Confidence
        assert "50000.0" in formatted_message
        assert "RSI oversold" in formatted_message
    
    def test_format_trade_execution(self, agent):
        """Test trade execution formatting."""
        execution = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 0.1,
            'price': 50000.0,
            'order_id': 'order_123',
            'status': 'filled'
        }
        
        formatted_message = agent.format_trade_execution(execution)
        
        assert "✅" in formatted_message  # Success emoji
        assert "BTC/USDT" in formatted_message
        assert "BUY" in formatted_message
        assert "0.1" in formatted_message
        assert "50000.0" in formatted_message
        assert "order_123" in formatted_message


@pytest.mark.unit
class TestDatabaseManagerAgent:
    """Test cases for DatabaseManagerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create DatabaseManagerAgent instance for testing."""
        agent = DatabaseManagerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_store_market_data(self, agent, mock_supabase_client):
        """Test market data storage."""
        market_data = {
            'symbol': 'BTC/USDT',
            'price': 50000.0,
            'volume': 1000.0,
            'timestamp': datetime.now().isoformat()
        }
        
        # Mock successful insertion
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value = Mock(
            data=[{'id': 1, **market_data}]
        )
        
        # Store market data
        result = await agent.store_market_data(market_data)
        
        # Verify storage
        assert result is not None
        assert result['id'] == 1
        mock_supabase_client.table.assert_called_with('market_data')
    
    @pytest.mark.asyncio
    async def test_store_trading_signal(self, agent, mock_supabase_client):
        """Test trading signal storage."""
        signal = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'confidence': 0.85,
            'timestamp': datetime.now().isoformat(),
            'agent_id': 'signal_generator'
        }
        
        # Mock successful insertion
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value = Mock(
            data=[{'id': 1, **signal}]
        )
        
        # Store signal
        result = await agent.store_trading_signal(signal)
        
        # Verify storage
        assert result is not None
        assert result['id'] == 1
        mock_supabase_client.table.assert_called_with('trading_signals')
    
    @pytest.mark.asyncio
    async def test_store_trade_execution(self, agent, mock_supabase_client):
        """Test trade execution storage."""
        execution = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'amount': 0.1,
            'price': 50000.0,
            'order_id': 'order_123',
            'status': 'filled',
            'timestamp': datetime.now().isoformat()
        }
        
        # Mock successful insertion
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value = Mock(
            data=[{'id': 1, **execution}]
        )
        
        # Store execution
        result = await agent.store_trade_execution(execution)
        
        # Verify storage
        assert result is not None
        assert result['id'] == 1
        mock_supabase_client.table.assert_called_with('trade_executions')
    
    @pytest.mark.asyncio
    async def test_get_recent_signals(self, agent, mock_supabase_client):
        """Test retrieving recent signals."""
        # Mock query response
        mock_signals = [
            {
                'id': 1,
                'symbol': 'BTC/USDT',
                'action': 'BUY',
                'confidence': 0.85,
                'timestamp': datetime.now().isoformat()
            },
            {
                'id': 2,
                'symbol': 'ETH/USDT',
                'action': 'SELL',
                'confidence': 0.78,
                'timestamp': (datetime.now() - timedelta(hours=1)).isoformat()
            }
        ]
        
        mock_supabase_client.table.return_value.select.return_value.order.return_value.limit.return_value.execute.return_value = Mock(
            data=mock_signals
        )
        
        # Get recent signals
        result = await agent.get_recent_signals(limit=10)
        
        # Verify retrieval
        assert result is not None
        assert len(result) == 2
        assert result[0]['symbol'] == 'BTC/USDT'
        assert result[1]['symbol'] == 'ETH/USDT'
    
    @pytest.mark.asyncio
    async def test_get_portfolio_history(self, agent, mock_supabase_client):
        """Test retrieving portfolio history."""
        # Mock portfolio history
        mock_history = [
            {
                'id': 1,
                'total_value': 10000.0,
                'timestamp': (datetime.now() - timedelta(days=2)).isoformat()
            },
            {
                'id': 2,
                'total_value': 10500.0,
                'timestamp': (datetime.now() - timedelta(days=1)).isoformat()
            },
            {
                'id': 3,
                'total_value': 11000.0,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        mock_supabase_client.table.return_value.select.return_value.gte.return_value.order.return_value.execute.return_value = Mock(
            data=mock_history
        )
        
        # Get portfolio history
        start_date = datetime.now() - timedelta(days=7)
        result = await agent.get_portfolio_history(start_date)
        
        # Verify retrieval
        assert result is not None
        assert len(result) == 3
        assert result[0]['total_value'] == 10000.0
        assert result[-1]['total_value'] == 11000.0
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data(self, agent, mock_supabase_client):
        """Test old data cleanup."""
        # Mock deletion response
        mock_supabase_client.table.return_value.delete.return_value.lt.return_value.execute.return_value = Mock(
            data=[]
        )
        
        # Cleanup old data (older than 30 days)
        cutoff_date = datetime.now() - timedelta(days=30)
        result = await agent.cleanup_old_data('market_data', cutoff_date)
        
        # Verify cleanup was attempted
        assert result is not None
        mock_supabase_client.table.assert_called_with('market_data')
    
    def test_validate_data_structure(self, agent):
        """Test data structure validation."""
        # Valid market data
        valid_market_data = {
            'symbol': 'BTC/USDT',
            'price': 50000.0,
            'volume': 1000.0,
            'timestamp': datetime.now().isoformat()
        }
        
        assert agent.validate_market_data(valid_market_data) is True
        
        # Invalid market data - missing required fields
        invalid_market_data = {
            'symbol': 'BTC/USDT',
            'price': 50000.0
            # Missing volume and timestamp
        }
        
        assert agent.validate_market_data(invalid_market_data) is False
        
        # Valid trading signal
        valid_signal = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'confidence': 0.85,
            'timestamp': datetime.now().isoformat()
        }
        
        assert agent.validate_trading_signal(valid_signal) is True
