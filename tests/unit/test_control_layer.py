"""
Unit tests for Control & Interaction Layer core functionality.
"""
import pytest
from datetime import datetime


@pytest.mark.unit
class TestControlLayerCore:
    """Test cases for Control Layer core functionality."""

    def test_parameter_optimization_logic(self):
        """Test parameter optimization calculations."""
        def optimize_parameters(current_params, performance_data):
            """Simple parameter optimization logic."""
            if performance_data['sharpe_ratio'] < 1.0:
                # Increase lookback period for better signals
                return {
                    'lookback_period': min(current_params['lookback_period'] * 1.2, 50),
                    'threshold': current_params['threshold'] * 0.9
                }
            return current_params

        current_params = {'lookback_period': 20, 'threshold': 0.05}
        performance_data = {'sharpe_ratio': 0.8}

        optimized = optimize_parameters(current_params, performance_data)

        # Should increase lookback period and decrease threshold
        assert optimized['lookback_period'] > current_params['lookback_period']
        assert optimized['threshold'] < current_params['threshold']

    def test_notification_formatting(self):
        """Test notification message formatting."""
        def format_performance_alert(strategy_name, metric, value, threshold):
            """Format performance alert message."""
            return f"⚠️ {strategy_name}: {metric} = {value:.2%} (threshold: {threshold:.2%})"

        message = format_performance_alert("BTC_Strategy", "drawdown", -0.08, -0.05)

        assert "BTC_Strategy" in message
        assert "-8.00%" in message
        assert "-5.00%" in message
        assert "⚠️" in message

    def test_telegram_message_validation(self):
        """Test Telegram message validation."""
        def validate_telegram_message(message):
            """Validate Telegram message format."""
            if not isinstance(message, str):
                return False
            if not message or len(message) > 4096:
                return False
            return True

        # Valid message
        assert validate_telegram_message("Valid message") is True

        # Invalid messages
        assert validate_telegram_message("") is False
        assert validate_telegram_message("x" * 5000) is False
        assert validate_telegram_message(123) is False

    def test_slack_webhook_formatting(self):
        """Test Slack webhook message formatting."""
        def format_slack_message(title, text, color="good"):
            """Format message for Slack webhook."""
            return {
                "attachments": [
                    {
                        "title": title,
                        "text": text,
                        "color": color,
                        "ts": int(datetime.now().timestamp())
                    }
                ]
            }

        message = format_slack_message("Trading Alert", "BTC signal generated", "warning")

        assert "attachments" in message
        assert message["attachments"][0]["title"] == "Trading Alert"
        assert message["attachments"][0]["text"] == "BTC signal generated"
        assert message["attachments"][0]["color"] == "warning"
        
        # Valid trading signal validation function
        def validate_trading_signal(signal):
            """Validate trading signal structure."""
            required_fields = ['action', 'symbol', 'confidence', 'timestamp']
            return all(field in signal for field in required_fields)

        valid_signal = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'confidence': 0.85,
            'timestamp': datetime.now().isoformat()
        }

        assert validate_trading_signal(valid_signal) is True
