"""
Unit tests for Strategy Generation & Backtesting Layer agents.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from crypto_trading_system.agents.strategy_generation.signal_generator import SignalGeneratorAgent
from crypto_trading_system.agents.strategy_generation.backtesting_engine import BacktestingEngineAgent
from crypto_trading_system.agents.strategy_generation.strategy_optimizer import StrategyOptimizerAgent
from crypto_trading_system.agents.strategy_generation.risk_calculator import RiskCalculatorAgent


@pytest.mark.unit
class TestSignalGeneratorAgent:
    """Test cases for SignalGeneratorAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_ai_clients, mock_supabase_client):
        """Create SignalGeneratorAgent instance for testing."""
        agent = SignalGeneratorAgent(
            runtime=mock_runtime,
            ai_clients=mock_ai_clients,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_generate_buy_signal(self, agent, sample_market_data):
        """Test buy signal generation."""
        # Setup market conditions for buy signal
        market_analysis = {
            'rsi': 25,  # Oversold
            'macd_signal': 'bullish_crossover',
            'trend': 'upward',
            'support_level': 48000,
            'current_price': 48500
        }
        
        # Mock AI response for buy signal
        agent.ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "BUY", "confidence": 0.85, "reasoning": "Oversold RSI with bullish MACD crossover"}')])
        )
        
        signal = await agent.generate_trading_signal('BTC/USDT', market_analysis)
        
        assert signal is not None
        assert signal['action'] == 'BUY'
        assert signal['symbol'] == 'BTC/USDT'
        assert 0 <= signal['confidence'] <= 1
        assert 'reasoning' in signal
        assert 'timestamp' in signal
    
    @pytest.mark.asyncio
    async def test_generate_sell_signal(self, agent):
        """Test sell signal generation."""
        # Setup market conditions for sell signal
        market_analysis = {
            'rsi': 75,  # Overbought
            'macd_signal': 'bearish_crossover',
            'trend': 'downward',
            'resistance_level': 52000,
            'current_price': 51500
        }
        
        # Mock AI response for sell signal
        agent.ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "SELL", "confidence": 0.78, "reasoning": "Overbought RSI with bearish MACD crossover"}')])
        )
        
        signal = await agent.generate_trading_signal('BTC/USDT', market_analysis)
        
        assert signal is not None
        assert signal['action'] == 'SELL'
        assert signal['confidence'] > 0.7
    
    @pytest.mark.asyncio
    async def test_generate_hold_signal(self, agent):
        """Test hold signal generation."""
        # Setup neutral market conditions
        market_analysis = {
            'rsi': 50,  # Neutral
            'macd_signal': 'neutral',
            'trend': 'sideways',
            'current_price': 50000
        }
        
        # Mock AI response for hold signal
        agent.ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "HOLD", "confidence": 0.60, "reasoning": "Neutral market conditions"}')])
        )
        
        signal = await agent.generate_trading_signal('BTC/USDT', market_analysis)
        
        assert signal is not None
        assert signal['action'] == 'HOLD'
    
    def test_signal_validation(self, agent):
        """Test signal validation logic."""
        # Valid signal
        valid_signal = {
            'action': 'BUY',
            'confidence': 0.8,
            'symbol': 'BTC/USDT',
            'timestamp': datetime.now()
        }
        
        assert agent.validate_signal(valid_signal) is True
        
        # Invalid signal - missing required fields
        invalid_signal = {
            'action': 'BUY',
            'confidence': 0.8
            # Missing symbol and timestamp
        }
        
        assert agent.validate_signal(invalid_signal) is False
        
        # Invalid signal - invalid confidence
        invalid_confidence = {
            'action': 'BUY',
            'confidence': 1.5,  # > 1.0
            'symbol': 'BTC/USDT',
            'timestamp': datetime.now()
        }
        
        assert agent.validate_signal(invalid_confidence) is False


@pytest.mark.unit
class TestBacktestingEngineAgent:
    """Test cases for BacktestingEngineAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create BacktestingEngineAgent instance for testing."""
        agent = BacktestingEngineAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.fixture
    def sample_strategy(self):
        """Create sample trading strategy for testing."""
        return {
            'name': 'RSI_MACD_Strategy',
            'parameters': {
                'rsi_period': 14,
                'rsi_oversold': 30,
                'rsi_overbought': 70,
                'macd_fast': 12,
                'macd_slow': 26,
                'macd_signal': 9
            },
            'entry_conditions': ['rsi < 30', 'macd_crossover_bullish'],
            'exit_conditions': ['rsi > 70', 'macd_crossover_bearish']
        }
    
    @pytest.mark.asyncio
    async def test_run_backtest(self, agent, sample_strategy, sample_market_data):
        """Test backtest execution."""
        backtest_config = {
            'initial_balance': 10000,
            'commission': 0.001,
            'slippage': 0.0005,
            'start_date': '2024-01-01',
            'end_date': '2024-01-31'
        }
        
        result = await agent.run_backtest(
            strategy=sample_strategy,
            market_data=sample_market_data,
            config=backtest_config
        )
        
        assert result is not None
        assert 'total_return' in result
        assert 'sharpe_ratio' in result
        assert 'max_drawdown' in result
        assert 'total_trades' in result
        assert 'win_rate' in result
        assert 'profit_factor' in result
        
        # Verify metrics are reasonable
        assert isinstance(result['total_trades'], int)
        assert result['total_trades'] >= 0
        assert 0 <= result['win_rate'] <= 1
    
    def test_calculate_returns(self, agent):
        """Test return calculation."""
        trades = [
            {'entry_price': 50000, 'exit_price': 52000, 'quantity': 0.1, 'side': 'long'},
            {'entry_price': 52000, 'exit_price': 51000, 'quantity': 0.1, 'side': 'long'},
            {'entry_price': 51000, 'exit_price': 53000, 'quantity': 0.1, 'side': 'long'}
        ]
        
        returns = agent.calculate_trade_returns(trades)
        
        assert len(returns) == len(trades)
        assert returns[0] > 0  # Profitable trade
        assert returns[1] < 0  # Loss trade
        assert returns[2] > 0  # Profitable trade
    
    def test_calculate_sharpe_ratio(self, agent):
        """Test Sharpe ratio calculation."""
        returns = [0.02, -0.01, 0.03, 0.01, -0.005, 0.025]
        
        sharpe = agent.calculate_sharpe_ratio(returns, risk_free_rate=0.02)
        
        assert isinstance(sharpe, (int, float))
        # Sharpe ratio should be reasonable for these returns
        assert -5 <= sharpe <= 5
    
    def test_calculate_max_drawdown(self, agent):
        """Test maximum drawdown calculation."""
        portfolio_values = [10000, 10500, 10200, 9800, 9500, 10100, 10800]
        
        max_dd = agent.calculate_max_drawdown(portfolio_values)
        
        assert isinstance(max_dd, (int, float))
        assert max_dd <= 0  # Drawdown should be negative or zero
        assert max_dd >= -1  # Should not exceed -100%


@pytest.mark.unit
class TestStrategyOptimizerAgent:
    """Test cases for StrategyOptimizerAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create StrategyOptimizerAgent instance for testing."""
        agent = StrategyOptimizerAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    @pytest.mark.asyncio
    async def test_optimize_parameters(self, agent, sample_market_data):
        """Test parameter optimization."""
        strategy_template = {
            'name': 'RSI_Strategy',
            'parameters': {
                'rsi_period': [10, 14, 20],
                'rsi_oversold': [20, 30, 35],
                'rsi_overbought': [65, 70, 80]
            }
        }
        
        # Mock backtesting results
        with patch.object(agent, 'run_parameter_combination') as mock_backtest:
            mock_backtest.return_value = {
                'sharpe_ratio': 1.5,
                'total_return': 0.15,
                'max_drawdown': -0.08
            }
            
            result = await agent.optimize_strategy_parameters(
                strategy_template,
                sample_market_data,
                optimization_metric='sharpe_ratio'
            )
            
            assert result is not None
            assert 'best_parameters' in result
            assert 'best_performance' in result
            assert 'optimization_results' in result
            
            # Verify best parameters are from the parameter space
            best_params = result['best_parameters']
            assert best_params['rsi_period'] in [10, 14, 20]
            assert best_params['rsi_oversold'] in [20, 30, 35]
            assert best_params['rsi_overbought'] in [65, 70, 80]
    
    def test_parameter_grid_generation(self, agent):
        """Test parameter grid generation."""
        param_ranges = {
            'param1': [1, 2, 3],
            'param2': [0.1, 0.2],
            'param3': [True, False]
        }
        
        grid = agent.generate_parameter_grid(param_ranges)
        
        # Should generate all combinations: 3 * 2 * 2 = 12
        assert len(grid) == 12
        
        # Verify all combinations are present
        for combo in grid:
            assert combo['param1'] in [1, 2, 3]
            assert combo['param2'] in [0.1, 0.2]
            assert combo['param3'] in [True, False]
    
    def test_performance_ranking(self, agent):
        """Test performance ranking of parameter combinations."""
        results = [
            {'params': {'a': 1}, 'sharpe_ratio': 1.2, 'total_return': 0.10},
            {'params': {'a': 2}, 'sharpe_ratio': 1.8, 'total_return': 0.15},
            {'params': {'a': 3}, 'sharpe_ratio': 0.9, 'total_return': 0.08}
        ]
        
        ranked = agent.rank_results_by_metric(results, 'sharpe_ratio')
        
        assert len(ranked) == 3
        assert ranked[0]['sharpe_ratio'] == 1.8  # Best performance first
        assert ranked[1]['sharpe_ratio'] == 1.2
        assert ranked[2]['sharpe_ratio'] == 0.9


@pytest.mark.unit
class TestRiskCalculatorAgent:
    """Test cases for RiskCalculatorAgent."""
    
    @pytest.fixture
    def agent(self, mock_runtime, mock_supabase_client):
        """Create RiskCalculatorAgent instance for testing."""
        agent = RiskCalculatorAgent(
            runtime=mock_runtime,
            supabase_client=mock_supabase_client
        )
        return agent
    
    def test_calculate_position_size(self, agent):
        """Test position size calculation."""
        account_balance = 10000
        risk_per_trade = 0.02  # 2%
        entry_price = 50000
        stop_loss_price = 48000
        
        position_size = agent.calculate_position_size(
            account_balance,
            risk_per_trade,
            entry_price,
            stop_loss_price
        )
        
        assert position_size > 0
        assert isinstance(position_size, (int, float))
        
        # Verify risk calculation
        risk_amount = account_balance * risk_per_trade
        price_risk = entry_price - stop_loss_price
        expected_size = risk_amount / price_risk
        
        assert abs(position_size - expected_size) < 0.001
    
    def test_calculate_var(self, agent):
        """Test Value at Risk calculation."""
        returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for 1 year
        confidence_level = 0.95
        
        var = agent.calculate_var(returns, confidence_level)
        
        assert isinstance(var, (int, float))
        assert var < 0  # VaR should be negative (loss)
        
        # VaR should be reasonable for the given returns
        assert var > -0.1  # Should not exceed -10% daily loss
    
    def test_calculate_portfolio_beta(self, agent):
        """Test portfolio beta calculation."""
        portfolio_returns = np.random.normal(0.001, 0.025, 100)
        market_returns = np.random.normal(0.0008, 0.02, 100)
        
        beta = agent.calculate_portfolio_beta(portfolio_returns, market_returns)
        
        assert isinstance(beta, (int, float))
        # Beta should be reasonable
        assert -3 <= beta <= 3
    
    def test_risk_metrics_calculation(self, agent):
        """Test comprehensive risk metrics calculation."""
        portfolio_data = {
            'returns': np.random.normal(0.001, 0.02, 252),
            'positions': {
                'BTC/USDT': {'value': 5000, 'weight': 0.5},
                'ETH/USDT': {'value': 3000, 'weight': 0.3},
                'ADA/USDT': {'value': 2000, 'weight': 0.2}
            },
            'total_value': 10000
        }
        
        risk_metrics = agent.calculate_risk_metrics(portfolio_data)
        
        assert 'var_95' in risk_metrics
        assert 'var_99' in risk_metrics
        assert 'expected_shortfall' in risk_metrics
        assert 'volatility' in risk_metrics
        assert 'sharpe_ratio' in risk_metrics
        assert 'max_drawdown' in risk_metrics
        
        # Verify metrics are reasonable
        assert risk_metrics['volatility'] > 0
        assert risk_metrics['var_95'] < 0
        assert risk_metrics['var_99'] < risk_metrics['var_95']  # 99% VaR should be worse than 95%
    
    @pytest.mark.asyncio
    async def test_risk_assessment(self, agent):
        """Test overall risk assessment."""
        portfolio_state = {
            'balance': 10000,
            'positions': {
                'BTC/USDT': {'amount': 0.1, 'entry_price': 50000, 'current_price': 52000},
                'ETH/USDT': {'amount': 1.0, 'entry_price': 3000, 'current_price': 3100}
            },
            'total_exposure': 8200
        }
        
        risk_assessment = await agent.assess_portfolio_risk(portfolio_state)
        
        assert risk_assessment is not None
        assert 'risk_level' in risk_assessment
        assert 'risk_score' in risk_assessment
        assert 'recommendations' in risk_assessment
        
        # Risk level should be one of the expected values
        assert risk_assessment['risk_level'] in ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
        
        # Risk score should be between 0 and 100
        assert 0 <= risk_assessment['risk_score'] <= 100
