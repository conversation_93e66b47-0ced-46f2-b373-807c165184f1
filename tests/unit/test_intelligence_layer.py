"""
Unit tests for Intelligence Collection Layer agents.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np


@pytest.mark.unit
class TestIntelligenceLayerCore:
    """Test cases for Intelligence Layer core functionality."""

    def test_data_processing_functions(self):
        """Test core data processing functions."""
        # Test data validation
        sample_data = {
            'timestamp': datetime.now(),
            'symbol': 'BTC/USDT',
            'price': 50000.0,
            'volume': 1000.0
        }

        # Validate required fields
        required_fields = ['timestamp', 'symbol', 'price', 'volume']
        assert all(field in sample_data for field in required_fields)

        # Validate data types
        assert isinstance(sample_data['timestamp'], datetime)
        assert isinstance(sample_data['symbol'], str)
        assert isinstance(sample_data['price'], (int, float))
        assert isinstance(sample_data['volume'], (int, float))

    def test_sentiment_analysis_functions(self):
        """Test sentiment analysis helper functions."""
        # Test sentiment scoring
        def calculate_sentiment_score(positive_words, negative_words, total_words):
            if total_words == 0:
                return 0.0

            positive_ratio = positive_words / total_words
            negative_ratio = negative_words / total_words

            # Simple sentiment score: positive - negative
            return positive_ratio - negative_ratio

        # Test cases
        assert calculate_sentiment_score(10, 5, 20) == 0.25  # Positive sentiment
        assert calculate_sentiment_score(3, 7, 20) == -0.2   # Negative sentiment
        assert calculate_sentiment_score(5, 5, 20) == 0.0    # Neutral sentiment

    def test_web_intelligence_data_structure(self):
        """Test web intelligence data structure validation."""
        # Sample web intelligence data
        web_data = {
            'source': 'twitter',
            'content': 'Bitcoin price is rising!',
            'timestamp': datetime.now(),
            'sentiment_score': 0.8,
            'engagement_metrics': {
                'likes': 100,
                'retweets': 50,
                'replies': 25
            }
        }

        # Validate structure
        assert 'source' in web_data
        assert 'content' in web_data
        assert 'timestamp' in web_data
        assert 'sentiment_score' in web_data
        assert 'engagement_metrics' in web_data

        # Validate sentiment score range
        assert -1 <= web_data['sentiment_score'] <= 1

        # Validate engagement metrics
        metrics = web_data['engagement_metrics']
        assert all(isinstance(metrics[key], int) for key in metrics)
        assert all(metrics[key] >= 0 for key in metrics)

    def test_whale_tracking_calculations(self):
        """Test whale tracking calculation functions."""
        # Sample transaction data
        transactions = [
            {'amount': 1000, 'address': '0x123...', 'timestamp': datetime.now()},
            {'amount': 500, 'address': '0x456...', 'timestamp': datetime.now()},
            {'amount': 2000, 'address': '0x123...', 'timestamp': datetime.now()},
            {'amount': 100, 'address': '0x789...', 'timestamp': datetime.now()}
        ]

        # Calculate whale activity (threshold: 1000)
        whale_threshold = 1000
        whale_txs = [tx for tx in transactions if tx['amount'] >= whale_threshold]

        assert len(whale_txs) == 2  # Two transactions >= 1000

        # Calculate total whale volume
        whale_volume = sum(tx['amount'] for tx in whale_txs)
        assert whale_volume == 3000  # 1000 + 2000

        # Group by address
        address_activity = {}
        for tx in transactions:
            addr = tx['address']
            if addr not in address_activity:
                address_activity[addr] = 0
            address_activity[addr] += tx['amount']

        # Find whale addresses
        whale_addresses = [addr for addr, volume in address_activity.items()
                          if volume >= whale_threshold]

        assert len(whale_addresses) == 1  # Only 0x123... has total >= 1000
        assert '0x123...' in whale_addresses

    def test_tvl_monitoring_calculations(self):
        """Test TVL monitoring calculation functions."""
        # Sample DeFi protocol data
        protocols = [
            {'name': 'Protocol A', 'tvl': 1000000, 'change_24h': 0.05},
            {'name': 'Protocol B', 'tvl': 2000000, 'change_24h': -0.02},
            {'name': 'Protocol C', 'tvl': 500000, 'change_24h': 0.10}
        ]

        # Calculate total TVL
        total_tvl = sum(p['tvl'] for p in protocols)
        assert total_tvl == 3500000

        # Find top protocol by TVL
        top_protocol = max(protocols, key=lambda x: x['tvl'])
        assert top_protocol['name'] == 'Protocol B'

        # Find fastest growing protocol
        fastest_growing = max(protocols, key=lambda x: x['change_24h'])
        assert fastest_growing['name'] == 'Protocol C'

        # Calculate market share
        for protocol in protocols:
            protocol['market_share'] = protocol['tvl'] / total_tvl

        protocol_a = next(p for p in protocols if p['name'] == 'Protocol A')
        expected_share = 1000000 / 3500000
        assert abs(protocol_a['market_share'] - expected_share) < 0.001

    def test_meme_token_trend_analysis(self):
        """Test meme token trend analysis functions."""
        # Sample meme token data
        meme_tokens = [
            {'symbol': 'DOGE', 'price_change_24h': 0.15, 'volume_24h': 1000000, 'social_mentions': 5000},
            {'symbol': 'SHIB', 'price_change_24h': -0.05, 'volume_24h': 800000, 'social_mentions': 3000},
            {'symbol': 'PEPE', 'price_change_24h': 0.25, 'volume_24h': 500000, 'social_mentions': 2000}
        ]

        # Calculate trend score (price change + volume + social activity)
        for token in meme_tokens:
            # Normalize metrics (simplified)
            price_score = token['price_change_24h']
            volume_score = token['volume_24h'] / 1000000  # Normalize to 0-1
            social_score = token['social_mentions'] / 5000  # Normalize to 0-1

            token['trend_score'] = (price_score + volume_score + social_score) / 3

        # Find trending token
        trending_token = max(meme_tokens, key=lambda x: x['trend_score'])
        assert trending_token['symbol'] == 'DOGE'  # Should have highest combined score

        # Filter tokens with positive price movement
        positive_tokens = [t for t in meme_tokens if t['price_change_24h'] > 0]
        assert len(positive_tokens) == 2  # DOGE and PEPE

    
    def test_address_classification(self, agent):
        """Test address classification logic."""
        # Test whale address detection
        balance = 1000000000000000000000  # 1000 ETH
        classification = agent.classify_address_by_balance(balance, 'ETH')
        
        assert classification in ['whale', 'large_holder', 'retail', 'dust']
        
        # Test with different balance ranges
        small_balance = 1000000000000000000  # 1 ETH
        small_classification = agent.classify_address_by_balance(small_balance, 'ETH')
        assert small_classification != 'whale'
