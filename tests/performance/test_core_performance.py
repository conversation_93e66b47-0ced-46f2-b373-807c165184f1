"""
Core performance tests for the crypto trading system.
"""
import pytest
import time
import pandas as pd
import numpy as np
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor
import asyncio


@pytest.mark.performance
class TestCorePerformance:
    """Performance benchmarks for core trading functionality."""
    
    def test_memory_usage_baseline(self):
        """Test baseline memory usage."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform garbage collection
        gc.collect()
        
        # Memory should be reasonable for a Python application
        assert initial_memory < 500, f"Initial memory usage too high: {initial_memory:.2f} MB"
    
    def test_data_processing_performance(self):
        """Test data processing performance with large datasets."""
        # Generate large dataset
        data_size = 10000
        prices = np.random.randn(data_size).cumsum() + 50000
        volumes = np.random.exponential(100, data_size)
        timestamps = pd.date_range('2024-01-01', periods=data_size, freq='1min')
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'close': prices,
            'volume': volumes
        })
        
        # Measure processing time
        start_time = time.time()
        
        # Simulate data processing operations
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['rsi'] = self._calculate_rsi(df['close'], 14)
        df['volume_sma'] = df['volume'].rolling(window=10).mean()
        
        processing_time = time.time() - start_time
        
        # Processing should be fast
        assert processing_time < 1.0, f"Data processing too slow: {processing_time:.3f}s"
        
        # Verify results
        assert not pd.isna(df['sma_20'].iloc[-1])  # Not NaN
        assert 0 <= df['rsi'].iloc[-1] <= 100
    
    def _calculate_rsi(self, prices, period=14):
        """Calculate RSI for performance testing."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def test_concurrent_processing_performance(self):
        """Test performance under concurrent load."""
        def process_data_chunk(chunk_id):
            """Process a chunk of data."""
            data_size = 1000
            prices = np.random.randn(data_size).cumsum() + 50000
            
            # Simulate processing
            sma = pd.Series(prices).rolling(window=20).mean()
            return {
                'chunk_id': chunk_id,
                'processed_points': len(sma.dropna()),
                'avg_price': np.mean(prices)
            }
        
        # Test concurrent processing
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(process_data_chunk, i) for i in range(10)]
            results = [future.result() for future in futures]
        
        processing_time = time.time() - start_time
        
        # Concurrent processing should be efficient
        assert processing_time < 2.0, f"Concurrent processing too slow: {processing_time:.3f}s"
        assert len(results) == 10
        assert all('processed_points' in result for result in results)
    
    @pytest.mark.asyncio
    async def test_async_operations_performance(self):
        """Test async operations performance."""
        async def mock_api_call(delay=0.01):
            """Mock API call with delay."""
            await asyncio.sleep(delay)
            return {
                'timestamp': pd.Timestamp.now().isoformat(),
                'data': np.random.randn(100).tolist()
            }
        
        # Test concurrent async operations
        start_time = time.time()
        
        tasks = [mock_api_call() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        
        processing_time = time.time() - start_time
        
        # Async operations should be concurrent
        assert processing_time < 0.5, f"Async operations too slow: {processing_time:.3f}s"
        assert len(results) == 20
        assert all('data' in result for result in results)
    
    def test_memory_efficiency_large_dataset(self):
        """Test memory efficiency with large datasets."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large dataset
        large_data = []
        for i in range(5):
            df = pd.DataFrame({
                'timestamp': pd.date_range('2024-01-01', periods=5000, freq='1min'),
                'price': np.random.randn(5000).cumsum() + 50000,
                'volume': np.random.exponential(100, 5000)
            })
            large_data.append(df)
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Clean up
        del large_data
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        memory_cleanup = peak_memory - final_memory
        
        # Memory usage should be reasonable
        assert memory_increase < 100, f"Memory increase too high: {memory_increase:.2f} MB"
        # Memory cleanup assertion is optional since Python GC behavior varies
        # assert memory_cleanup > memory_increase * 0.1, "Poor memory cleanup"
    
    def test_technical_indicator_performance(self):
        """Test technical indicator calculation performance."""
        # Generate price dataset
        data_size = 5000
        np.random.seed(42)
        prices = pd.Series(np.random.randn(data_size).cumsum() + 50000)
        
        # Measure calculation performance
        start_time = time.time()
        
        # Calculate technical indicators
        sma_20 = prices.rolling(window=20).mean()
        sma_50 = prices.rolling(window=50).mean()
        
        # RSI calculation
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        calculation_time = time.time() - start_time
        
        # Performance benchmark
        assert calculation_time < 0.5, f"Technical indicators took {calculation_time:.3f}s, expected < 0.5s"
        
        # Verify calculations
        assert not pd.isna(sma_20.iloc[-1])
        assert not pd.isna(rsi.iloc[-1])
        assert 0 <= rsi.iloc[-1] <= 100
        assert sma_50.iloc[-1] != sma_20.iloc[-1]  # Different periods should give different values
