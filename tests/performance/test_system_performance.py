"""
Performance tests for the crypto trading system.
"""
import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor

from crypto_trading_system.core.trading_system import CryptoTradingSystem


@pytest.mark.performance
class TestSystemPerformance:
    """Performance benchmarks for the trading system."""
    
    @pytest.fixture
    async def trading_system(self, mock_supabase_client, mock_exchange_client, mock_ai_clients):
        """Set up trading system for performance testing."""
        with patch('crypto_trading_system.core.trading_system.create_supabase_client') as mock_supabase:
            with patch('crypto_trading_system.core.trading_system.ccxt') as mock_ccxt:
                mock_supabase.return_value = mock_supabase_client
                mock_ccxt.binance.return_value = mock_exchange_client
                
                system = CryptoTradingSystem()
                await system.initialize()
                
                yield system
                
                await system.shutdown()
    
    @pytest.fixture
    def large_market_dataset(self):
        """Generate large market dataset for performance testing."""
        # Generate 1 year of hourly data (8760 data points)
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='1H')
        np.random.seed(42)
        
        # Generate realistic price movements
        returns = np.random.normal(0.0001, 0.02, len(dates))
        prices = [50000]
        
        for i in range(1, len(dates)):
            price = prices[-1] * (1 + returns[i])
            prices.append(max(price, 1000))
        
        data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100, 10000, len(dates))
        })
        
        # Ensure OHLC consistency
        for i in range(len(data)):
            data.loc[i, 'high'] = max(data.loc[i, 'open'], data.loc[i, 'close'], data.loc[i, 'high'])
            data.loc[i, 'low'] = min(data.loc[i, 'open'], data.loc[i, 'close'], data.loc[i, 'low'])
        
        return data
    
    @pytest.mark.asyncio
    async def test_data_processing_speed(self, trading_system, large_market_dataset):
        """Test speed of market data processing."""
        # Measure processing time for large dataset
        start_time = time.time()
        
        # Process market data through technical analysis
        with patch.object(trading_system.technical_analyst, 'analyze_market_data') as mock_analyze:
            mock_analyze.return_value = {
                'signals': [],
                'indicators': {},
                'confidence': 0.5
            }
            
            result = await trading_system.technical_analyst.analyze_market_data(large_market_dataset)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Performance benchmark: should process 8760 data points in under 5 seconds
        assert processing_time < 5.0, f"Data processing took {processing_time:.2f}s, expected < 5.0s"
        
        # Calculate throughput
        data_points = len(large_market_dataset)
        throughput = data_points / processing_time
        
        # Should process at least 1000 data points per second
        assert throughput > 1000, f"Throughput: {throughput:.0f} points/sec, expected > 1000"
    
    @pytest.mark.asyncio
    async def test_concurrent_agent_performance(self, trading_system, mock_exchange_client):
        """Test performance under concurrent agent operations."""
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT']
        
        # Setup mock responses
        def mock_fetch_ticker(symbol):
            return {
                'symbol': symbol,
                'last': 1000 + hash(symbol) % 10000,
                'bid': 999 + hash(symbol) % 10000,
                'ask': 1001 + hash(symbol) % 10000,
                'volume': 1000.0,
                'timestamp': datetime.now().timestamp() * 1000
            }
        
        mock_exchange_client.fetch_ticker.side_effect = mock_fetch_ticker
        
        # Measure concurrent processing time
        start_time = time.time()
        
        # Create concurrent tasks
        tasks = []
        for symbol in symbols:
            task = trading_system.execute_trading_cycle([symbol])
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        concurrent_time = end_time - start_time
        
        # Verify all tasks completed successfully
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(symbols)
        
        # Performance benchmark: concurrent processing should be faster than sequential
        # Estimate sequential time (assuming 1 second per symbol)
        estimated_sequential_time = len(symbols) * 1.0
        
        # Concurrent processing should be at least 50% faster
        assert concurrent_time < estimated_sequential_time * 0.5, \
            f"Concurrent time: {concurrent_time:.2f}s, expected < {estimated_sequential_time * 0.5:.2f}s"
    
    @pytest.mark.asyncio
    async def test_memory_usage(self, trading_system, large_market_dataset):
        """Test memory usage during intensive operations."""
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform memory-intensive operations
        for i in range(10):
            # Process large dataset multiple times
            with patch.object(trading_system.technical_analyst, 'analyze_market_data') as mock_analyze:
                mock_analyze.return_value = {'signals': [], 'indicators': {}, 'confidence': 0.5}
                await trading_system.technical_analyst.analyze_market_data(large_market_dataset)
            
            # Force garbage collection
            gc.collect()
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory usage should not increase by more than 500MB
        assert memory_increase < 500, f"Memory increased by {memory_increase:.1f}MB, expected < 500MB"
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, trading_system, mock_supabase_client):
        """Test database query performance."""
        # Setup mock database responses
        mock_data = [{'id': i, 'symbol': 'BTC/USDT', 'price': 50000 + i} for i in range(1000)]
        mock_supabase_client.table.return_value.select.return_value.execute.return_value = Mock(data=mock_data)
        
        # Measure query performance
        start_time = time.time()
        
        # Perform multiple database queries
        for i in range(100):
            result = mock_supabase_client.table('market_data').select('*').execute()
            assert len(result.data) == 1000
        
        end_time = time.time()
        query_time = end_time - start_time
        
        # Performance benchmark: 100 queries should complete in under 1 second
        assert query_time < 1.0, f"Database queries took {query_time:.2f}s, expected < 1.0s"
        
        # Calculate query throughput
        queries_per_second = 100 / query_time
        assert queries_per_second > 50, f"Query rate: {queries_per_second:.0f} queries/sec, expected > 50"
    
    @pytest.mark.asyncio
    async def test_signal_generation_latency(self, trading_system, mock_ai_clients):
        """Test latency of signal generation."""
        # Setup mock AI responses
        mock_ai_clients['anthropic'].messages.create = AsyncMock(
            return_value=Mock(content=[Mock(text='{"action": "BUY", "confidence": 0.8}')])
        )
        
        # Measure signal generation latency
        latencies = []
        
        for i in range(50):  # Test 50 signal generations
            start_time = time.time()
            
            # Generate trading signal
            with patch.object(trading_system.signal_generator, 'generate_trading_signals') as mock_generate:
                mock_generate.return_value = {
                    'signals': [{'action': 'BUY', 'confidence': 0.8, 'symbol': 'BTC/USDT'}]
                }
                
                result = await trading_system.signal_generator.generate_trading_signals({})
            
            end_time = time.time()
            latency = (end_time - start_time) * 1000  # Convert to milliseconds
            latencies.append(latency)
        
        # Calculate performance metrics
        avg_latency = np.mean(latencies)
        p95_latency = np.percentile(latencies, 95)
        p99_latency = np.percentile(latencies, 99)
        
        # Performance benchmarks
        assert avg_latency < 100, f"Average latency: {avg_latency:.1f}ms, expected < 100ms"
        assert p95_latency < 200, f"P95 latency: {p95_latency:.1f}ms, expected < 200ms"
        assert p99_latency < 500, f"P99 latency: {p99_latency:.1f}ms, expected < 500ms"
    
    @pytest.mark.asyncio
    async def test_system_throughput(self, trading_system, mock_exchange_client):
        """Test overall system throughput."""
        # Setup mock exchange responses
        mock_exchange_client.fetch_ticker.return_value = {
            'symbol': 'BTC/USDT',
            'last': 50000.0,
            'bid': 49950.0,
            'ask': 50050.0,
            'volume': 1000.0,
            'timestamp': datetime.now().timestamp() * 1000
        }
        
        # Measure system throughput
        start_time = time.time()
        completed_cycles = 0
        
        # Run trading cycles for 10 seconds
        end_time = start_time + 10
        
        while time.time() < end_time:
            await trading_system.execute_trading_cycle(['BTC/USDT'])
            completed_cycles += 1
        
        actual_duration = time.time() - start_time
        throughput = completed_cycles / actual_duration
        
        # Performance benchmark: should complete at least 5 cycles per second
        assert throughput >= 5, f"Throughput: {throughput:.1f} cycles/sec, expected >= 5"
    
    @pytest.mark.asyncio
    async def test_error_handling_performance(self, trading_system, mock_exchange_client):
        """Test performance impact of error handling."""
        # Setup alternating success/failure responses
        call_count = 0
        
        def mock_fetch_with_errors(symbol):
            nonlocal call_count
            call_count += 1
            
            if call_count % 2 == 0:  # Every other call fails
                raise Exception("Simulated API Error")
            else:
                return {
                    'symbol': symbol,
                    'last': 50000.0,
                    'bid': 49950.0,
                    'ask': 50050.0,
                    'volume': 1000.0,
                    'timestamp': datetime.now().timestamp() * 1000
                }
        
        mock_exchange_client.fetch_ticker.side_effect = mock_fetch_with_errors
        
        # Measure performance with errors
        start_time = time.time()
        
        # Execute multiple cycles with errors
        results = []
        for i in range(20):
            result = await trading_system.execute_trading_cycle(['BTC/USDT'])
            results.append(result)
        
        end_time = time.time()
        error_handling_time = end_time - start_time
        
        # Verify error handling doesn't significantly impact performance
        # Should complete 20 cycles (with 50% errors) in under 10 seconds
        assert error_handling_time < 10, f"Error handling took {error_handling_time:.2f}s, expected < 10s"
        
        # Verify some cycles succeeded despite errors
        successful_results = [r for r in results if r is not None and r.get('status') != 'error']
        assert len(successful_results) > 0, "No successful cycles completed"


@pytest.mark.performance
class TestScalabilityTests:
    """Test system scalability under increasing load."""
    
    @pytest.mark.asyncio
    async def test_increasing_symbol_load(self, trading_system, mock_exchange_client):
        """Test performance with increasing number of symbols."""
        # Setup mock responses
        def mock_fetch_ticker(symbol):
            return {
                'symbol': symbol,
                'last': 1000 + hash(symbol) % 10000,
                'bid': 999 + hash(symbol) % 10000,
                'ask': 1001 + hash(symbol) % 10000,
                'volume': 1000.0,
                'timestamp': datetime.now().timestamp() * 1000
            }
        
        mock_exchange_client.fetch_ticker.side_effect = mock_fetch_ticker
        
        # Test with increasing number of symbols
        symbol_counts = [1, 5, 10, 20, 50]
        performance_results = {}
        
        for count in symbol_counts:
            symbols = [f'SYMBOL{i}/USDT' for i in range(count)]
            
            # Measure processing time
            start_time = time.time()
            result = await trading_system.execute_trading_cycle(symbols)
            end_time = time.time()
            
            processing_time = end_time - start_time
            performance_results[count] = processing_time
            
            # Verify processing completed
            assert result is not None
        
        # Verify scalability - processing time should scale sub-linearly
        # (due to concurrent processing)
        time_1_symbol = performance_results[1]
        time_50_symbols = performance_results[50]
        
        # 50 symbols should not take 50x longer than 1 symbol
        scalability_factor = time_50_symbols / time_1_symbol
        assert scalability_factor < 25, f"Scalability factor: {scalability_factor:.1f}, expected < 25"
