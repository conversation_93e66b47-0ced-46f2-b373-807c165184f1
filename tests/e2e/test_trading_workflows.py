"""
End-to-end tests for complete trading workflows.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import json
import pandas as pd
import numpy as np

from crypto_trading_system.core.trading_system import CryptoTradingSystem
from crypto_trading_system.agents.control.system_coordinator import SystemCoordinatorAgent


@pytest.mark.e2e
class TestCompleteTradingWorkflow:
    """Test complete trading workflows from data collection to execution."""
    
    @pytest.fixture
    async def trading_system(self, mock_supabase_client, mock_exchange_client, mock_ai_clients):
        """Set up complete trading system for E2E testing."""
        with patch('crypto_trading_system.core.trading_system.create_supabase_client') as mock_supabase:
            with patch('crypto_trading_system.core.trading_system.ccxt') as mock_ccxt:
                mock_supabase.return_value = mock_supabase_client
                mock_ccxt.binance.return_value = mock_exchange_client
                
                system = CryptoTradingSystem()
                await system.initialize()
                
                yield system
                
                await system.shutdown()
    
    @pytest.mark.asyncio
    async def test_full_buy_workflow(self, trading_system, sample_market_data, mock_exchange_client):
        """Test complete buy workflow from signal generation to execution."""
        # Setup mock market conditions for buy signal
        mock_exchange_client.fetch_ticker.return_value = {
            'symbol': 'BTC/USDT',
            'last': 48000.0,  # Lower price to trigger buy signal
            'bid': 47950.0,
            'ask': 48050.0,
            'volume': 1000.0,
            'timestamp': datetime.now().timestamp() * 1000
        }
        
        mock_exchange_client.fetch_ohlcv.return_value = [
            [datetime.now().timestamp() * 1000 - i * 60000, 48000 + i * 100, 48500 + i * 100, 47500 + i * 100, 48000 + i * 50, 1000]
            for i in range(100, 0, -1)  # Upward trend data
        ]
        
        # Mock successful order execution
        mock_exchange_client.create_market_buy_order.return_value = {
            'id': 'test_order_123',
            'symbol': 'BTC/USDT',
            'amount': 0.1,
            'price': 48000.0,
            'status': 'closed',
            'filled': 0.1,
            'timestamp': datetime.now().timestamp() * 1000
        }
        
        # Execute trading workflow
        result = await trading_system.execute_trading_cycle(['BTC/USDT'])
        
        # Verify workflow completion
        assert result is not None
        assert 'market_data' in result
        assert 'analysis' in result
        assert 'signals' in result
        assert 'execution_results' in result
        
        # Verify buy signal was generated and executed
        if result['signals']:
            buy_signals = [s for s in result['signals'] if s.get('action') == 'BUY']
            if buy_signals:
                assert len(buy_signals) > 0
                assert buy_signals[0]['symbol'] == 'BTC/USDT'
                
                # Verify order execution
                if result['execution_results']:
                    assert 'BTC/USDT' in result['execution_results']
                    execution = result['execution_results']['BTC/USDT']
                    assert execution['status'] in ['executed', 'pending']
    
    @pytest.mark.asyncio
    async def test_full_sell_workflow(self, trading_system, mock_exchange_client):
        """Test complete sell workflow."""
        # Setup existing position
        with patch.object(trading_system.portfolio_manager, 'get_position') as mock_get_position:
            mock_get_position.return_value = {
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'entry_price': 45000.0,
                'current_value': 5000.0
            }
            
            # Setup market conditions for sell signal
            mock_exchange_client.fetch_ticker.return_value = {
                'symbol': 'BTC/USDT',
                'last': 52000.0,  # Higher price to trigger sell signal
                'bid': 51950.0,
                'ask': 52050.0,
                'volume': 1000.0,
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            # Mock successful sell order
            mock_exchange_client.create_market_sell_order.return_value = {
                'id': 'test_sell_order_456',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 52000.0,
                'status': 'closed',
                'filled': 0.1,
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            # Execute trading workflow
            result = await trading_system.execute_trading_cycle(['BTC/USDT'])
            
            # Verify sell workflow
            if result['signals']:
                sell_signals = [s for s in result['signals'] if s.get('action') == 'SELL']
                if sell_signals:
                    assert sell_signals[0]['symbol'] == 'BTC/USDT'
                    
                    # Verify profit calculation
                    if result['execution_results']:
                        execution = result['execution_results']['BTC/USDT']
                        if 'profit_loss' in execution:
                            assert execution['profit_loss'] > 0  # Should be profitable
    
    @pytest.mark.asyncio
    async def test_risk_management_workflow(self, trading_system, mock_exchange_client):
        """Test risk management and stop-loss execution."""
        # Setup position with loss
        with patch.object(trading_system.portfolio_manager, 'get_position') as mock_get_position:
            mock_get_position.return_value = {
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'entry_price': 50000.0,
                'current_value': 4500.0,  # 10% loss
                'stop_loss': 47500.0
            }
            
            # Setup market conditions triggering stop-loss
            mock_exchange_client.fetch_ticker.return_value = {
                'symbol': 'BTC/USDT',
                'last': 47000.0,  # Below stop-loss
                'bid': 46950.0,
                'ask': 47050.0,
                'volume': 1000.0,
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            # Mock stop-loss order execution
            mock_exchange_client.create_market_sell_order.return_value = {
                'id': 'stop_loss_order_789',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 47000.0,
                'status': 'closed',
                'filled': 0.1,
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            # Execute risk management
            result = await trading_system.risk_manager.check_and_execute_risk_management()
            
            # Verify stop-loss execution
            assert result is not None
            if 'stop_loss_executed' in result:
                assert result['stop_loss_executed'] is True
                assert result['symbol'] == 'BTC/USDT'
    
    @pytest.mark.asyncio
    async def test_multi_asset_workflow(self, trading_system, mock_exchange_client):
        """Test trading workflow with multiple assets."""
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        
        # Setup mock data for multiple assets
        def mock_fetch_ticker(symbol):
            prices = {'BTC/USDT': 50000, 'ETH/USDT': 3000, 'ADA/USDT': 1.5}
            return {
                'symbol': symbol,
                'last': prices[symbol],
                'bid': prices[symbol] * 0.999,
                'ask': prices[symbol] * 1.001,
                'volume': 1000.0,
                'timestamp': datetime.now().timestamp() * 1000
            }
        
        mock_exchange_client.fetch_ticker.side_effect = mock_fetch_ticker
        
        # Execute multi-asset workflow
        result = await trading_system.execute_trading_cycle(symbols)
        
        # Verify multi-asset processing
        assert result is not None
        assert 'market_data' in result
        
        # Check that all symbols were processed
        if isinstance(result['market_data'], dict):
            processed_symbols = list(result['market_data'].keys())
            assert all(symbol in processed_symbols for symbol in symbols)
    
    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, trading_system, mock_exchange_client):
        """Test system recovery from errors during trading workflow."""
        # Setup exchange error
        mock_exchange_client.fetch_ticker.side_effect = Exception("Exchange API Error")
        
        # Execute workflow with error
        result = await trading_system.execute_trading_cycle(['BTC/USDT'])
        
        # Verify error handling
        assert result is not None
        assert 'errors' in result or result.get('status') == 'error'
        
        # Test recovery - fix the error and retry
        mock_exchange_client.fetch_ticker.side_effect = None
        mock_exchange_client.fetch_ticker.return_value = {
            'symbol': 'BTC/USDT',
            'last': 50000.0,
            'bid': 49950.0,
            'ask': 50050.0,
            'volume': 1000.0,
            'timestamp': datetime.now().timestamp() * 1000
        }
        
        # Retry workflow
        recovery_result = await trading_system.execute_trading_cycle(['BTC/USDT'])
        
        # Verify recovery
        assert recovery_result is not None
        assert recovery_result.get('status') != 'error'


@pytest.mark.e2e
class TestBacktestingWorkflow:
    """Test backtesting workflows."""
    
    @pytest.fixture
    def historical_data(self):
        """Generate historical data for backtesting."""
        dates = pd.date_range(start='2024-01-01', end='2024-03-31', freq='1H')
        np.random.seed(42)
        
        # Generate realistic price movement
        returns = np.random.normal(0.0001, 0.02, len(dates))  # Small positive drift
        prices = [50000]  # Starting price
        
        for i in range(1, len(dates)):
            price = prices[-1] * (1 + returns[i])
            prices.append(max(price, 1000))  # Minimum price floor
        
        data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100, 1000, len(dates))
        })
        
        # Ensure OHLC consistency
        for i in range(len(data)):
            data.loc[i, 'high'] = max(data.loc[i, 'open'], data.loc[i, 'close'], data.loc[i, 'high'])
            data.loc[i, 'low'] = min(data.loc[i, 'open'], data.loc[i, 'close'], data.loc[i, 'low'])
        
        return data
    
    @pytest.mark.asyncio
    async def test_strategy_backtesting(self, trading_system, historical_data):
        """Test strategy backtesting with historical data."""
        # Setup backtesting parameters
        backtest_config = {
            'initial_balance': 10000,
            'commission': 0.001,
            'slippage': 0.0005,
            'start_date': '2024-01-01',
            'end_date': '2024-03-31'
        }
        
        with patch.object(trading_system, 'get_historical_data') as mock_historical:
            mock_historical.return_value = historical_data
            
            # Run backtest
            backtest_result = await trading_system.run_backtest('BTC/USDT', backtest_config)
            
            # Verify backtest results
            assert backtest_result is not None
            assert 'total_return' in backtest_result
            assert 'sharpe_ratio' in backtest_result
            assert 'max_drawdown' in backtest_result
            assert 'total_trades' in backtest_result
            assert 'win_rate' in backtest_result
            
            # Verify performance metrics are reasonable
            assert isinstance(backtest_result['total_return'], (int, float))
            assert isinstance(backtest_result['total_trades'], int)
            assert 0 <= backtest_result['win_rate'] <= 1
    
    @pytest.mark.asyncio
    async def test_strategy_optimization(self, trading_system, historical_data):
        """Test strategy parameter optimization."""
        # Define parameter ranges for optimization
        param_ranges = {
            'rsi_period': [10, 14, 20],
            'rsi_oversold': [20, 30, 35],
            'rsi_overbought': [65, 70, 80],
            'stop_loss_pct': [0.02, 0.05, 0.08]
        }
        
        with patch.object(trading_system, 'get_historical_data') as mock_historical:
            mock_historical.return_value = historical_data
            
            # Run optimization
            optimization_result = await trading_system.optimize_strategy(
                'BTC/USDT',
                param_ranges,
                optimization_metric='sharpe_ratio'
            )
            
            # Verify optimization results
            assert optimization_result is not None
            assert 'best_parameters' in optimization_result
            assert 'best_performance' in optimization_result
            assert 'optimization_results' in optimization_result
            
            # Verify best parameters are within specified ranges
            best_params = optimization_result['best_parameters']
            for param, value in best_params.items():
                if param in param_ranges:
                    assert value in param_ranges[param]


@pytest.mark.e2e
@pytest.mark.slow
class TestSystemStressTest:
    """Stress tests for the complete trading system."""
    
    @pytest.mark.asyncio
    async def test_high_frequency_trading(self, trading_system, mock_exchange_client):
        """Test system performance under high-frequency trading conditions."""
        # Setup rapid market data updates
        update_count = 100
        
        async def rapid_market_updates():
            for i in range(update_count):
                mock_exchange_client.fetch_ticker.return_value = {
                    'symbol': 'BTC/USDT',
                    'last': 50000 + i * 10,  # Gradually increasing price
                    'bid': 50000 + i * 10 - 5,
                    'ask': 50000 + i * 10 + 5,
                    'volume': 1000.0,
                    'timestamp': datetime.now().timestamp() * 1000
                }
                
                # Execute trading cycle
                await trading_system.execute_trading_cycle(['BTC/USDT'])
                await asyncio.sleep(0.01)  # 10ms intervals
        
        # Measure execution time
        start_time = datetime.now()
        await rapid_market_updates()
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        
        # Verify performance (should complete within reasonable time)
        assert execution_time < 30  # Should complete within 30 seconds
        
        # Verify system stability
        system_status = await trading_system.get_system_status()
        assert system_status['status'] == 'running'
    
    @pytest.mark.asyncio
    async def test_concurrent_symbol_processing(self, trading_system, mock_exchange_client):
        """Test concurrent processing of multiple trading symbols."""
        symbols = [f'SYMBOL{i}/USDT' for i in range(20)]  # 20 concurrent symbols
        
        # Setup mock responses for all symbols
        def mock_fetch_ticker(symbol):
            return {
                'symbol': symbol,
                'last': 1000 + hash(symbol) % 1000,
                'bid': 999 + hash(symbol) % 1000,
                'ask': 1001 + hash(symbol) % 1000,
                'volume': 1000.0,
                'timestamp': datetime.now().timestamp() * 1000
            }
        
        mock_exchange_client.fetch_ticker.side_effect = mock_fetch_ticker
        
        # Execute concurrent processing
        start_time = datetime.now()
        result = await trading_system.execute_trading_cycle(symbols)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        
        # Verify concurrent processing efficiency
        assert execution_time < 60  # Should complete within 1 minute
        assert result is not None
        
        # Verify all symbols were processed
        if isinstance(result.get('market_data'), dict):
            processed_count = len(result['market_data'])
            assert processed_count == len(symbols)
