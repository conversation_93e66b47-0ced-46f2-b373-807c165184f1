"""
Integration Tests for Multi-Agent Crypto Trading System
Tests end-to-end functionality across all system layers
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crypto_trading_system.core.database_manager import DatabaseManager
from crypto_trading_system.core.system_manager import SystemManager
from crypto_trading_system.config.system_config import get_development_config
from crypto_trading_system.core.messages import (
    IntelligenceDataMessage, TrendSignalMessage, StrategyMessage,
    TradeSignalMessage, RiskAlertMessage
)


@pytest.fixture
async def db_manager():
    """Database manager fixture"""
    config = get_development_config()
    
    # Skip tests if no Supabase key
    if not config.get("supabase_key"):
        pytest.skip("SUPABASE_ANON_KEY not set")
    
    db_manager = DatabaseManager(
        supabase_url=config["supabase_url"],
        supabase_key=config["supabase_key"]
    )
    
    yield db_manager
    
    # Cleanup test data
    try:
        await db_manager._client.table("agents").delete().eq("agent_name", "TestAgent").execute()
        await db_manager._client.table("web_intelligence").delete().eq("platform", "test").execute()
        await db_manager._client.table("strategies").delete().like("strategy_name", "%Test%").execute()
    except:
        pass


@pytest.fixture
async def system_manager():
    """System manager fixture"""
    config = get_development_config()
    
    if not config.get("supabase_key"):
        pytest.skip("SUPABASE_ANON_KEY not set")
    
    system_manager = SystemManager(config)
    await system_manager.initialize()
    
    yield system_manager
    
    await system_manager.cleanup()


class TestDatabaseIntegration:
    """Test database integration functionality"""
    
    @pytest.mark.asyncio
    async def test_agent_registration(self, db_manager):
        """Test agent registration and retrieval"""
        # Register test agent
        agent_id = await db_manager.register_agent(
            agent_name="TestAgent",
            agent_type="test",
            layer="test_layer",
            configuration={"test": True}
        )
        
        assert agent_id is not None
        
        # Retrieve agent status
        status = await db_manager.get_agent_status("TestAgent")
        assert status is not None
        assert status["agent_name"] == "TestAgent"
        assert status["agent_type"] == "test"
        assert status["layer"] == "test_layer"
    
    @pytest.mark.asyncio
    async def test_message_logging(self, db_manager):
        """Test message logging and retrieval"""
        # Create test message
        message = IntelligenceDataMessage(
            source_agent="TestAgent",
            data_type="test_data",
            data={"test": "value"},
            confidence_score=0.9
        )
        
        # Log message
        message_id = await db_manager.log_message(message)
        assert message_id is not None
        
        # Retrieve messages
        messages = await db_manager.get_agent_messages("TestAgent", limit=1)
        assert len(messages) >= 1
        assert messages[0]["message_type"] == "IntelligenceDataMessage"
    
    @pytest.mark.asyncio
    async def test_intelligence_data_flow(self, db_manager):
        """Test intelligence data storage and retrieval"""
        # Store web intelligence
        intel_id = await db_manager.store_web_intelligence(
            platform="test",
            content="Test intelligence content",
            sentiment_score=0.7,
            confidence_score=0.8
        )
        assert intel_id is not None
        
        # Store whale activity
        whale_id = await db_manager.store_whale_activity(
            transaction_hash="0xtest123",
            from_address="0xfrom",
            to_address="0xto",
            token_symbol="TEST",
            amount=1000.0,
            usd_value=50000.0
        )
        assert whale_id is not None
        
        # Retrieve recent intelligence
        intelligence = await db_manager.get_recent_intelligence(limit=5)
        assert "web_intelligence" in intelligence
        assert len(intelligence["web_intelligence"]) >= 1
        assert "whale_activity" in intelligence
        assert len(intelligence["whale_activity"]) >= 1
    
    @pytest.mark.asyncio
    async def test_strategy_lifecycle(self, db_manager):
        """Test complete strategy lifecycle"""
        # Create strategy
        strategy_id = await db_manager.store_strategy(
            strategy_name="Test Strategy",
            strategy_type="test",
            description="Test strategy for integration testing",
            parameters={"test_param": 1.0}
        )
        assert strategy_id is not None
        
        # Create backtest result
        backtest_id = await db_manager.store_backtest_result(
            strategy_id=strategy_id,
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc),
            total_return=0.15,
            sharpe_ratio=1.2,
            max_drawdown=0.08,
            win_rate=0.65
        )
        assert backtest_id is not None
        
        # Create deployment
        deployment_id = await db_manager.create_strategy_deployment(
            strategy_id=strategy_id,
            deployment_name="Test Deployment",
            allocated_capital=10000.0
        )
        assert deployment_id is not None
        
        # Get active deployments
        deployments = await db_manager.get_active_deployments()
        assert len(deployments) >= 1
        assert any(d["deployment_id"] == deployment_id for d in deployments)


class TestSystemIntegration:
    """Test system-level integration"""
    
    @pytest.mark.asyncio
    async def test_system_initialization(self, system_manager):
        """Test system initialization"""
        status = system_manager.get_system_status()
        
        assert status["initialized"] is True
        assert status["database_connected"] is True
        assert status["agent_count"] >= 0
        assert "agents" in status
    
    @pytest.mark.asyncio
    async def test_agent_communication_flow(self, system_manager):
        """Test agent communication through database"""
        db_manager = system_manager.get_database_manager()
        
        # Simulate intelligence collection
        intel_message = IntelligenceDataMessage(
            source_agent="WebIntelligenceAgent",
            data_type="social_sentiment",
            data={"platform": "twitter", "sentiment": 0.8},
            confidence_score=0.9
        )
        
        message_id = await db_manager.log_message(intel_message)
        assert message_id is not None
        
        # Simulate trend analysis
        trend_message = TrendSignalMessage(
            source_agent="TrendAggregationAgent",
            signal_type="momentum",
            symbol="BTC",
            direction="bullish",
            strength=0.75,
            timeframe="4h"
        )
        
        trend_id = await db_manager.log_message(trend_message)
        assert trend_id is not None
        
        # Verify message flow
        messages = await db_manager.get_recent_messages(limit=10)
        message_types = [msg["message_type"] for msg in messages]
        
        assert "IntelligenceDataMessage" in message_types
        assert "TrendSignalMessage" in message_types


class TestPerformanceIntegration:
    """Test system performance under load"""
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, db_manager):
        """Test concurrent database operations"""
        # Create multiple concurrent operations
        tasks = []
        
        for i in range(10):
            task = db_manager.store_web_intelligence(
                platform="performance_test",
                content=f"Performance test content {i}",
                sentiment_score=0.5 + (i * 0.05)
            )
            tasks.append(task)
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all operations succeeded
        successful_operations = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_operations) == 10
        
        # Cleanup
        await db_manager._client.table("web_intelligence").delete().eq("platform", "performance_test").execute()
    
    @pytest.mark.asyncio
    async def test_bulk_data_processing(self, db_manager):
        """Test bulk data processing capabilities"""
        # Store multiple trend signals
        signal_ids = []
        
        for i in range(20):
            signal_id = await db_manager.store_trend_signal(
                signal_type="test_bulk",
                symbol=f"TEST{i}",
                timeframe="1h",
                signal_strength=0.5 + (i * 0.02),
                direction="bullish" if i % 2 == 0 else "bearish"
            )
            signal_ids.append(signal_id)
        
        assert len(signal_ids) == 20
        assert all(sid is not None for sid in signal_ids)
        
        # Query bulk data
        signals = await db_manager._client.table("trend_signals").select("*").eq("signal_type", "test_bulk").execute()
        assert len(signals.data) == 20
        
        # Cleanup
        await db_manager._client.table("trend_signals").delete().eq("signal_type", "test_bulk").execute()


class TestErrorHandling:
    """Test error handling and recovery"""
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self, db_manager):
        """Test database error handling"""
        # Test duplicate agent registration
        await db_manager.register_agent("DuplicateTest", "test", "test")
        
        # Should handle duplicate gracefully
        try:
            await db_manager.register_agent("DuplicateTest", "test", "test")
        except Exception as e:
            # Should be a handled exception, not a crash
            assert "already exists" in str(e).lower() or "duplicate" in str(e).lower()
        
        # Cleanup
        await db_manager._client.table("agents").delete().eq("agent_name", "DuplicateTest").execute()
    
    @pytest.mark.asyncio
    async def test_invalid_data_handling(self, db_manager):
        """Test handling of invalid data"""
        # Test with invalid confidence score
        with pytest.raises((ValueError, Exception)):
            await db_manager.store_web_intelligence(
                platform="test",
                content="Test content",
                confidence_score=1.5  # Invalid: > 1.0
            )
        
        # Test with missing required fields
        with pytest.raises((ValueError, Exception)):
            await db_manager.store_whale_activity(
                transaction_hash="",  # Invalid: empty hash
                from_address="0xfrom",
                to_address="0xto",
                token_symbol="TEST",
                amount=1000.0,
                usd_value=50000.0
            )


# Test configuration
@pytest.mark.asyncio
async def test_system_configuration():
    """Test system configuration validation"""
    from crypto_trading_system.config.system_config import get_development_config, validate_config
    
    config = get_development_config()
    
    # Should not raise exception
    assert validate_config(config) is True
    
    # Check required components
    assert "agent_configs" in config
    assert "model_configs" in config
    assert "supabase_url" in config
    
    # Check agent count
    assert len(config["agent_configs"]) == 16
    
    # Check all layers represented
    layers = set()
    for agent_config in config["agent_configs"].values():
        # Extract layer from agent configuration or description
        if "intelligence" in agent_config.get("description", "").lower():
            layers.add("intelligence_collection")
        elif "market" in agent_config.get("description", "").lower() or "analysis" in agent_config.get("description", "").lower():
            layers.add("market_analysis")
        elif "strategy" in agent_config.get("description", "").lower() or "backtest" in agent_config.get("description", "").lower():
            layers.add("strategy_generation")
        elif "execution" in agent_config.get("description", "").lower() or "risk" in agent_config.get("description", "").lower() or "performance" in agent_config.get("description", "").lower() or "memory" in agent_config.get("description", "").lower():
            layers.add("execution_risk_management")
        elif "optimization" in agent_config.get("description", "").lower() or "notification" in agent_config.get("description", "").lower():
            layers.add("control_interaction")
    
    # Should have all 5 layers
    assert len(layers) == 5


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
