"""
Unit Tests for Database Manager
Tests database operations and data validation
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crypto_trading_system.core.database_manager import DatabaseManager
from crypto_trading_system.core.messages import IntelligenceDataMessage, TrendSignalMessage


class TestDatabaseManager:
    """Test DatabaseManager functionality"""
    
    @pytest.fixture
    def mock_supabase_client(self):
        """Mock Supabase client"""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        
        # Mock successful responses
        mock_response = Mock()
        mock_response.data = [{"id": "test-id", "agent_name": "TestAgent"}]
        mock_response.count = 1
        
        mock_table.insert.return_value.execute = AsyncMock(return_value=mock_response)
        mock_table.select.return_value.execute = AsyncMock(return_value=mock_response)
        mock_table.update.return_value.eq.return_value.execute = AsyncMock(return_value=mock_response)
        mock_table.delete.return_value.eq.return_value.execute = AsyncMock(return_value=mock_response)
        
        return mock_client
    
    @pytest.fixture
    def db_manager(self, mock_supabase_client):
        """Database manager with mocked client"""
        with patch('crypto_trading_system.core.database_manager.create_client', return_value=mock_supabase_client):
            manager = DatabaseManager("http://test.supabase.co", "test-key")
            return manager
    
    @pytest.mark.asyncio
    async def test_register_agent(self, db_manager):
        """Test agent registration"""
        agent_id = await db_manager.register_agent(
            agent_name="TestAgent",
            agent_type="test",
            layer="test_layer",
            configuration={"test": True}
        )
        
        assert agent_id is not None
        
        # Verify the client was called correctly
        db_manager._client.table.assert_called_with("agents")
    
    @pytest.mark.asyncio
    async def test_log_message(self, db_manager):
        """Test message logging"""
        message = IntelligenceDataMessage(
            source_agent="TestAgent",
            data_type="test_data",
            data={"test": "value"},
            confidence_score=0.9
        )
        
        message_id = await db_manager.log_message(message)
        
        assert message_id is not None
        db_manager._client.table.assert_called_with("messages")
    
    @pytest.mark.asyncio
    async def test_store_web_intelligence(self, db_manager):
        """Test web intelligence storage"""
        intel_id = await db_manager.store_web_intelligence(
            platform="twitter",
            content="Test content",
            sentiment_score=0.8,
            confidence_score=0.9
        )
        
        assert intel_id is not None
        db_manager._client.table.assert_called_with("web_intelligence")
    
    @pytest.mark.asyncio
    async def test_store_whale_activity(self, db_manager):
        """Test whale activity storage"""
        whale_id = await db_manager.store_whale_activity(
            transaction_hash="0xtest123",
            from_address="0xfrom",
            to_address="0xto",
            token_symbol="ETH",
            amount=1000.0,
            usd_value=2500000.0
        )
        
        assert whale_id is not None
        db_manager._client.table.assert_called_with("whale_activity")
    
    @pytest.mark.asyncio
    async def test_store_strategy(self, db_manager):
        """Test strategy storage"""
        strategy_id = await db_manager.store_strategy(
            strategy_name="Test Strategy",
            strategy_type="momentum",
            description="Test strategy"
        )
        
        assert strategy_id is not None
        db_manager._client.table.assert_called_with("strategies")
    
    @pytest.mark.asyncio
    async def test_data_validation(self, db_manager):
        """Test data validation"""
        # Test invalid confidence score
        with pytest.raises(ValueError):
            await db_manager.store_web_intelligence(
                platform="test",
                content="Test",
                confidence_score=1.5  # Invalid: > 1.0
            )
        
        # Test invalid sentiment score
        with pytest.raises(ValueError):
            await db_manager.store_web_intelligence(
                platform="test",
                content="Test",
                sentiment_score=-2.0  # Invalid: < -1.0
            )
    
    @pytest.mark.asyncio
    async def test_get_agent_status(self, db_manager):
        """Test agent status retrieval"""
        status = await db_manager.get_agent_status("TestAgent")
        
        assert status is not None
        db_manager._client.table.assert_called_with("agents")
    
    @pytest.mark.asyncio
    async def test_get_recent_intelligence(self, db_manager):
        """Test recent intelligence retrieval"""
        intelligence = await db_manager.get_recent_intelligence(limit=5)
        
        assert isinstance(intelligence, dict)
        assert "web_intelligence" in intelligence
        assert "whale_activity" in intelligence
        assert "tvl_data" in intelligence
        assert "meme_trends" in intelligence
    
    @pytest.mark.asyncio
    async def test_update_agent_heartbeat(self, db_manager):
        """Test agent heartbeat update"""
        success = await db_manager.update_agent_heartbeat("TestAgent")
        
        assert success is True
        db_manager._client.table.assert_called_with("agents")


class TestDataValidation:
    """Test data validation functions"""
    
    def test_validate_confidence_score(self):
        """Test confidence score validation"""
        from crypto_trading_system.core.database_manager import DatabaseManager
        
        # Valid scores
        assert DatabaseManager._validate_confidence_score(0.0) == 0.0
        assert DatabaseManager._validate_confidence_score(0.5) == 0.5
        assert DatabaseManager._validate_confidence_score(1.0) == 1.0
        
        # Invalid scores
        with pytest.raises(ValueError):
            DatabaseManager._validate_confidence_score(-0.1)
        
        with pytest.raises(ValueError):
            DatabaseManager._validate_confidence_score(1.1)
    
    def test_validate_sentiment_score(self):
        """Test sentiment score validation"""
        from crypto_trading_system.core.database_manager import DatabaseManager
        
        # Valid scores
        assert DatabaseManager._validate_sentiment_score(-1.0) == -1.0
        assert DatabaseManager._validate_sentiment_score(0.0) == 0.0
        assert DatabaseManager._validate_sentiment_score(1.0) == 1.0
        
        # Invalid scores
        with pytest.raises(ValueError):
            DatabaseManager._validate_sentiment_score(-1.1)
        
        with pytest.raises(ValueError):
            DatabaseManager._validate_sentiment_score(1.1)
    
    def test_validate_required_fields(self):
        """Test required field validation"""
        from crypto_trading_system.core.database_manager import DatabaseManager
        
        # Valid fields
        DatabaseManager._validate_required_string("test", "field_name")
        
        # Invalid fields
        with pytest.raises(ValueError):
            DatabaseManager._validate_required_string("", "field_name")
        
        with pytest.raises(ValueError):
            DatabaseManager._validate_required_string(None, "field_name")
        
        with pytest.raises(ValueError):
            DatabaseManager._validate_required_string("   ", "field_name")


class TestMessageHandling:
    """Test message handling functionality"""
    
    def test_message_serialization(self):
        """Test message serialization for database storage"""
        message = IntelligenceDataMessage(
            source_agent="TestAgent",
            data_type="test_data",
            data={"test": "value"},
            confidence_score=0.9
        )
        
        # Should be able to convert to dict for database storage
        message_dict = {
            "message_type": message.__class__.__name__,
            "source_agent": message.source_agent,
            "payload": {
                "data_type": message.data_type,
                "data": message.data,
                "confidence_score": message.confidence_score
            }
        }
        
        assert message_dict["message_type"] == "IntelligenceDataMessage"
        assert message_dict["source_agent"] == "TestAgent"
        assert message_dict["payload"]["confidence_score"] == 0.9
    
    def test_trend_signal_message(self):
        """Test trend signal message handling"""
        message = TrendSignalMessage(
            source_agent="TrendAgent",
            signal_type="momentum",
            symbol="BTC",
            direction="bullish",
            strength=0.75,
            timeframe="4h"
        )
        
        assert message.source_agent == "TrendAgent"
        assert message.signal_type == "momentum"
        assert message.symbol == "BTC"
        assert message.direction == "bullish"
        assert message.strength == 0.75
        assert message.timeframe == "4h"


class TestErrorHandling:
    """Test error handling in database operations"""
    
    @pytest.fixture
    def failing_db_manager(self):
        """Database manager that simulates failures"""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        
        # Mock failing responses
        mock_table.insert.return_value.execute = AsyncMock(side_effect=Exception("Database error"))
        mock_table.select.return_value.execute = AsyncMock(side_effect=Exception("Database error"))
        
        with patch('crypto_trading_system.core.database_manager.create_client', return_value=mock_client):
            manager = DatabaseManager("http://test.supabase.co", "test-key")
            return manager
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self, failing_db_manager):
        """Test handling of database errors"""
        with pytest.raises(Exception):
            await failing_db_manager.register_agent("TestAgent", "test", "test")
        
        with pytest.raises(Exception):
            await failing_db_manager.store_web_intelligence("test", "content")
    
    @pytest.mark.asyncio
    async def test_connection_error_handling(self):
        """Test handling of connection errors"""
        # Test with invalid URL
        with pytest.raises(Exception):
            DatabaseManager("invalid-url", "test-key")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
