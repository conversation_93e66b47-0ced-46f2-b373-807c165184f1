"""
Integration tests for core system functionality.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import json


@pytest.mark.integration
class TestCoreIntegration:
    """Test cases for core system integration."""
    
    def test_data_flow_integration(self):
        """Test data flow between layers."""
        # Simulate data flow from intelligence to analysis to strategy
        
        # Intelligence layer output
        intelligence_data = {
            'market_data': {
                'symbol': 'BTC/USDT',
                'price': 50000.0,
                'volume': 1000000.0,
                'timestamp': datetime.now()
            },
            'sentiment': {
                'score': 0.7,
                'confidence': 0.85,
                'source': 'twitter'
            },
            'whale_activity': {
                'large_transactions': 5,
                'net_flow': 100.5,
                'timestamp': datetime.now()
            }
        }
        
        # Analysis layer processing
        def process_market_analysis(data):
            price = data['market_data']['price']
            volume = data['market_data']['volume']
            sentiment = data['sentiment']['score']
            
            # Simple technical analysis
            rsi = 30 if price < 48000 else 70  # Simplified RSI
            macd = 0.5 if sentiment > 0.5 else -0.5  # Simplified MACD
            
            return {
                'rsi': rsi,
                'macd': macd,
                'trend': 'bullish' if sentiment > 0.6 else 'bearish',
                'volume_profile': 'high' if volume > 500000 else 'low'
            }
        
        analysis_result = process_market_analysis(intelligence_data)
        
        # Strategy layer signal generation
        def generate_trading_signal(analysis):
            if analysis['rsi'] < 35 and analysis['macd'] > 0:
                return {'action': 'BUY', 'confidence': 0.8}
            elif analysis['rsi'] > 65 and analysis['macd'] < 0:
                return {'action': 'SELL', 'confidence': 0.8}
            else:
                return {'action': 'HOLD', 'confidence': 0.5}
        
        signal = generate_trading_signal(analysis_result)
        
        # Verify integration
        assert 'action' in signal
        assert signal['action'] in ['BUY', 'SELL', 'HOLD']
        assert 0 <= signal['confidence'] <= 1
        assert analysis_result['rsi'] in [30, 70]
        assert analysis_result['macd'] in [0.5, -0.5]
    
    def test_message_passing_integration(self):
        """Test message passing between components."""
        # Simulate AutoGen message passing
        
        def create_message(sender, recipient, content, message_type='data'):
            return {
                'sender': sender,
                'recipient': recipient,
                'content': content,
                'type': message_type,
                'timestamp': datetime.now().isoformat()
            }
        
        # Create sample messages
        market_data_message = create_message(
            'market_data_collector',
            'technical_analyzer',
            {
                'symbol': 'BTC/USDT',
                'ohlcv': [50000, 51000, 49500, 50500, 1000000],
                'timestamp': datetime.now().isoformat()
            }
        )
        
        analysis_message = create_message(
            'technical_analyzer',
            'signal_generator',
            {
                'rsi': 35,
                'macd': 0.2,
                'bollinger_position': 'lower',
                'trend': 'bullish'
            }
        )
        
        signal_message = create_message(
            'signal_generator',
            'trade_executor',
            {
                'action': 'BUY',
                'symbol': 'BTC/USDT',
                'confidence': 0.75,
                'reasoning': 'Oversold RSI with bullish MACD'
            }
        )
        
        # Verify message structure
        for message in [market_data_message, analysis_message, signal_message]:
            assert 'sender' in message
            assert 'recipient' in message
            assert 'content' in message
            assert 'type' in message
            assert 'timestamp' in message
            assert isinstance(message['content'], dict)
    
    def test_error_handling_integration(self):
        """Test error handling across components."""
        
        def process_with_error_handling(data, component_name):
            try:
                if not data:
                    raise ValueError(f"{component_name}: No data provided")
                
                if 'price' not in data:
                    raise KeyError(f"{component_name}: Missing price data")
                
                # Simulate processing
                result = {
                    'processed_by': component_name,
                    'status': 'success',
                    'data': data,
                    'timestamp': datetime.now().isoformat()
                }
                
                return result
                
            except Exception as e:
                return {
                    'processed_by': component_name,
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
        
        # Test successful processing
        valid_data = {'price': 50000, 'volume': 1000000}
        result = process_with_error_handling(valid_data, 'test_component')
        assert result['status'] == 'success'
        assert result['processed_by'] == 'test_component'
        
        # Test error handling
        invalid_data = {'volume': 1000000}  # Missing price
        error_result = process_with_error_handling(invalid_data, 'test_component')
        assert error_result['status'] == 'error'
        assert 'Missing price data' in error_result['error']
        
        # Test empty data handling
        empty_result = process_with_error_handling(None, 'test_component')
        assert empty_result['status'] == 'error'
        assert 'No data provided' in empty_result['error']
    
    def test_configuration_integration(self):
        """Test configuration management across components."""
        
        # Sample system configuration
        system_config = {
            'trading': {
                'max_position_size': 0.1,
                'stop_loss_percentage': 0.05,
                'take_profit_percentage': 0.15,
                'risk_per_trade': 0.02
            },
            'analysis': {
                'rsi_period': 14,
                'macd_fast': 12,
                'macd_slow': 26,
                'bollinger_period': 20
            },
            'data': {
                'update_interval': 60,
                'symbols': ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
                'timeframes': ['1m', '5m', '1h', '1d']
            }
        }
        
        def validate_config(config, component):
            required_sections = {
                'trading_component': ['trading'],
                'analysis_component': ['analysis'],
                'data_component': ['data']
            }
            
            if component in required_sections:
                for section in required_sections[component]:
                    if section not in config:
                        return False, f"Missing {section} configuration"
            
            return True, "Configuration valid"
        
        # Test configuration validation
        is_valid, message = validate_config(system_config, 'trading_component')
        assert is_valid
        assert message == "Configuration valid"
        
        # Test missing configuration
        incomplete_config = {'trading': system_config['trading']}
        is_valid, message = validate_config(incomplete_config, 'analysis_component')
        assert not is_valid
        assert 'Missing analysis configuration' in message
        
        # Verify configuration values
        assert system_config['trading']['max_position_size'] == 0.1
        assert system_config['analysis']['rsi_period'] == 14
        assert len(system_config['data']['symbols']) == 3
