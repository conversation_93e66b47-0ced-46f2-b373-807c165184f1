"""
Integration tests for inter-agent communication and data flow.
"""
import pytest
import asyncio
from unittest.mock import Mo<PERSON>, Async<PERSON>ock, patch
from datetime import datetime, timedelta
import json

from autogen_core import SingleThreadedAgentRuntime, TypeSubscription
from crypto_trading_system.agents.intelligence_collection.market_data_collector import MarketDataCollectorAgent
from crypto_trading_system.agents.market_analysis.technical_analyst import TechnicalAnalystAgent
from crypto_trading_system.agents.strategy_generation.signal_generator import Signal<PERSON>eneratorAgent
from crypto_trading_system.agents.execution.portfolio_manager import PortfolioManagerAgent
from crypto_trading_system.agents.control.system_coordinator import SystemCoordinatorAgent


@pytest.mark.integration
class TestAgentCommunication:
    """Test inter-agent communication patterns."""
    
    @pytest.fixture
    async def runtime(self):
        """Create and start AutoGen runtime for testing."""
        runtime = SingleThreadedAgentRuntime()
        await runtime.start()
        yield runtime
        await runtime.stop()
    
    @pytest.fixture
    async def agent_system(self, runtime, mock_supabase_client, mock_exchange_client, mock_ai_clients):
        """Set up a complete agent system for integration testing."""
        # Create agents
        market_data_agent = MarketDataCollectorAgent(
            runtime=runtime,
            supabase_client=mock_supabase_client
        )
        
        technical_analyst = TechnicalAnalystAgent(
            runtime=runtime,
            supabase_client=mock_supabase_client
        )
        
        signal_generator = SignalGeneratorAgent(
            runtime=runtime,
            ai_clients=mock_ai_clients,
            supabase_client=mock_supabase_client
        )
        
        portfolio_manager = PortfolioManagerAgent(
            runtime=runtime,
            supabase_client=mock_supabase_client
        )
        
        system_coordinator = SystemCoordinatorAgent(
            runtime=runtime,
            supabase_client=mock_supabase_client
        )
        
        # Register agents
        await runtime.register(
            "market_data_collector",
            lambda: market_data_agent,
            TypeSubscription("market_data_request")
        )
        
        await runtime.register(
            "technical_analyst",
            lambda: technical_analyst,
            TypeSubscription("analysis_request")
        )
        
        await runtime.register(
            "signal_generator",
            lambda: signal_generator,
            TypeSubscription("signal_request")
        )
        
        await runtime.register(
            "portfolio_manager",
            lambda: portfolio_manager,
            TypeSubscription("portfolio_update")
        )
        
        await runtime.register(
            "system_coordinator",
            lambda: system_coordinator,
            TypeSubscription("system_command")
        )
        
        return {
            'market_data_agent': market_data_agent,
            'technical_analyst': technical_analyst,
            'signal_generator': signal_generator,
            'portfolio_manager': portfolio_manager,
            'system_coordinator': system_coordinator
        }
    
    @pytest.mark.asyncio
    async def test_data_flow_pipeline(self, agent_system, runtime, sample_market_data):
        """Test complete data flow from collection to execution."""
        # Mock market data collection
        with patch.object(agent_system['market_data_agent'], 'collect_market_data') as mock_collect:
            mock_collect.return_value = sample_market_data.to_dict('records')
            
            # Mock technical analysis
            with patch.object(agent_system['technical_analyst'], 'analyze_market_data') as mock_analyze:
                mock_analyze.return_value = {
                    'signals': [{'action': 'BUY', 'confidence': 0.8, 'symbol': 'BTC/USDT'}],
                    'indicators': {'rsi': 30, 'macd': 0.5},
                    'confidence': 0.8
                }
                
                # Mock signal generation
                with patch.object(agent_system['signal_generator'], 'generate_trading_signals') as mock_signals:
                    mock_signals.return_value = {
                        'signals': [{
                            'symbol': 'BTC/USDT',
                            'action': 'BUY',
                            'quantity': 0.1,
                            'confidence': 0.85,
                            'timestamp': datetime.now()
                        }]
                    }
                    
                    # Simulate data flow
                    market_data = await agent_system['market_data_agent'].collect_market_data(['BTC/USDT'])
                    analysis_result = await agent_system['technical_analyst'].analyze_market_data(market_data)
                    signals = await agent_system['signal_generator'].generate_trading_signals(analysis_result)
                    
                    # Verify data flow
                    assert market_data is not None
                    assert analysis_result is not None
                    assert signals is not None
                    assert len(signals['signals']) > 0
                    
                    # Verify signal structure
                    signal = signals['signals'][0]
                    assert 'symbol' in signal
                    assert 'action' in signal
                    assert 'confidence' in signal
    
    @pytest.mark.asyncio
    async def test_message_passing(self, agent_system, runtime):
        """Test message passing between agents."""
        coordinator = agent_system['system_coordinator']
        
        # Mock message handling
        with patch.object(coordinator, 'handle_market_update') as mock_handler:
            mock_handler.return_value = {'status': 'processed'}
            
            # Send test message
            message = {
                'type': 'market_update',
                'data': {
                    'symbol': 'BTC/USDT',
                    'price': 50000,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            # Simulate message sending
            result = await coordinator.handle_market_update(message)
            
            assert result is not None
            assert result['status'] == 'processed'
            mock_handler.assert_called_once_with(message)
    
    @pytest.mark.asyncio
    async def test_error_propagation(self, agent_system, runtime):
        """Test error handling and propagation between agents."""
        market_agent = agent_system['market_data_agent']
        
        # Mock an error in data collection
        with patch.object(market_agent, 'collect_market_data') as mock_collect:
            mock_collect.side_effect = Exception("API Error")
            
            # Test error handling
            try:
                await market_agent.collect_market_data(['BTC/USDT'])
                assert False, "Expected exception was not raised"
            except Exception as e:
                assert str(e) == "API Error"
    
    @pytest.mark.asyncio
    async def test_concurrent_agent_operations(self, agent_system, runtime):
        """Test concurrent operations between multiple agents."""
        market_agent = agent_system['market_data_agent']
        analyst = agent_system['technical_analyst']
        
        # Mock concurrent operations
        with patch.object(market_agent, 'collect_market_data') as mock_collect:
            with patch.object(analyst, 'analyze_market_data') as mock_analyze:
                mock_collect.return_value = {'BTC/USDT': []}
                mock_analyze.return_value = {'signals': []}
                
                # Run concurrent operations
                tasks = [
                    market_agent.collect_market_data(['BTC/USDT']),
                    market_agent.collect_market_data(['ETH/USDT']),
                    analyst.analyze_market_data({'test': 'data'})
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify all operations completed
                assert len(results) == 3
                assert all(not isinstance(result, Exception) for result in results)
    
    @pytest.mark.asyncio
    async def test_state_synchronization(self, agent_system, runtime):
        """Test state synchronization between agents."""
        portfolio_manager = agent_system['portfolio_manager']
        
        # Mock portfolio state
        initial_state = {
            'balance': 10000,
            'positions': {},
            'last_update': datetime.now()
        }
        
        with patch.object(portfolio_manager, 'get_portfolio_state') as mock_get_state:
            with patch.object(portfolio_manager, 'update_portfolio_state') as mock_update_state:
                mock_get_state.return_value = initial_state
                mock_update_state.return_value = True
                
                # Test state operations
                current_state = await portfolio_manager.get_portfolio_state()
                assert current_state == initial_state
                
                # Update state
                new_state = current_state.copy()
                new_state['balance'] = 9500
                new_state['positions'] = {'BTC/USDT': 0.1}
                
                update_result = await portfolio_manager.update_portfolio_state(new_state)
                assert update_result is True


@pytest.mark.integration
class TestDataPersistence:
    """Test data persistence and retrieval across agents."""
    
    @pytest.fixture
    def mock_database(self):
        """Mock database for testing persistence."""
        db_data = {}
        
        class MockDB:
            def insert(self, table, data):
                if table not in db_data:
                    db_data[table] = []
                db_data[table].append(data)
                return {'id': len(db_data[table])}
            
            def select(self, table, conditions=None):
                if table not in db_data:
                    return []
                data = db_data[table]
                if conditions:
                    # Simple filtering for testing
                    filtered_data = []
                    for item in data:
                        match = True
                        for key, value in conditions.items():
                            if item.get(key) != value:
                                match = False
                                break
                        if match:
                            filtered_data.append(item)
                    return filtered_data
                return data
            
            def update(self, table, data, conditions):
                if table in db_data:
                    for item in db_data[table]:
                        match = True
                        for key, value in conditions.items():
                            if item.get(key) != value:
                                match = False
                                break
                        if match:
                            item.update(data)
                return True
        
        return MockDB()
    
    @pytest.mark.asyncio
    async def test_market_data_persistence(self, mock_database):
        """Test market data storage and retrieval."""
        # Test data insertion
        market_data = {
            'symbol': 'BTC/USDT',
            'price': 50000,
            'volume': 1000,
            'timestamp': datetime.now().isoformat()
        }
        
        result = mock_database.insert('market_data', market_data)
        assert result['id'] == 1
        
        # Test data retrieval
        retrieved_data = mock_database.select('market_data', {'symbol': 'BTC/USDT'})
        assert len(retrieved_data) == 1
        assert retrieved_data[0]['price'] == 50000
    
    @pytest.mark.asyncio
    async def test_signal_persistence(self, mock_database):
        """Test trading signal storage and retrieval."""
        # Test signal storage
        signal_data = {
            'symbol': 'BTC/USDT',
            'action': 'BUY',
            'confidence': 0.85,
            'timestamp': datetime.now().isoformat(),
            'agent_id': 'signal_generator'
        }
        
        result = mock_database.insert('trading_signals', signal_data)
        assert result['id'] == 1
        
        # Test signal retrieval
        signals = mock_database.select('trading_signals', {'symbol': 'BTC/USDT'})
        assert len(signals) == 1
        assert signals[0]['action'] == 'BUY'
    
    @pytest.mark.asyncio
    async def test_portfolio_state_persistence(self, mock_database):
        """Test portfolio state storage and updates."""
        # Initial portfolio state
        portfolio_state = {
            'user_id': 'test_user',
            'balance': 10000,
            'positions': json.dumps({'BTC/USDT': 0.1}),
            'last_update': datetime.now().isoformat()
        }
        
        # Insert initial state
        result = mock_database.insert('portfolio_states', portfolio_state)
        assert result['id'] == 1
        
        # Update portfolio state
        updated_data = {
            'balance': 9500,
            'positions': json.dumps({'BTC/USDT': 0.15}),
            'last_update': datetime.now().isoformat()
        }
        
        update_result = mock_database.update(
            'portfolio_states',
            updated_data,
            {'user_id': 'test_user'}
        )
        assert update_result is True
        
        # Verify update
        updated_portfolio = mock_database.select('portfolio_states', {'user_id': 'test_user'})
        assert len(updated_portfolio) == 1
        assert updated_portfolio[0]['balance'] == 9500
