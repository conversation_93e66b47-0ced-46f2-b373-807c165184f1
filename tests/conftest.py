"""
Pytest configuration and shared fixtures for the crypto trading system tests.
"""
import pytest
import asyncio
import os
import tempfile
import shutil
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any, List
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Test environment setup
os.environ['TESTING'] = 'true'
os.environ['LOG_LEVEL'] = 'DEBUG'

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing."""
    mock_client = Mock()
    mock_client.table.return_value.insert.return_value.execute.return_value = Mock(data=[])
    mock_client.table.return_value.select.return_value.execute.return_value = Mock(data=[])
    mock_client.table.return_value.update.return_value.execute.return_value = Mock(data=[])
    mock_client.table.return_value.delete.return_value.execute.return_value = Mock(data=[])
    return mock_client

@pytest.fixture
def mock_ai_clients():
    """Mock AI service clients."""
    return {
        'anthropic': AsyncMock(),
        'openai': AsyncMock(),
        'gemini': AsyncMock()
    }

@pytest.fixture
def mock_exchange_client():
    """Mock cryptocurrency exchange client."""
    mock_exchange = Mock()
    mock_exchange.fetch_ticker.return_value = {
        'symbol': 'BTC/USDT',
        'last': 50000.0,
        'bid': 49950.0,
        'ask': 50050.0,
        'volume': 1000.0,
        'timestamp': datetime.now().timestamp() * 1000
    }
    mock_exchange.fetch_ohlcv.return_value = [
        [datetime.now().timestamp() * 1000, 49000, 51000, 48000, 50000, 1000]
    ]
    return mock_exchange

@pytest.fixture
def sample_market_data():
    """Generate sample market data for testing."""
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='1H')
    np.random.seed(42)  # For reproducible tests
    
    data = {
        'timestamp': dates,
        'open': 50000 + np.random.randn(len(dates)) * 1000,
        'high': 50000 + np.random.randn(len(dates)) * 1000 + 500,
        'low': 50000 + np.random.randn(len(dates)) * 1000 - 500,
        'close': 50000 + np.random.randn(len(dates)) * 1000,
        'volume': np.random.randint(100, 1000, len(dates))
    }
    
    # Ensure high >= max(open, close) and low <= min(open, close)
    for i in range(len(dates)):
        data['high'][i] = max(data['high'][i], data['open'][i], data['close'][i])
        data['low'][i] = min(data['low'][i], data['open'][i], data['close'][i])
    
    return pd.DataFrame(data)

@pytest.fixture
def sample_news_data():
    """Generate sample news data for testing."""
    return [
        {
            'title': 'Bitcoin reaches new all-time high',
            'content': 'Bitcoin has surged to unprecedented levels...',
            'source': 'CryptoNews',
            'timestamp': datetime.now() - timedelta(hours=1),
            'sentiment': 0.8,
            'relevance': 0.9
        },
        {
            'title': 'Regulatory concerns impact crypto market',
            'content': 'New regulations may affect cryptocurrency trading...',
            'source': 'FinancialTimes',
            'timestamp': datetime.now() - timedelta(hours=2),
            'sentiment': -0.3,
            'relevance': 0.7
        }
    ]

@pytest.fixture
def sample_social_data():
    """Generate sample social media data for testing."""
    return [
        {
            'platform': 'twitter',
            'content': 'Bitcoin to the moon! 🚀 #BTC #crypto',
            'author': 'crypto_enthusiast',
            'timestamp': datetime.now() - timedelta(minutes=30),
            'sentiment': 0.9,
            'engagement': 150,
            'followers': 10000
        },
        {
            'platform': 'twitter',
            'content': 'Concerned about the recent market volatility...',
            'author': 'market_analyst',
            'timestamp': datetime.now() - timedelta(minutes=45),
            'sentiment': -0.2,
            'engagement': 75,
            'followers': 50000
        }
    ]

@pytest.fixture
def temp_directory():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture
def mock_runtime():
    """Mock AutoGen runtime for testing."""
    from autogen_core import SingleThreadedAgentRuntime
    runtime = Mock(spec=SingleThreadedAgentRuntime)
    runtime.start = AsyncMock()
    runtime.stop = AsyncMock()
    runtime.send_message = AsyncMock()
    runtime.register = AsyncMock()
    return runtime

@pytest.fixture
def test_config():
    """Test configuration settings."""
    return {
        'database': {
            'url': 'sqlite:///:memory:',
            'echo': False
        },
        'trading': {
            'base_currency': 'USDT',
            'max_position_size': 0.1,
            'risk_per_trade': 0.02,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.10
        },
        'backtesting': {
            'initial_balance': 10000,
            'commission': 0.001,
            'slippage': 0.0005
        }
    }

@pytest.fixture
def mock_telegram_bot():
    """Mock Telegram bot for testing."""
    mock_bot = AsyncMock()
    mock_bot.send_message = AsyncMock()
    mock_bot.send_photo = AsyncMock()
    mock_bot.send_document = AsyncMock()
    return mock_bot

class TestDataGenerator:
    """Utility class for generating test data."""
    
    @staticmethod
    def generate_price_series(length: int = 100, start_price: float = 50000) -> List[float]:
        """Generate a realistic price series using random walk."""
        np.random.seed(42)
        returns = np.random.normal(0, 0.02, length)
        prices = [start_price]
        
        for i in range(1, length):
            price = prices[-1] * (1 + returns[i])
            prices.append(max(price, 0.01))  # Prevent negative prices
        
        return prices
    
    @staticmethod
    def generate_trading_signals(length: int = 100) -> List[Dict[str, Any]]:
        """Generate sample trading signals."""
        np.random.seed(42)
        signals = []
        
        for i in range(length):
            signal = {
                'timestamp': datetime.now() - timedelta(hours=length-i),
                'symbol': 'BTC/USDT',
                'action': np.random.choice(['BUY', 'SELL', 'HOLD'], p=[0.3, 0.3, 0.4]),
                'confidence': np.random.uniform(0.5, 1.0),
                'price': 50000 + np.random.randn() * 1000,
                'volume': np.random.uniform(0.1, 2.0),
                'reason': 'Technical analysis signal'
            }
            signals.append(signal)
        
        return signals

@pytest.fixture
def test_data_generator():
    """Provide test data generator utility."""
    return TestDataGenerator()

# Pytest markers for different test categories
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.performance = pytest.mark.performance
pytest.mark.slow = pytest.mark.slow
