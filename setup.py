"""
Setup script for Multi-Agent Crypto Trading System
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="crypto-trading-system",
    version="1.0.0",
    author="Crypto Trading System",
    author_email="<EMAIL>",
    description="Multi-Agent AI System for Cryptocurrency Trading using Microsoft AutoGen",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/crypto-trading-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "docs": [
            "sphinx>=6.0.0",
            "sphinx-rtd-theme>=1.2.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "crypto-trading-system=crypto_trading_system.main:cli_main",
            "crypto-init-db=crypto_trading_system.scripts.init_database:main",
            "crypto-test-system=crypto_trading_system.scripts.test_system:main",
        ],
    },
    include_package_data=True,
    package_data={
        "crypto_trading_system": [
            "config/*.py",
            "scripts/*.py",
            "*.sql",
        ],
    },
    zip_safe=False,
)
