-- Multi-Agent Crypto Trading System Database Schema
-- This schema supports all 16 agents across 5 layers with proper state management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ============================================================================
-- Core System Tables
-- ============================================================================

-- Agents registry and status
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_name VARCHAR(100) NOT NULL UNIQUE,
    agent_type VARCHAR(50) NOT NULL,
    layer VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'inactive',
    configuration JSONB DEFAULT '{}',
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Message log for all inter-agent communications
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id VARCHAR(100) NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    source_agent VARCHAR(100) NOT NULL,
    target_agent VARCHAR(100),
    topic_id VARCHAR(100),
    payload JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'sent',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- System configuration and settings
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Intelligence Collection Layer Tables
-- ============================================================================

-- Web intelligence data (social media, news)
CREATE TABLE web_intelligence (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    sentiment_score DECIMAL(5,4),
    engagement_metrics JSONB DEFAULT '{}',
    keywords TEXT[],
    confidence_score DECIMAL(5,4),
    source_url TEXT,
    author VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Whale activity tracking
CREATE TABLE whale_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_hash VARCHAR(100) NOT NULL UNIQUE,
    from_address VARCHAR(100) NOT NULL,
    to_address VARCHAR(100) NOT NULL,
    token_symbol VARCHAR(20) NOT NULL,
    amount DECIMAL(30,18) NOT NULL,
    usd_value DECIMAL(15,2),
    chain VARCHAR(50) NOT NULL,
    block_number BIGINT,
    transaction_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- TVL and chain inflow data
CREATE TABLE tvl_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    protocol VARCHAR(100) NOT NULL,
    chain VARCHAR(50) NOT NULL,
    tvl_usd DECIMAL(15,2) NOT NULL,
    tvl_change_24h DECIMAL(10,4),
    inflow_24h DECIMAL(15,2),
    outflow_24h DECIMAL(15,2),
    token_breakdown JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meme token trends
CREATE TABLE meme_trends (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token_symbol VARCHAR(20) NOT NULL,
    token_address VARCHAR(100),
    chain VARCHAR(50) NOT NULL,
    trend_score DECIMAL(5,4),
    social_mentions INTEGER DEFAULT 0,
    price_change_24h DECIMAL(10,4),
    volume_24h DECIMAL(15,2),
    market_cap DECIMAL(15,2),
    viral_content JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Market Analysis Layer Tables
-- ============================================================================

-- Trend signals and analysis
CREATE TABLE trend_signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    signal_type VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(20) NOT NULL,
    signal_strength DECIMAL(5,4) NOT NULL,
    direction VARCHAR(10) NOT NULL, -- 'bullish', 'bearish', 'neutral'
    confidence_score DECIMAL(5,4),
    supporting_data JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contract analysis results
CREATE TABLE contract_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contract_address VARCHAR(100) NOT NULL,
    chain VARCHAR(50) NOT NULL,
    token_symbol VARCHAR(20),
    security_score DECIMAL(5,4),
    audit_status VARCHAR(50),
    risk_factors TEXT[],
    tokenomics JSONB DEFAULT '{}',
    liquidity_analysis JSONB DEFAULT '{}',
    holder_analysis JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Strategy Generation & Backtesting Layer Tables
-- ============================================================================

-- Generated trading strategies
CREATE TABLE strategies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_name VARCHAR(100) NOT NULL UNIQUE,
    strategy_type VARCHAR(50) NOT NULL,
    description TEXT,
    parameters JSONB DEFAULT '{}',
    code TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    created_by VARCHAR(100) DEFAULT 'StrategyGenerationAgent',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Backtest results
CREATE TABLE backtest_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_id UUID REFERENCES strategies(id) ON DELETE CASCADE,
    backtest_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    backtest_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_return DECIMAL(10,4),
    sharpe_ratio DECIMAL(8,4),
    max_drawdown DECIMAL(8,4),
    win_rate DECIMAL(5,4),
    total_trades INTEGER,
    performance_metrics JSONB DEFAULT '{}',
    trade_history JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Execution & Risk Management Layer Tables
-- ============================================================================

-- Live strategy deployments
CREATE TABLE strategy_deployments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_id UUID REFERENCES strategies(id) ON DELETE CASCADE,
    deployment_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'inactive',
    allocated_capital DECIMAL(15,2),
    current_positions JSONB DEFAULT '{}',
    risk_parameters JSONB DEFAULT '{}',
    deployed_at TIMESTAMP WITH TIME ZONE,
    stopped_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trade executions
CREATE TABLE trade_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    deployment_id UUID REFERENCES strategy_deployments(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL, -- 'buy', 'sell'
    quantity DECIMAL(20,8) NOT NULL,
    price DECIMAL(15,8),
    order_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    exchange VARCHAR(50),
    order_id VARCHAR(100),
    filled_quantity DECIMAL(20,8) DEFAULT 0,
    fees DECIMAL(15,8) DEFAULT 0,
    executed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance tracking
CREATE TABLE performance_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    deployment_id UUID REFERENCES strategy_deployments(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    portfolio_value DECIMAL(15,2),
    pnl_realized DECIMAL(15,2),
    pnl_unrealized DECIMAL(15,2),
    drawdown DECIMAL(8,4),
    positions JSONB DEFAULT '{}',
    metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Risk alerts and monitoring
CREATE TABLE risk_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    deployment_id UUID REFERENCES strategy_deployments(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    current_value DECIMAL(15,8),
    threshold_value DECIMAL(15,8),
    status VARCHAR(20) DEFAULT 'active',
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Control & Interaction Layer Tables
-- ============================================================================

-- Parameter optimization history
CREATE TABLE optimization_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_id UUID REFERENCES strategies(id) ON DELETE CASCADE,
    optimization_algorithm VARCHAR(50) NOT NULL,
    original_parameters JSONB NOT NULL,
    optimized_parameters JSONB NOT NULL,
    performance_improvement DECIMAL(8,4),
    confidence_score DECIMAL(5,4),
    optimization_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification history
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_type VARCHAR(50) NOT NULL, -- 'telegram', 'slack', 'email'
    recipient VARCHAR(100) NOT NULL,
    subject VARCHAR(200),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivery_status VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions and preferences
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(100) NOT NULL,
    platform VARCHAR(20) NOT NULL, -- 'telegram', 'slack'
    session_data JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

-- Agents table indexes
CREATE INDEX idx_agents_name ON agents(agent_name);
CREATE INDEX idx_agents_type ON agents(agent_type);
CREATE INDEX idx_agents_status ON agents(status);

-- Messages table indexes
CREATE INDEX idx_messages_type ON messages(message_type);
CREATE INDEX idx_messages_source ON messages(source_agent);
CREATE INDEX idx_messages_created ON messages(created_at);
CREATE INDEX idx_messages_status ON messages(status);

-- Intelligence data indexes
CREATE INDEX idx_web_intelligence_platform ON web_intelligence(platform);
CREATE INDEX idx_web_intelligence_created ON web_intelligence(created_at);
CREATE INDEX idx_whale_activity_symbol ON whale_activity(token_symbol);
CREATE INDEX idx_whale_activity_created ON whale_activity(created_at);
CREATE INDEX idx_tvl_data_protocol ON tvl_data(protocol);
CREATE INDEX idx_tvl_data_created ON tvl_data(created_at);
CREATE INDEX idx_meme_trends_symbol ON meme_trends(token_symbol);
CREATE INDEX idx_meme_trends_created ON meme_trends(created_at);

-- Analysis data indexes
CREATE INDEX idx_trend_signals_symbol ON trend_signals(symbol);
CREATE INDEX idx_trend_signals_created ON trend_signals(created_at);
CREATE INDEX idx_contract_analysis_address ON contract_analysis(contract_address);

-- Strategy and execution indexes
CREATE INDEX idx_strategies_name ON strategies(strategy_name);
CREATE INDEX idx_strategies_status ON strategies(status);
CREATE INDEX idx_backtest_results_strategy ON backtest_results(strategy_id);
CREATE INDEX idx_deployments_status ON strategy_deployments(status);
CREATE INDEX idx_trade_executions_deployment ON trade_executions(deployment_id);
CREATE INDEX idx_trade_executions_symbol ON trade_executions(symbol);
CREATE INDEX idx_performance_data_deployment ON performance_data(deployment_id);
CREATE INDEX idx_performance_data_timestamp ON performance_data(timestamp);

-- Control layer indexes
CREATE INDEX idx_optimization_history_strategy ON optimization_history(strategy_id);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);

-- ============================================================================
-- Triggers for Updated Timestamps
-- ============================================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to relevant tables
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategies_updated_at BEFORE UPDATE ON strategies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
