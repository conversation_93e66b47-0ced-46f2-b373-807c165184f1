"""
System Configuration for Multi-Agent Crypto Trading System
Defines all agents, models, and system settings
"""

import os
from typing import Dict, Any


def get_system_config() -> Dict[str, Any]:
    """Get complete system configuration"""
    
    # Supabase configuration
    supabase_config = {
        "supabase_url": os.getenv("SUPABASE_URL", "https://bvqfowfiluzywduolpxh.supabase.co"),
        "supabase_key": os.getenv("SUPABASE_ANON_KEY", "")  # Will need to be provided
    }
    
    # Model configurations
    model_configs = {
        "default": {
            "provider": "anthropic",
            "model": "claude-3-5-sonnet-20241022",
            "api_key": os.getenv("ANTHROPIC_API_KEY", ""),
            "temperature": 0.7,
            "max_tokens": 2000
        },
        "gemini": {
            "provider": "google",
            "model": "gemini-1.5-pro",
            "api_key": os.getenv("GOOGLE_API_KEY", ""),
            "temperature": 0.7,
            "max_tokens": 2000
        },
        "gpt4": {
            "provider": "openai",
            "model": "gpt-4-turbo-preview",
            "api_key": os.getenv("OPENAI_API_KEY", ""),
            "temperature": 0.7,
            "max_tokens": 2000
        }
    }
    
    # Agent configurations
    agent_configs = {
        # ============================================================================
        # Intelligence Collection Layer (4 agents)
        # ============================================================================
        "WebIntelligenceAgent": {
            "class": "WebIntelligenceAgent",
            "model": "default",
            "description": "Monitors social media and web sources for crypto intelligence",
            "config": {
                "platforms": ["twitter", "reddit", "discord", "telegram"],
                "keywords": ["crypto", "defi", "nft", "blockchain", "trading"],
                "sentiment_analysis": True,
                "update_interval": 300,  # 5 minutes
                "max_posts_per_batch": 100
            }
        },
        
        "WhaleActivityAgent": {
            "class": "WhaleActivityAgent", 
            "model": "default",
            "description": "Tracks large cryptocurrency transactions and whale movements",
            "config": {
                "chains": ["ethereum", "bsc", "polygon", "arbitrum"],
                "min_transaction_value": 100000,  # $100k minimum
                "whale_threshold": 1000000,  # $1M for whale classification
                "update_interval": 60,  # 1 minute
                "track_tokens": ["ETH", "BTC", "USDC", "USDT"]
            }
        },
        
        "TVLMonitoringAgent": {
            "class": "TVLMonitoringAgent",
            "model": "default", 
            "description": "Monitors Total Value Locked across DeFi protocols",
            "config": {
                "protocols": ["uniswap", "aave", "compound", "makerdao", "curve"],
                "chains": ["ethereum", "bsc", "polygon", "arbitrum", "optimism"],
                "update_interval": 900,  # 15 minutes
                "alert_threshold": 0.1  # 10% change alert
            }
        },
        
        "MemeTrendAgent": {
            "class": "MemeTrendAgent",
            "model": "default",
            "description": "Tracks meme coin trends and viral content",
            "config": {
                "social_platforms": ["twitter", "tiktok", "reddit"],
                "trend_keywords": ["moon", "diamond hands", "ape", "hodl"],
                "min_market_cap": 1000000,  # $1M minimum
                "update_interval": 180,  # 3 minutes
                "viral_threshold": 1000  # minimum mentions for viral status
            }
        },
        
        # ============================================================================
        # Market Analysis Layer (2 agents)
        # ============================================================================
        "TrendAggregationAgent": {
            "class": "TrendAggregationAgent",
            "model": "gemini",
            "description": "Aggregates and analyzes market trends from multiple sources",
            "config": {
                "analysis_timeframes": ["1h", "4h", "1d", "1w"],
                "confidence_threshold": 0.7,
                "update_interval": 300,  # 5 minutes
                "max_signals_per_analysis": 50
            }
        },
        
        "ContractAnalysisAgent": {
            "class": "ContractAnalysisAgent",
            "model": "default",
            "description": "Analyzes smart contracts for security and tokenomics",
            "config": {
                "supported_chains": ["ethereum", "bsc", "polygon"],
                "security_checks": ["reentrancy", "overflow", "access_control"],
                "audit_sources": ["certik", "consensys", "openzeppelin"],
                "min_security_score": 0.6
            }
        },
        
        # ============================================================================
        # Strategy Generation & Backtesting Layer (2 agents)
        # ============================================================================
        "StrategyGenerationAgent": {
            "class": "StrategyGenerationAgent",
            "model": "gpt4",
            "description": "Generates trading strategies based on market analysis",
            "config": {
                "strategy_types": ["momentum", "mean_reversion", "arbitrage", "trend_following"],
                "risk_levels": ["conservative", "moderate", "aggressive"],
                "max_strategies_per_day": 10,
                "min_confidence_score": 0.8
            }
        },
        
        "BacktestingAgent": {
            "class": "BacktestingAgent",
            "model": "default",
            "description": "Backtests trading strategies against historical data",
            "config": {
                "backtest_period_days": 90,
                "min_sharpe_ratio": 1.0,
                "max_drawdown": 0.2,  # 20% maximum drawdown
                "transaction_cost": 0.001,  # 0.1% transaction cost
                "data_sources": ["binance", "coinbase", "kraken"]
            }
        },
        
        # ============================================================================
        # Execution & Risk Management Layer (5 agents)
        # ============================================================================
        "StrategyDeploymentAgent": {
            "class": "StrategyDeploymentAgent",
            "model": "default",
            "description": "Manages deployment of approved trading strategies",
            "config": {
                "max_concurrent_strategies": 5,
                "min_capital_per_strategy": 10000,  # $10k minimum
                "deployment_approval_required": True,
                "supported_exchanges": ["binance", "coinbase", "kraken"]
            }
        },
        
        "TradeExecutionAgent": {
            "class": "TradeExecutionAgent",
            "model": "default",
            "description": "Executes trades based on strategy signals",
            "config": {
                "order_types": ["market", "limit", "stop_loss", "take_profit"],
                "max_slippage": 0.005,  # 0.5% maximum slippage
                "execution_timeout": 30,  # 30 seconds
                "retry_attempts": 3
            }
        },
        
        "PerformanceTrackingAgent": {
            "class": "PerformanceTrackingAgent",
            "model": "default",
            "description": "Tracks and analyzes strategy performance",
            "config": {
                "tracking_interval": 60,  # 1 minute
                "performance_metrics": ["pnl", "sharpe", "drawdown", "win_rate"],
                "alert_thresholds": {
                    "drawdown": 0.15,  # 15% drawdown alert
                    "daily_loss": 0.05  # 5% daily loss alert
                }
            }
        },
        
        "RiskManagementAgent": {
            "class": "RiskManagementAgent",
            "model": "default",
            "description": "Monitors and manages trading risks",
            "config": {
                "max_portfolio_risk": 0.02,  # 2% portfolio risk per trade
                "position_size_limits": {
                    "single_position": 0.1,  # 10% max single position
                    "sector_exposure": 0.3   # 30% max sector exposure
                },
                "stop_loss_percentage": 0.05,  # 5% stop loss
                "risk_check_interval": 30  # 30 seconds
            }
        },
        
        "MemoryManagementAgent": {
            "class": "MemoryManagementAgent",
            "model": "default",
            "description": "Manages system memory and data retention",
            "config": {
                "data_retention_days": 90,
                "cleanup_interval": 3600,  # 1 hour
                "max_memory_usage": 0.8,  # 80% max memory usage
                "archive_old_data": True
            }
        },
        
        # ============================================================================
        # Control & Interaction Layer (3 agents)
        # ============================================================================
        "ParameterOptimizationAgent": {
            "class": "ParameterOptimizationAgent",
            "model": "gemini",
            "description": "Optimizes strategy parameters using advanced algorithms",
            "config": {
                "optimization_algorithms": ["genetic", "bayesian", "grid_search"],
                "optimization_interval": 86400,  # 24 hours
                "parameter_bounds": {
                    "lookback_period": [5, 50],
                    "threshold": [0.01, 0.1]
                },
                "max_optimization_time": 3600  # 1 hour max
            }
        },
        
        "TelegramNotificationAgent": {
            "class": "TelegramNotificationAgent",
            "model": "default",
            "description": "Sends notifications via Telegram",
            "config": {
                "bot_token": os.getenv("TELEGRAM_BOT_TOKEN", ""),
                "chat_ids": [],  # Will be configured by user
                "notification_types": ["alerts", "performance", "trades"],
                "rate_limit": 10  # max 10 messages per minute
            }
        },
        
        "SlackNotificationAgent": {
            "class": "SlackNotificationAgent",
            "model": "default",
            "description": "Sends notifications via Slack",
            "config": {
                "webhook_url": os.getenv("SLACK_WEBHOOK_URL", ""),
                "channels": ["#trading-alerts", "#performance"],
                "notification_types": ["alerts", "performance", "trades"],
                "rate_limit": 10  # max 10 messages per minute
            }
        }
    }
    
    # System-wide settings
    system_settings = {
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "crypto_trading_system.log"
        },
        "security": {
            "encrypt_api_keys": True,
            "rate_limiting": True,
            "max_requests_per_minute": 1000
        },
        "performance": {
            "max_concurrent_operations": 50,
            "database_connection_pool_size": 10,
            "cache_ttl": 300  # 5 minutes
        }
    }
    
    return {
        **supabase_config,
        "model_configs": model_configs,
        "agent_configs": agent_configs,
        "system_settings": system_settings
    }


def validate_config(config: Dict[str, Any]) -> bool:
    """Validate system configuration"""
    required_keys = ["supabase_url", "supabase_key", "model_configs", "agent_configs"]
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    # Validate Supabase configuration
    if not config["supabase_url"]:
        raise ValueError("Supabase URL is required")
    
    if not config["supabase_key"]:
        raise ValueError("Supabase key is required")
    
    # Validate model configurations
    for model_name, model_config in config["model_configs"].items():
        if "provider" not in model_config:
            raise ValueError(f"Model {model_name} missing provider")
        if "model" not in model_config:
            raise ValueError(f"Model {model_name} missing model name")
    
    # Validate agent configurations
    for agent_name, agent_config in config["agent_configs"].items():
        if "class" not in agent_config:
            raise ValueError(f"Agent {agent_name} missing class")
        if "model" not in agent_config:
            raise ValueError(f"Agent {agent_name} missing model")
    
    return True


# Environment-specific configurations
def get_development_config() -> Dict[str, Any]:
    """Get development environment configuration"""
    config = get_system_config()
    
    # Override for development
    config["system_settings"]["logging"]["level"] = "DEBUG"
    
    # Reduce update intervals for faster testing
    for agent_config in config["agent_configs"].values():
        if "config" in agent_config and "update_interval" in agent_config["config"]:
            agent_config["config"]["update_interval"] = max(60, agent_config["config"]["update_interval"] // 5)
    
    return config


def get_production_config() -> Dict[str, Any]:
    """Get production environment configuration"""
    config = get_system_config()
    
    # Production-specific settings
    config["system_settings"]["logging"]["level"] = "INFO"
    config["system_settings"]["security"]["encrypt_api_keys"] = True
    config["system_settings"]["performance"]["max_concurrent_operations"] = 100
    
    return config
