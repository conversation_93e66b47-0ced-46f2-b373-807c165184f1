"""
Database Initialization Script for Multi-Agent Crypto Trading System
Tests database connectivity and initializes system configuration
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from crypto_trading_system.core.database_manager import DatabaseManager
from crypto_trading_system.config.system_config import get_development_config


async def test_database_connection():
    """Test database connection and basic operations"""
    print("🔍 Testing database connection...")
    
    try:
        # Get configuration
        config = get_development_config()
        
        # Check if Supabase key is provided
        if not config.get("supabase_key"):
            print("❌ SUPABASE_ANON_KEY environment variable not set")
            print("Please set your Supabase anonymous key:")
            print("export SUPABASE_ANON_KEY='your_supabase_key_here'")
            return False
        
        # Create database manager
        db_manager = DatabaseManager(
            supabase_url=config["supabase_url"],
            supabase_key=config["supabase_key"]
        )
        
        # Test basic query
        result = await db_manager._client.table("agents").select("count", count="exact").execute()
        print(f"✅ Database connection successful")
        print(f"📊 Current agent count: {result.count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


async def initialize_system_agents():
    """Initialize all system agents in the database"""
    print("\n🤖 Initializing system agents...")
    
    try:
        config = get_development_config()
        db_manager = DatabaseManager(
            supabase_url=config["supabase_url"],
            supabase_key=config["supabase_key"]
        )
        
        # Define all 16 agents with their configurations
        agents_to_register = [
            # Intelligence Collection Layer
            ("WebIntelligenceAgent", "intelligence_collector", "intelligence_collection"),
            ("WhaleActivityAgent", "intelligence_collector", "intelligence_collection"),
            ("TVLMonitoringAgent", "intelligence_collector", "intelligence_collection"),
            ("MemeTrendAgent", "intelligence_collector", "intelligence_collection"),
            
            # Market Analysis Layer
            ("TrendAggregationAgent", "market_analyzer", "market_analysis"),
            ("ContractAnalysisAgent", "market_analyzer", "market_analysis"),
            
            # Strategy Generation & Backtesting Layer
            ("StrategyGenerationAgent", "strategy_manager", "strategy_generation"),
            ("BacktestingAgent", "strategy_manager", "strategy_generation"),
            
            # Execution & Risk Management Layer
            ("StrategyDeploymentAgent", "execution_manager", "execution_risk_management"),
            ("TradeExecutionAgent", "execution_manager", "execution_risk_management"),
            ("PerformanceTrackingAgent", "execution_manager", "execution_risk_management"),
            ("RiskManagementAgent", "execution_manager", "execution_risk_management"),
            ("MemoryManagementAgent", "execution_manager", "execution_risk_management"),
            
            # Control & Interaction Layer
            ("ParameterOptimizationAgent", "control_interface", "control_interaction"),
            ("TelegramNotificationAgent", "control_interface", "control_interaction"),
            ("SlackNotificationAgent", "control_interface", "control_interaction")
        ]
        
        registered_count = 0
        for agent_name, agent_type, layer in agents_to_register:
            try:
                agent_id = await db_manager.register_agent(
                    agent_name=agent_name,
                    agent_type=agent_type,
                    layer=layer,
                    configuration=config["agent_configs"].get(agent_name, {}).get("config", {})
                )
                print(f"  ✅ {agent_name} registered with ID: {agent_id}")
                registered_count += 1
                
            except Exception as e:
                print(f"  ⚠️  {agent_name} registration failed: {e}")
        
        print(f"\n📈 Successfully registered {registered_count}/{len(agents_to_register)} agents")
        return registered_count == len(agents_to_register)
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False


async def create_sample_data():
    """Create sample data to test database operations"""
    print("\n📝 Creating sample data...")
    
    try:
        config = get_development_config()
        db_manager = DatabaseManager(
            supabase_url=config["supabase_url"],
            supabase_key=config["supabase_key"]
        )
        
        # Create sample web intelligence data
        web_intel_id = await db_manager.store_web_intelligence(
            platform="twitter",
            content="Bitcoin showing strong bullish momentum with institutional adoption increasing",
            sentiment_score=0.8,
            engagement_metrics={"likes": 150, "retweets": 45, "replies": 23},
            keywords=["bitcoin", "bullish", "institutional"],
            confidence_score=0.85,
            source_url="https://twitter.com/example/status/123",
            author="crypto_analyst"
        )
        print(f"  ✅ Sample web intelligence created: {web_intel_id}")
        
        # Create sample whale activity
        whale_id = await db_manager.store_whale_activity(
            transaction_hash="0x1234567890abcdef",
            from_address="0xabcdef1234567890",
            to_address="0x9876543210fedcba",
            token_symbol="ETH",
            amount=1000.0,
            usd_value=2500000.0,
            chain="ethereum",
            block_number=18500000,
            transaction_type="transfer"
        )
        print(f"  ✅ Sample whale activity created: {whale_id}")
        
        # Create sample TVL data
        tvl_id = await db_manager.store_tvl_data(
            protocol="uniswap",
            chain="ethereum",
            tvl_usd=5000000000.0,
            tvl_change_24h=0.05,
            inflow_24h=100000000.0,
            outflow_24h=80000000.0,
            token_breakdown={"ETH": 0.4, "USDC": 0.3, "USDT": 0.3}
        )
        print(f"  ✅ Sample TVL data created: {tvl_id}")
        
        # Create sample trend signal
        signal_id = await db_manager.store_trend_signal(
            signal_type="momentum",
            symbol="BTC",
            timeframe="4h",
            signal_strength=0.75,
            direction="bullish",
            confidence_score=0.82,
            supporting_data={"rsi": 65, "macd": "positive", "volume": "increasing"}
        )
        print(f"  ✅ Sample trend signal created: {signal_id}")
        
        # Create sample strategy
        strategy_id = await db_manager.store_strategy(
            strategy_name="BTC Momentum Strategy v1",
            strategy_type="momentum",
            description="A momentum-based strategy for Bitcoin trading",
            parameters={"lookback_period": 20, "threshold": 0.02, "stop_loss": 0.05},
            status="draft"
        )
        print(f"  ✅ Sample strategy created: {strategy_id}")
        
        print("\n🎉 Sample data creation completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False


async def verify_database_schema():
    """Verify that all required database tables and indexes exist"""
    print("\n🔍 Verifying database schema...")
    
    try:
        config = get_development_config()
        db_manager = DatabaseManager(
            supabase_url=config["supabase_url"],
            supabase_key=config["supabase_key"]
        )
        
        # Expected tables
        expected_tables = [
            "agents", "messages", "system_config",
            "web_intelligence", "whale_activity", "tvl_data", "meme_trends",
            "trend_signals", "contract_analysis",
            "strategies", "backtest_results",
            "strategy_deployments", "trade_executions", "performance_data", "risk_alerts",
            "optimization_history", "notifications", "user_sessions"
        ]
        
        # Check tables
        result = await db_manager._client.table("information_schema.tables").select("table_name").eq("table_schema", "public").execute()
        existing_tables = [row["table_name"] for row in result.data]
        
        missing_tables = set(expected_tables) - set(existing_tables)
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        
        print(f"✅ All {len(expected_tables)} required tables exist")
        
        # Check some key indexes
        result = await db_manager._client.rpc("pg_indexes").select("indexname").execute()
        index_count = len(result.data) if result.data else 0
        print(f"✅ Database has {index_count} indexes for performance optimization")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema verification failed: {e}")
        return False


async def store_system_configuration():
    """Store initial system configuration in database"""
    print("\n⚙️  Storing system configuration...")
    
    try:
        config = get_development_config()
        db_manager = DatabaseManager(
            supabase_url=config["supabase_url"],
            supabase_key=config["supabase_key"]
        )
        
        # Store system initialization config
        await db_manager._client.table("system_config").upsert({
            "config_key": "system_initialization",
            "config_value": {
                "initialized_at": datetime.now(timezone.utc).isoformat(),
                "version": "1.0.0",
                "environment": "development",
                "agent_count": 16,
                "database_schema_version": "1.0.0"
            }
        }, on_conflict="config_key").execute()
        
        # Store agent configuration
        await db_manager._client.table("system_config").upsert({
            "config_key": "agent_configuration",
            "config_value": {
                "total_agents": 16,
                "layers": {
                    "intelligence_collection": 4,
                    "market_analysis": 2,
                    "strategy_generation": 2,
                    "execution_risk_management": 5,
                    "control_interaction": 3
                },
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
        }, on_conflict="config_key").execute()
        
        print("✅ System configuration stored successfully")
        return True
        
    except Exception as e:
        print(f"❌ Configuration storage failed: {e}")
        return False


async def main():
    """Main initialization function"""
    print("🚀 Multi-Agent Crypto Trading System - Database Initialization")
    print("=" * 60)
    
    # Test database connection
    if not await test_database_connection():
        print("\n❌ Database initialization failed - connection error")
        return False
    
    # Verify database schema
    if not await verify_database_schema():
        print("\n❌ Database initialization failed - schema error")
        return False
    
    # Initialize system agents
    if not await initialize_system_agents():
        print("\n❌ Database initialization failed - agent registration error")
        return False
    
    # Store system configuration
    if not await store_system_configuration():
        print("\n❌ Database initialization failed - configuration error")
        return False
    
    # Create sample data
    if not await create_sample_data():
        print("\n⚠️  Sample data creation failed, but system is still functional")
    
    print("\n" + "=" * 60)
    print("🎉 Database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Set up your API keys (ANTHROPIC_API_KEY, GOOGLE_API_KEY, etc.)")
    print("2. Configure notification settings (Telegram, Slack)")
    print("3. Run the main system: python crypto_trading_system/main.py")
    print("\nFor testing: python crypto_trading_system/scripts/test_system.py")
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
