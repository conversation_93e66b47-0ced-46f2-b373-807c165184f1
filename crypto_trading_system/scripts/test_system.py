"""
System Testing Script for Multi-Agent Crypto Trading System
Tests database integration, agent communication, and system functionality
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from crypto_trading_system.core.database_manager import DatabaseManager
from crypto_trading_system.core.system_manager import SystemManager
from crypto_trading_system.config.system_config import get_development_config


class SystemTester:
    """Comprehensive system testing class"""
    
    def __init__(self):
        self.config = get_development_config()
        self.db_manager = None
        self.system_manager = None
        self.test_results = {}
    
    async def setup(self):
        """Setup test environment"""
        print("🔧 Setting up test environment...")
        
        try:
            # Initialize database manager
            self.db_manager = DatabaseManager(
                supabase_url=self.config["supabase_url"],
                supabase_key=self.config["supabase_key"]
            )
            
            # Initialize system manager
            self.system_manager = SystemManager(self.config)
            
            print("✅ Test environment setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Test setup failed: {e}")
            return False
    
    async def test_database_operations(self):
        """Test all database operations"""
        print("\n📊 Testing database operations...")
        
        tests = []
        
        # Test agent registration
        try:
            agent_id = await self.db_manager.register_agent(
                agent_name="TestAgent",
                agent_type="test",
                layer="test_layer",
                configuration={"test": True}
            )
            tests.append(("Agent Registration", True, f"ID: {agent_id}"))
        except Exception as e:
            tests.append(("Agent Registration", False, str(e)))
        
        # Test message logging
        try:
            from crypto_trading_system.core.messages import IntelligenceDataMessage
            
            test_message = IntelligenceDataMessage(
                source_agent="TestAgent",
                data_type="test_data",
                data={"test": "value"},
                confidence_score=0.9
            )
            
            message_id = await self.db_manager.log_message(test_message)
            tests.append(("Message Logging", True, f"ID: {message_id}"))
        except Exception as e:
            tests.append(("Message Logging", False, str(e)))
        
        # Test intelligence data storage
        try:
            intel_id = await self.db_manager.store_web_intelligence(
                platform="test_platform",
                content="Test intelligence content",
                sentiment_score=0.7,
                confidence_score=0.8
            )
            tests.append(("Intelligence Storage", True, f"ID: {intel_id}"))
        except Exception as e:
            tests.append(("Intelligence Storage", False, str(e)))
        
        # Test strategy storage
        try:
            strategy_id = await self.db_manager.store_strategy(
                strategy_name="Test Strategy",
                strategy_type="test",
                description="Test strategy for system validation"
            )
            tests.append(("Strategy Storage", True, f"ID: {strategy_id}"))
        except Exception as e:
            tests.append(("Strategy Storage", False, str(e)))
        
        # Test query operations
        try:
            intelligence = await self.db_manager.get_recent_intelligence(limit=5)
            tests.append(("Intelligence Query", True, f"Found {sum(len(data) for data in intelligence.values())} entries"))
        except Exception as e:
            tests.append(("Intelligence Query", False, str(e)))
        
        # Print results
        for test_name, success, details in tests:
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}: {details}")
        
        self.test_results["database_operations"] = tests
        return all(test[1] for test in tests)
    
    async def test_system_initialization(self):
        """Test system manager initialization"""
        print("\n🚀 Testing system initialization...")
        
        tests = []
        
        try:
            # Test system initialization
            await self.system_manager.initialize()
            tests.append(("System Initialization", True, "System initialized successfully"))
            
            # Test system status
            status = self.system_manager.get_system_status()
            tests.append(("System Status", status["initialized"], f"Agents: {status['agent_count']}"))
            
            # Test database connection
            db_connected = status["database_connected"]
            tests.append(("Database Connection", db_connected, "Database manager active"))
            
        except Exception as e:
            tests.append(("System Initialization", False, str(e)))
        
        # Print results
        for test_name, success, details in tests:
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}: {details}")
        
        self.test_results["system_initialization"] = tests
        return all(test[1] for test in tests)
    
    async def test_agent_functionality(self):
        """Test individual agent functionality"""
        print("\n🤖 Testing agent functionality...")
        
        tests = []
        
        try:
            # Get all registered agents
            agents_result = await self.db_manager._client.table("agents").select("*").execute()
            agent_count = len(agents_result.data)
            tests.append(("Agent Count", agent_count >= 16, f"Found {agent_count} agents"))
            
            # Test agent heartbeat
            if agent_count > 0:
                test_agent = agents_result.data[0]
                heartbeat_success = await self.db_manager.update_agent_heartbeat(test_agent["agent_name"])
                tests.append(("Agent Heartbeat", heartbeat_success, f"Updated {test_agent['agent_name']}"))
            
            # Test agent status retrieval
            if agent_count > 0:
                agent_status = await self.db_manager.get_agent_status(test_agent["agent_name"])
                tests.append(("Agent Status", agent_status is not None, f"Retrieved status for {test_agent['agent_name']}"))
            
        except Exception as e:
            tests.append(("Agent Functionality", False, str(e)))
        
        # Print results
        for test_name, success, details in tests:
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}: {details}")
        
        self.test_results["agent_functionality"] = tests
        return all(test[1] for test in tests)
    
    async def test_data_flow(self):
        """Test end-to-end data flow through the system"""
        print("\n🔄 Testing data flow...")
        
        tests = []
        
        try:
            # Test intelligence collection flow
            whale_id = await self.db_manager.store_whale_activity(
                transaction_hash="0xtest123",
                from_address="0xfrom",
                to_address="0xto",
                token_symbol="TEST",
                amount=1000.0,
                usd_value=50000.0
            )
            
            # Test analysis flow
            signal_id = await self.db_manager.store_trend_signal(
                signal_type="test_signal",
                symbol="TEST",
                timeframe="1h",
                signal_strength=0.8,
                direction="bullish"
            )
            
            # Test strategy flow
            strategy_id = await self.db_manager.store_strategy(
                strategy_name="Test Flow Strategy",
                strategy_type="test_flow",
                description="Testing data flow"
            )
            
            # Test execution flow
            deployment_id = await self.db_manager.create_strategy_deployment(
                strategy_id=strategy_id,
                deployment_name="Test Deployment",
                allocated_capital=10000.0
            )
            
            tests.append(("Data Flow", True, "Complete flow from intelligence to execution"))
            
        except Exception as e:
            tests.append(("Data Flow", False, str(e)))
        
        # Print results
        for test_name, success, details in tests:
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}: {details}")
        
        self.test_results["data_flow"] = tests
        return all(test[1] for test in tests)
    
    async def test_performance(self):
        """Test system performance"""
        print("\n⚡ Testing system performance...")
        
        tests = []
        
        try:
            # Test database query performance
            start_time = datetime.now()
            
            # Perform multiple operations
            for i in range(10):
                await self.db_manager.store_web_intelligence(
                    platform="performance_test",
                    content=f"Performance test content {i}",
                    sentiment_score=0.5
                )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            tests.append(("Bulk Operations", duration < 5.0, f"10 operations in {duration:.2f}s"))
            
            # Test concurrent operations
            start_time = datetime.now()
            
            tasks = []
            for i in range(5):
                task = self.db_manager.store_meme_trend(
                    token_symbol=f"TEST{i}",
                    chain="test_chain",
                    trend_score=0.5
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            end_time = datetime.now()
            concurrent_duration = (end_time - start_time).total_seconds()
            
            tests.append(("Concurrent Operations", concurrent_duration < 3.0, f"5 concurrent ops in {concurrent_duration:.2f}s"))
            
        except Exception as e:
            tests.append(("Performance", False, str(e)))
        
        # Print results
        for test_name, success, details in tests:
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}: {details}")
        
        self.test_results["performance"] = tests
        return all(test[1] for test in tests)
    
    async def cleanup(self):
        """Cleanup test data"""
        print("\n🧹 Cleaning up test data...")
        
        try:
            # Clean up test agents
            await self.db_manager._client.table("agents").delete().eq("agent_name", "TestAgent").execute()
            
            # Clean up test data
            await self.db_manager._client.table("web_intelligence").delete().eq("platform", "test_platform").execute()
            await self.db_manager._client.table("web_intelligence").delete().eq("platform", "performance_test").execute()
            
            # Clean up test strategies
            await self.db_manager._client.table("strategies").delete().like("strategy_name", "%Test%").execute()
            
            print("✅ Test cleanup completed")
            
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            category_passed = sum(1 for test in tests if test[1])
            category_total = len(tests)
            
            total_tests += category_total
            passed_tests += category_passed
            
            status = "✅" if category_passed == category_total else "❌"
            print(f"{status} {category.replace('_', ' ').title()}: {category_passed}/{category_total}")
        
        print("-" * 60)
        overall_status = "✅" if passed_tests == total_tests else "❌"
        print(f"{overall_status} OVERALL: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("\n🎉 All tests passed! System is ready for deployment.")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} tests failed. Please review the issues above.")
        
        return passed_tests == total_tests


async def main():
    """Main testing function"""
    print("🧪 Multi-Agent Crypto Trading System - Comprehensive Testing")
    print("=" * 60)
    
    tester = SystemTester()
    
    # Setup
    if not await tester.setup():
        print("❌ Test setup failed, aborting tests")
        return False
    
    # Run all tests
    try:
        await tester.test_database_operations()
        await tester.test_system_initialization()
        await tester.test_agent_functionality()
        await tester.test_data_flow()
        await tester.test_performance()
        
    except Exception as e:
        print(f"❌ Testing failed with error: {e}")
        return False
    
    finally:
        # Cleanup
        await tester.cleanup()
        
        # Print summary
        success = tester.print_summary()
        
        if success:
            print("\nNext steps:")
            print("1. Configure your API keys for production use")
            print("2. Set up notification channels (Telegram/Slack)")
            print("3. Deploy the system: python crypto_trading_system/main.py production")
        
        return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
