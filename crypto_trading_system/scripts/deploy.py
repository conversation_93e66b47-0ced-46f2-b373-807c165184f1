"""
Deployment Script for Multi-Agent Crypto Trading System
Handles production deployment and system validation
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from crypto_trading_system.core.database_manager import DatabaseManager
from crypto_trading_system.core.system_manager import SystemManager
from crypto_trading_system.config.system_config import get_production_config, validate_config


class DeploymentManager:
    """Manages system deployment and validation"""
    
    def __init__(self):
        self.config = get_production_config()
        self.logger = self._setup_logging()
        self.deployment_checks = []
    
    def _setup_logging(self):
        """Setup deployment logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - DEPLOY - %(levelname)s - %(message)s',
            handlers=[
                logging.Stream<PERSON><PERSON>ler(sys.stdout),
                logging.FileHandler('deployment.log')
            ]
        )
        return logging.getLogger(__name__)
    
    def check_environment_variables(self):
        """Check all required environment variables"""
        self.logger.info("🔍 Checking environment variables...")
        
        required_vars = {
            "SUPABASE_ANON_KEY": "Supabase database access",
            "ANTHROPIC_API_KEY": "Claude AI model access",
        }
        
        optional_vars = {
            "GOOGLE_API_KEY": "Gemini AI model access",
            "OPENAI_API_KEY": "GPT-4 AI model access",
            "TELEGRAM_BOT_TOKEN": "Telegram notifications",
            "SLACK_WEBHOOK_URL": "Slack notifications"
        }
        
        missing_required = []
        missing_optional = []
        
        # Check required variables
        for var, description in required_vars.items():
            if not os.getenv(var):
                missing_required.append(f"{var} ({description})")
        
        # Check optional variables
        for var, description in optional_vars.items():
            if not os.getenv(var):
                missing_optional.append(f"{var} ({description})")
        
        if missing_required:
            self.logger.error(f"❌ Missing required environment variables:")
            for var in missing_required:
                self.logger.error(f"   - {var}")
            return False
        
        if missing_optional:
            self.logger.warning(f"⚠️  Missing optional environment variables:")
            for var in missing_optional:
                self.logger.warning(f"   - {var}")
        
        self.logger.info("✅ Environment variables check completed")
        return True
    
    async def validate_database_connection(self):
        """Validate database connection and schema"""
        self.logger.info("🔍 Validating database connection...")
        
        try:
            db_manager = DatabaseManager(
                supabase_url=self.config["supabase_url"],
                supabase_key=self.config["supabase_key"]
            )
            
            # Test basic connection
            result = await db_manager._client.table("agents").select("count", count="exact").execute()
            self.logger.info(f"✅ Database connection successful")
            
            # Validate schema
            expected_tables = [
                "agents", "messages", "system_config",
                "web_intelligence", "whale_activity", "tvl_data", "meme_trends",
                "trend_signals", "contract_analysis",
                "strategies", "backtest_results",
                "strategy_deployments", "trade_executions", "performance_data", "risk_alerts",
                "optimization_history", "notifications", "user_sessions"
            ]
            
            # Check all tables exist
            for table in expected_tables:
                try:
                    await db_manager._client.table(table).select("count", count="exact").execute()
                except Exception as e:
                    self.logger.error(f"❌ Table {table} not found or accessible: {e}")
                    return False
            
            self.logger.info(f"✅ All {len(expected_tables)} database tables validated")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Database validation failed: {e}")
            return False
    
    async def validate_system_configuration(self):
        """Validate system configuration"""
        self.logger.info("🔍 Validating system configuration...")
        
        try:
            # Validate configuration structure
            validate_config(self.config)
            
            # Check agent configurations
            agent_count = len(self.config["agent_configs"])
            if agent_count != 16:
                self.logger.error(f"❌ Expected 16 agents, found {agent_count}")
                return False
            
            # Check model configurations
            model_count = len(self.config["model_configs"])
            if model_count < 1:
                self.logger.error(f"❌ No model configurations found")
                return False
            
            self.logger.info(f"✅ System configuration validated ({agent_count} agents, {model_count} models)")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Configuration validation failed: {e}")
            return False
    
    async def initialize_production_system(self):
        """Initialize the production system"""
        self.logger.info("🚀 Initializing production system...")
        
        try:
            system_manager = SystemManager(self.config)
            await system_manager.initialize()
            
            # Get system status
            status = system_manager.get_system_status()
            
            if not status["initialized"]:
                self.logger.error("❌ System initialization failed")
                return False
            
            if not status["database_connected"]:
                self.logger.error("❌ Database connection failed")
                return False
            
            if status["agent_count"] != 16:
                self.logger.error(f"❌ Expected 16 agents, initialized {status['agent_count']}")
                return False
            
            self.logger.info(f"✅ Production system initialized successfully")
            self.logger.info(f"   - Agents: {status['agent_count']}")
            self.logger.info(f"   - Database: Connected")
            self.logger.info(f"   - Runtime: Active")
            
            # Cleanup for deployment
            await system_manager.cleanup()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ System initialization failed: {e}")
            return False
    
    async def create_deployment_record(self):
        """Create deployment record in database"""
        self.logger.info("📝 Creating deployment record...")
        
        try:
            db_manager = DatabaseManager(
                supabase_url=self.config["supabase_url"],
                supabase_key=self.config["supabase_key"]
            )
            
            deployment_record = {
                "config_key": "production_deployment",
                "config_value": {
                    "deployed_at": datetime.now(timezone.utc).isoformat(),
                    "version": "1.0.0",
                    "environment": "production",
                    "agent_count": 16,
                    "deployment_status": "active",
                    "deployment_id": f"deploy_{int(datetime.now().timestamp())}"
                }
            }
            
            await db_manager._client.table("system_config").upsert(
                deployment_record, on_conflict="config_key"
            ).execute()
            
            self.logger.info("✅ Deployment record created")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create deployment record: {e}")
            return False
    
    def print_deployment_summary(self, success: bool):
        """Print deployment summary"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("🚀 PRODUCTION DEPLOYMENT SUMMARY")
        self.logger.info("=" * 60)
        
        if success:
            self.logger.info("✅ DEPLOYMENT SUCCESSFUL")
            self.logger.info("\nSystem is ready for production use!")
            self.logger.info("\nNext steps:")
            self.logger.info("1. Start the system: python crypto_trading_system/main.py production")
            self.logger.info("2. Monitor system logs: tail -f crypto_trading_system.log")
            self.logger.info("3. Set up monitoring and alerts")
            self.logger.info("4. Configure backup procedures")
            
        else:
            self.logger.error("❌ DEPLOYMENT FAILED")
            self.logger.error("\nPlease resolve the issues above before deploying to production.")
            self.logger.error("Check the deployment.log file for detailed error information.")
        
        self.logger.info("\n" + "=" * 60)
    
    async def deploy(self):
        """Execute complete deployment process"""
        self.logger.info("🚀 Starting production deployment...")
        self.logger.info(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
        
        deployment_steps = [
            ("Environment Variables", self.check_environment_variables),
            ("Database Connection", self.validate_database_connection),
            ("System Configuration", self.validate_system_configuration),
            ("System Initialization", self.initialize_production_system),
            ("Deployment Record", self.create_deployment_record)
        ]
        
        success = True
        
        for step_name, step_function in deployment_steps:
            self.logger.info(f"\n--- {step_name} ---")
            
            try:
                if asyncio.iscoroutinefunction(step_function):
                    step_success = await step_function()
                else:
                    step_success = step_function()
                
                if not step_success:
                    self.logger.error(f"❌ {step_name} failed")
                    success = False
                    break
                else:
                    self.logger.info(f"✅ {step_name} completed")
                    
            except Exception as e:
                self.logger.error(f"❌ {step_name} failed with exception: {e}")
                success = False
                break
        
        self.print_deployment_summary(success)
        return success


async def main():
    """Main deployment function"""
    print("🚀 Multi-Agent Crypto Trading System - Production Deployment")
    print("=" * 60)
    
    # Confirm production deployment
    if os.getenv("ENVIRONMENT") != "production":
        print("⚠️  This script is for production deployment.")
        print("Set ENVIRONMENT=production to continue.")
        confirm = input("Continue with production deployment? (yes/no): ")
        if confirm.lower() != "yes":
            print("Deployment cancelled.")
            return False
    
    deployment_manager = DeploymentManager()
    success = await deployment_manager.deploy()
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
