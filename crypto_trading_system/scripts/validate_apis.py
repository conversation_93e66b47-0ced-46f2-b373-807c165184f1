#!/usr/bin/env python3
"""
API Validation Script

Comprehensive validation of all configured API keys and services
before system startup to ensure proper functionality.
"""

import asyncio
import os
import sys
import json
import logging
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import aiohttp
import requests
from dataclasses import dataclass

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.env_loader import get_env_config

@dataclass
class ValidationResult:
    """Result of API validation"""
    service: str
    status: str  # 'success', 'warning', 'error', 'skipped'
    message: str
    details: Optional[Dict[str, Any]] = None

class APIValidator:
    """Comprehensive API validation system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.env_config = get_env_config()
        self.results: List[ValidationResult] = []
        
    async def validate_all_apis(self) -> List[ValidationResult]:
        """Validate all configured APIs"""
        self.logger.info("🔍 Starting comprehensive API validation...")
        
        # Core validations (required for system operation)
        await self._validate_supabase()
        await self._validate_ai_models()
        await self._validate_telegram()
        
        # Trading validations
        await self._validate_binance()
        
        # Blockchain data validations
        await self._validate_blockchain_apis()
        
        # Social media validations
        await self._validate_twitter()
        
        # Optional service validations
        await self._validate_optional_services()
        
        return self.results
    
    async def _validate_supabase(self):
        """Validate Supabase connection and authentication"""
        try:
            url = self.env_config.get("supabase_url")
            anon_key = self.env_config.get("supabase_anon_key")
            service_key = self.env_config.get("supabase_service_key")
            
            if not url or not anon_key:
                self.results.append(ValidationResult(
                    "Supabase", "error", 
                    "Missing required Supabase configuration (URL or anon key)"
                ))
                return
            
            # Test anon key connection
            headers = {
                "apikey": anon_key,
                "Authorization": f"Bearer {anon_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{url}/rest/v1/", headers=headers) as response:
                    if response.status == 200:
                        self.results.append(ValidationResult(
                            "Supabase", "success", 
                            "Supabase connection successful",
                            {"url": url, "anon_key_valid": True}
                        ))
                    else:
                        self.results.append(ValidationResult(
                            "Supabase", "error", 
                            f"Supabase connection failed: {response.status}"
                        ))
                        
        except Exception as e:
            self.results.append(ValidationResult(
                "Supabase", "error", f"Supabase validation error: {str(e)}"
            ))
    
    async def _validate_ai_models(self):
        """Validate AI model API keys"""
        # Anthropic Claude
        anthropic_key = self.env_config.get("anthropic_api_key")
        if anthropic_key and anthropic_key.startswith("sk-ant-"):
            try:
                headers = {
                    "x-api-key": anthropic_key,
                    "Content-Type": "application/json",
                    "anthropic-version": "2023-06-01"
                }
                
                async with aiohttp.ClientSession() as session:
                    # Test with a simple completion request
                    data = {
                        "model": "claude-3-sonnet-20240229",
                        "max_tokens": 10,
                        "messages": [{"role": "user", "content": "Hello"}]
                    }
                    
                    async with session.post(
                        "https://api.anthropic.com/v1/messages",
                        headers=headers,
                        json=data
                    ) as response:
                        if response.status == 200:
                            self.results.append(ValidationResult(
                                "Anthropic Claude", "success", "Claude API key valid"
                            ))
                        else:
                            self.results.append(ValidationResult(
                                "Anthropic Claude", "error", 
                                f"Claude API validation failed: {response.status}"
                            ))
                            
            except Exception as e:
                self.results.append(ValidationResult(
                    "Anthropic Claude", "error", f"Claude validation error: {str(e)}"
                ))
        else:
            self.results.append(ValidationResult(
                "Anthropic Claude", "warning", "Claude API key not configured or invalid format"
            ))
        
        # Google Gemini
        google_key = self.env_config.get("google_api_key")
        if google_key:
            try:
                url = f"https://generativelanguage.googleapis.com/v1/models?key={google_key}"
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            self.results.append(ValidationResult(
                                "Google Gemini", "success", "Gemini API key valid"
                            ))
                        else:
                            self.results.append(ValidationResult(
                                "Google Gemini", "error", 
                                f"Gemini API validation failed: {response.status}"
                            ))
            except Exception as e:
                self.results.append(ValidationResult(
                    "Google Gemini", "error", f"Gemini validation error: {str(e)}"
                ))
        else:
            self.results.append(ValidationResult(
                "Google Gemini", "skipped", "Gemini API key not configured (optional)"
            ))
        
        # OpenAI GPT-4
        openai_key = self.env_config.get("openai_api_key")
        if openai_key and openai_key.startswith("sk-"):
            try:
                headers = {
                    "Authorization": f"Bearer {openai_key}",
                    "Content-Type": "application/json"
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        "https://api.openai.com/v1/models",
                        headers=headers
                    ) as response:
                        if response.status == 200:
                            self.results.append(ValidationResult(
                                "OpenAI GPT-4", "success", "OpenAI API key valid"
                            ))
                        else:
                            self.results.append(ValidationResult(
                                "OpenAI GPT-4", "error", 
                                f"OpenAI API validation failed: {response.status}"
                            ))
            except Exception as e:
                self.results.append(ValidationResult(
                    "OpenAI GPT-4", "error", f"OpenAI validation error: {str(e)}"
                ))
        else:
            self.results.append(ValidationResult(
                "OpenAI GPT-4", "skipped", "OpenAI API key not configured (optional)"
            ))
    
    async def _validate_telegram(self):
        """Validate Telegram bot configuration"""
        bot_token = self.env_config.get("telegram_bot_token")
        chat_id = self.env_config.get("telegram_chat_id")
        
        if not bot_token:
            self.results.append(ValidationResult(
                "Telegram Bot", "error", "Telegram bot token not configured (required for mobile control)"
            ))
            return
        
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getMe"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("ok"):
                            bot_info = data.get("result", {})
                            self.results.append(ValidationResult(
                                "Telegram Bot", "success", 
                                f"Telegram bot '{bot_info.get('username', 'Unknown')}' is valid",
                                {"bot_info": bot_info, "chat_id_configured": bool(chat_id)}
                            ))
                        else:
                            self.results.append(ValidationResult(
                                "Telegram Bot", "error", "Telegram bot token is invalid"
                            ))
                    else:
                        self.results.append(ValidationResult(
                            "Telegram Bot", "error", f"Telegram API error: {response.status}"
                        ))
        except Exception as e:
            self.results.append(ValidationResult(
                "Telegram Bot", "error", f"Telegram validation error: {str(e)}"
            ))
    
    async def _validate_binance(self):
        """Validate Binance API configuration"""
        api_key = self.env_config.get("binance_api_key")
        secret_key = self.env_config.get("binance_secret_key")
        testnet = self.env_config.get("binance_testnet", True)
        
        if not api_key or not secret_key:
            self.results.append(ValidationResult(
                "Binance", "warning", "Binance API keys not configured (required for live trading)"
            ))
            return
        
        try:
            base_url = "https://testnet.binance.vision" if testnet else "https://api.binance.com"
            headers = {"X-MBX-APIKEY": api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{base_url}/api/v3/account", headers=headers) as response:
                    if response.status == 200:
                        self.results.append(ValidationResult(
                            "Binance", "success", 
                            f"Binance API valid ({'testnet' if testnet else 'live'})"
                        ))
                    elif response.status == 401:
                        self.results.append(ValidationResult(
                            "Binance", "error", "Binance API keys are invalid"
                        ))
                    else:
                        self.results.append(ValidationResult(
                            "Binance", "warning", 
                            f"Binance API validation inconclusive: {response.status}"
                        ))
        except Exception as e:
            self.results.append(ValidationResult(
                "Binance", "error", f"Binance validation error: {str(e)}"
            ))
    
    async def _validate_blockchain_apis(self):
        """Validate blockchain data provider APIs"""
        # Alchemy
        alchemy_key = self.env_config.get("alchemy_api_key")
        if alchemy_key:
            try:
                url = f"https://eth-mainnet.g.alchemy.com/v2/{alchemy_key}"
                data = {
                    "jsonrpc": "2.0",
                    "method": "eth_blockNumber",
                    "params": [],
                    "id": 1
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            if "result" in result:
                                self.results.append(ValidationResult(
                                    "Alchemy", "success", "Alchemy API key valid"
                                ))
                            else:
                                self.results.append(ValidationResult(
                                    "Alchemy", "error", "Alchemy API key invalid"
                                ))
                        else:
                            self.results.append(ValidationResult(
                                "Alchemy", "error", f"Alchemy API error: {response.status}"
                            ))
            except Exception as e:
                self.results.append(ValidationResult(
                    "Alchemy", "error", f"Alchemy validation error: {str(e)}"
                ))
        else:
            self.results.append(ValidationResult(
                "Alchemy", "warning", "Alchemy API key not configured"
            ))
        
        # Moralis
        moralis_key = self.env_config.get("moralis_api_key")
        if moralis_key:
            try:
                headers = {"X-API-Key": moralis_key}
                url = "https://deep-index.moralis.io/api/v2/0x0000000000000000000000000000000000000000/balance"
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, headers=headers) as response:
                        if response.status == 200:
                            self.results.append(ValidationResult(
                                "Moralis", "success", "Moralis API key valid"
                            ))
                        else:
                            self.results.append(ValidationResult(
                                "Moralis", "error", f"Moralis API validation failed: {response.status}"
                            ))
            except Exception as e:
                self.results.append(ValidationResult(
                    "Moralis", "error", f"Moralis validation error: {str(e)}"
                ))
        else:
            self.results.append(ValidationResult(
                "Moralis", "warning", "Moralis API key not configured"
            ))
    
    async def _validate_twitter(self):
        """Validate Twitter API configuration"""
        bearer_token = self.env_config.get("twitter_bearer_token")
        
        if not bearer_token:
            self.results.append(ValidationResult(
                "Twitter", "warning", "Twitter Bearer Token not configured"
            ))
            return
        
        try:
            headers = {"Authorization": f"Bearer {bearer_token}"}
            url = "https://api.twitter.com/2/users/me"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        self.results.append(ValidationResult(
                            "Twitter", "success", "Twitter API access valid"
                        ))
                    else:
                        self.results.append(ValidationResult(
                            "Twitter", "error", f"Twitter API validation failed: {response.status}"
                        ))
        except Exception as e:
            self.results.append(ValidationResult(
                "Twitter", "error", f"Twitter validation error: {str(e)}"
            ))
    
    async def _validate_optional_services(self):
        """Validate optional service configurations"""
        # Slack
        slack_webhook = self.env_config.get("slack_webhook_url")
        if slack_webhook:
            try:
                test_payload = {"text": "API validation test - please ignore"}
                async with aiohttp.ClientSession() as session:
                    async with session.post(slack_webhook, json=test_payload) as response:
                        if response.status == 200:
                            self.results.append(ValidationResult(
                                "Slack", "success", "Slack webhook valid"
                            ))
                        else:
                            self.results.append(ValidationResult(
                                "Slack", "error", f"Slack webhook validation failed: {response.status}"
                            ))
            except Exception as e:
                self.results.append(ValidationResult(
                    "Slack", "error", f"Slack validation error: {str(e)}"
                ))
        else:
            self.results.append(ValidationResult(
                "Slack", "skipped", "Slack webhook not configured (optional)"
            ))
    
    def print_validation_report(self):
        """Print comprehensive validation report"""
        print("\n" + "="*80)
        print("🔍 API VALIDATION REPORT")
        print("="*80)
        
        success_count = sum(1 for r in self.results if r.status == "success")
        error_count = sum(1 for r in self.results if r.status == "error")
        warning_count = sum(1 for r in self.results if r.status == "warning")
        skipped_count = sum(1 for r in self.results if r.status == "skipped")
        
        print(f"📊 Summary: {success_count} ✅ | {error_count} ❌ | {warning_count} ⚠️ | {skipped_count} ⏭️")
        print()
        
        for result in self.results:
            status_emoji = {
                "success": "✅",
                "error": "❌", 
                "warning": "⚠️",
                "skipped": "⏭️"
            }
            
            print(f"{status_emoji[result.status]} {result.service:<20} {result.message}")
            if result.details:
                for key, value in result.details.items():
                    print(f"   └─ {key}: {value}")
        
        print("\n" + "="*80)
        
        if error_count > 0:
            print("❌ CRITICAL ERRORS DETECTED - System may not function properly")
            return False
        elif warning_count > 0:
            print("⚠️  WARNINGS DETECTED - Some features may be limited")
            return True
        else:
            print("✅ ALL VALIDATIONS PASSED - System ready for operation")
            return True

async def main():
    """Main validation function"""
    logging.basicConfig(level=logging.INFO)
    
    validator = APIValidator()
    await validator.validate_all_apis()
    
    success = validator.print_validation_report()
    
    # Save detailed results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"validation_report_{timestamp}.json"
    
    with open(report_file, 'w') as f:
        json.dump([{
            "service": r.service,
            "status": r.status,
            "message": r.message,
            "details": r.details
        } for r in validator.results], f, indent=2)
    
    print(f"\n📄 Detailed report saved to: {report_file}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
