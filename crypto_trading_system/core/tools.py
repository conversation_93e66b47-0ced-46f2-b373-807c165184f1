"""
Shared Tools and Utilities for Multi-Agent Crypto Trading System

This module provides common tools and utilities that agents can use
for data collection, analysis, and execution.
"""

import asyncio
import aiohttp
import json
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from autogen_core.tools import Tool


# ============================================================================
# Data Collection Tools
# ============================================================================

class TwitterAPITool(Tool):
    """Tool for collecting data from Twitter API"""
    
    def __init__(self, api_key: str, api_secret: str, bearer_token: str):
        super().__init__(
            name="twitter_api_tool",
            description="Collect tweets and social sentiment data from Twitter"
        )
        self.api_key = api_key
        self.api_secret = api_secret
        self.bearer_token = bearer_token
        self.base_url = "https://api.twitter.com/2"
    
    async def run_json(self, args: Dict[str, Any], cancellation_token=None) -> Dict[str, Any]:
        """Search for tweets based on query parameters"""
        query = args.get("query", "")
        max_results = args.get("max_results", 100)
        
        headers = {
            "Authorization": f"Bearer {self.bearer_token}",
            "Content-Type": "application/json"
        }
        
        params = {
            "query": query,
            "max_results": max_results,
            "tweet.fields": "created_at,public_metrics,context_annotations,sentiment"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/tweets/search/recent",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_twitter_data(data)
                else:
                    return {"error": f"Twitter API error: {response.status}"}
    
    def _process_twitter_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process raw Twitter API response"""
        tweets = data.get("data", [])
        processed_tweets = []
        
        for tweet in tweets:
            processed_tweet = {
                "id": tweet.get("id"),
                "text": tweet.get("text"),
                "created_at": tweet.get("created_at"),
                "metrics": tweet.get("public_metrics", {}),
                "sentiment": self._analyze_sentiment(tweet.get("text", "")),
                "keywords": self._extract_keywords(tweet.get("text", ""))
            }
            processed_tweets.append(processed_tweet)
        
        return {
            "tweets": processed_tweets,
            "total_count": len(processed_tweets),
            "timestamp": datetime.now().isoformat()
        }
    
    def _analyze_sentiment(self, text: str) -> float:
        """Simple sentiment analysis (replace with proper NLP model)"""
        positive_words = ["bullish", "moon", "pump", "buy", "long", "up", "gain"]
        negative_words = ["bearish", "dump", "sell", "short", "down", "loss", "crash"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count + negative_count == 0:
            return 0.0
        
        return (positive_count - negative_count) / (positive_count + negative_count)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract crypto-related keywords from text"""
        crypto_keywords = [
            "bitcoin", "btc", "ethereum", "eth", "defi", "nft", "dao", "yield",
            "staking", "liquidity", "airdrop", "meme", "doge", "shib", "pepe"
        ]
        
        text_lower = text.lower()
        found_keywords = [keyword for keyword in crypto_keywords if keyword in text_lower]
        return found_keywords


class OnChainDataTool(Tool):
    """Tool for collecting on-chain data"""
    
    def __init__(self, etherscan_api_key: str):
        super().__init__(
            name="onchain_data_tool",
            description="Collect on-chain transaction and whale activity data"
        )
        self.etherscan_api_key = etherscan_api_key
        self.etherscan_base_url = "https://api.etherscan.io/api"
    
    async def run_json(self, args: Dict[str, Any], cancellation_token=None) -> Dict[str, Any]:
        """Get on-chain data based on request type"""
        request_type = args.get("type", "whale_transactions")
        
        if request_type == "whale_transactions":
            return await self._get_whale_transactions(args)
        elif request_type == "token_transfers":
            return await self._get_token_transfers(args)
        elif request_type == "contract_info":
            return await self._get_contract_info(args)
        else:
            return {"error": f"Unknown request type: {request_type}"}
    
    async def _get_whale_transactions(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Get large transactions (whale activity)"""
        min_value = args.get("min_value", 1000000)  # $1M minimum
        
        params = {
            "module": "account",
            "action": "txlist",
            "startblock": 0,
            "endblock": ********,
            "sort": "desc",
            "apikey": self.etherscan_api_key
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(self.etherscan_base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._filter_whale_transactions(data, min_value)
                else:
                    return {"error": f"Etherscan API error: {response.status}"}
    
    def _filter_whale_transactions(self, data: Dict[str, Any], min_value: float) -> Dict[str, Any]:
        """Filter transactions for whale activity"""
        transactions = data.get("result", [])
        whale_transactions = []
        
        for tx in transactions:
            value_eth = float(tx.get("value", 0)) / 1e18
            value_usd = value_eth * 2000  # Approximate ETH price
            
            if value_usd >= min_value:
                whale_tx = {
                    "hash": tx.get("hash"),
                    "from": tx.get("from"),
                    "to": tx.get("to"),
                    "value_eth": value_eth,
                    "value_usd": value_usd,
                    "timestamp": datetime.fromtimestamp(int(tx.get("timeStamp", 0))).isoformat(),
                    "gas_used": tx.get("gasUsed"),
                    "gas_price": tx.get("gasPrice")
                }
                whale_transactions.append(whale_tx)
        
        return {
            "whale_transactions": whale_transactions,
            "total_count": len(whale_transactions),
            "timestamp": datetime.now().isoformat()
        }


class DeFiDataTool(Tool):
    """Tool for collecting DeFi protocol data"""
    
    def __init__(self):
        super().__init__(
            name="defi_data_tool",
            description="Collect DeFi protocol TVL and yield data"
        )
        self.defillama_base_url = "https://api.llama.fi"
    
    async def run_json(self, args: Dict[str, Any], cancellation_token=None) -> Dict[str, Any]:
        """Get DeFi data based on request type"""
        request_type = args.get("type", "tvl_data")
        
        if request_type == "tvl_data":
            return await self._get_tvl_data(args)
        elif request_type == "protocol_data":
            return await self._get_protocol_data(args)
        elif request_type == "yield_data":
            return await self._get_yield_data(args)
        else:
            return {"error": f"Unknown request type: {request_type}"}
    
    async def _get_tvl_data(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Get TVL data for DeFi protocols"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.defillama_base_url}/protocols") as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_tvl_data(data)
                else:
                    return {"error": f"DeFiLlama API error: {response.status}"}
    
    def _process_tvl_data(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process TVL data from DeFiLlama"""
        processed_protocols = []
        
        for protocol in data:
            processed_protocol = {
                "name": protocol.get("name"),
                "symbol": protocol.get("symbol"),
                "tvl": protocol.get("tvl", 0),
                "change_1h": protocol.get("change_1h", 0),
                "change_1d": protocol.get("change_1d", 0),
                "change_7d": protocol.get("change_7d", 0),
                "category": protocol.get("category"),
                "chains": protocol.get("chains", []),
                "url": protocol.get("url")
            }
            processed_protocols.append(processed_protocol)
        
        # Sort by TVL descending
        processed_protocols.sort(key=lambda x: x["tvl"], reverse=True)
        
        return {
            "protocols": processed_protocols[:50],  # Top 50 protocols
            "total_count": len(processed_protocols),
            "timestamp": datetime.now().isoformat()
        }


# ============================================================================
# Analysis Tools
# ============================================================================

class TechnicalAnalysisTool(Tool):
    """Tool for technical analysis calculations"""
    
    def __init__(self):
        super().__init__(
            name="technical_analysis_tool",
            description="Perform technical analysis on price data"
        )
    
    async def run_json(self, args: Dict[str, Any], cancellation_token=None) -> Dict[str, Any]:
        """Perform technical analysis"""
        price_data = args.get("price_data", [])
        indicators = args.get("indicators", ["sma", "rsi", "macd"])
        
        if not price_data:
            return {"error": "No price data provided"}
        
        df = pd.DataFrame(price_data)
        results = {}
        
        for indicator in indicators:
            if indicator == "sma":
                results["sma_20"] = self._calculate_sma(df, 20)
                results["sma_50"] = self._calculate_sma(df, 50)
            elif indicator == "rsi":
                results["rsi"] = self._calculate_rsi(df)
            elif indicator == "macd":
                results["macd"] = self._calculate_macd(df)
            elif indicator == "bollinger":
                results["bollinger"] = self._calculate_bollinger_bands(df)
        
        return {
            "indicators": results,
            "timestamp": datetime.now().isoformat()
        }
    
    def _calculate_sma(self, df: pd.DataFrame, period: int) -> List[float]:
        """Calculate Simple Moving Average"""
        if "close" not in df.columns:
            return []
        return df["close"].rolling(window=period).mean().fillna(0).tolist()
    
    def _calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> List[float]:
        """Calculate Relative Strength Index"""
        if "close" not in df.columns:
            return []
        
        delta = df["close"].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50).tolist()
    
    def _calculate_macd(self, df: pd.DataFrame) -> Dict[str, List[float]]:
        """Calculate MACD indicator"""
        if "close" not in df.columns:
            return {"macd": [], "signal": [], "histogram": []}
        
        ema_12 = df["close"].ewm(span=12).mean()
        ema_26 = df["close"].ewm(span=26).mean()
        macd = ema_12 - ema_26
        signal = macd.ewm(span=9).mean()
        histogram = macd - signal
        
        return {
            "macd": macd.fillna(0).tolist(),
            "signal": signal.fillna(0).tolist(),
            "histogram": histogram.fillna(0).tolist()
        }
    
    def _calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20) -> Dict[str, List[float]]:
        """Calculate Bollinger Bands"""
        if "close" not in df.columns:
            return {"upper": [], "middle": [], "lower": []}
        
        sma = df["close"].rolling(window=period).mean()
        std = df["close"].rolling(window=period).std()
        upper = sma + (std * 2)
        lower = sma - (std * 2)
        
        return {
            "upper": upper.fillna(0).tolist(),
            "middle": sma.fillna(0).tolist(),
            "lower": lower.fillna(0).tolist()
        }


# ============================================================================
# Risk Management Tools
# ============================================================================

class RiskCalculatorTool(Tool):
    """Tool for risk calculations and position sizing"""
    
    def __init__(self):
        super().__init__(
            name="risk_calculator_tool",
            description="Calculate position sizes and risk metrics"
        )
    
    async def run_json(self, args: Dict[str, Any], cancellation_token=None) -> Dict[str, Any]:
        """Calculate risk metrics"""
        calculation_type = args.get("type", "position_size")
        
        if calculation_type == "position_size":
            return self._calculate_position_size(args)
        elif calculation_type == "var":
            return self._calculate_var(args)
        elif calculation_type == "sharpe_ratio":
            return self._calculate_sharpe_ratio(args)
        else:
            return {"error": f"Unknown calculation type: {calculation_type}"}
    
    def _calculate_position_size(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal position size based on risk parameters"""
        account_balance = args.get("account_balance", 10000)
        risk_per_trade = args.get("risk_per_trade", 0.02)  # 2%
        entry_price = args.get("entry_price", 0)
        stop_loss_price = args.get("stop_loss_price", 0)
        
        if entry_price <= 0 or stop_loss_price <= 0:
            return {"error": "Invalid entry or stop loss price"}
        
        risk_amount = account_balance * risk_per_trade
        price_risk = abs(entry_price - stop_loss_price)
        position_size = risk_amount / price_risk
        
        return {
            "position_size": position_size,
            "risk_amount": risk_amount,
            "price_risk": price_risk,
            "risk_percentage": risk_per_trade * 100
        }
    
    def _calculate_var(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        returns = args.get("returns", [])
        confidence_level = args.get("confidence_level", 0.95)
        
        if not returns:
            return {"error": "No returns data provided"}
        
        returns_series = pd.Series(returns)
        var = returns_series.quantile(1 - confidence_level)
        
        return {
            "var": var,
            "confidence_level": confidence_level,
            "worst_case_scenario": min(returns)
        }
    
    def _calculate_sharpe_ratio(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Sharpe ratio"""
        returns = args.get("returns", [])
        risk_free_rate = args.get("risk_free_rate", 0.02)  # 2% annual
        
        if not returns:
            return {"error": "No returns data provided"}
        
        returns_series = pd.Series(returns)
        excess_returns = returns_series.mean() - risk_free_rate / 252  # Daily risk-free rate
        volatility = returns_series.std()
        
        if volatility == 0:
            sharpe_ratio = 0
        else:
            sharpe_ratio = excess_returns / volatility * (252 ** 0.5)  # Annualized
        
        return {
            "sharpe_ratio": sharpe_ratio,
            "annual_return": returns_series.mean() * 252,
            "annual_volatility": volatility * (252 ** 0.5)
        }


# ============================================================================
# Tool Registry
# ============================================================================

def create_tool_registry(config: Dict[str, Any]) -> Dict[str, Tool]:
    """Create a registry of all available tools"""
    tools = {}
    
    # Data collection tools
    if "twitter" in config:
        tools["twitter_api"] = TwitterAPITool(
            api_key=config["twitter"]["api_key"],
            api_secret=config["twitter"]["api_secret"],
            bearer_token=config["twitter"]["bearer_token"]
        )
    
    if "etherscan" in config:
        tools["onchain_data"] = OnChainDataTool(
            etherscan_api_key=config["etherscan"]["api_key"]
        )
    
    tools["defi_data"] = DeFiDataTool()
    
    # Analysis tools
    tools["technical_analysis"] = TechnicalAnalysisTool()
    tools["risk_calculator"] = RiskCalculatorTool()
    
    return tools
