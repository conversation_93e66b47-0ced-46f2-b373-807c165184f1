"""
System Manager for Multi-Agent Crypto Trading System
Handles system initialization, configuration, and orchestration
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

try:
    from autogen_core import SingleThreadedAgentRuntime, TopicId
    from autogen_core.models import ChatCompletionClient
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    # Fallback classes
    class SingleThreadedAgentRuntime: pass
    class TopicId: pass
    class ChatCompletionClient: pass

from .database_manager import DatabaseManager
from .base_agent import BaseAgentWithDB


class SystemManager:
    """Central system manager for the multi-agent crypto trading system"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize system manager with configuration"""
        self._config = config
        self._logger = logging.getLogger(__name__)
        
        # Core components
        self._db_manager: Optional[DatabaseManager] = None
        self._runtime: Optional[SingleThreadedAgentRuntime] = None
        self._agents: Dict[str, BaseAgentWithDB] = {}
        
        # System state
        self._is_initialized = False
        self._is_running = False
        
        # Configuration validation
        self._validate_config()
    
    def _validate_config(self):
        """Validate system configuration"""
        required_keys = [
            "supabase_url", "supabase_key", "model_configs", "agent_configs"
        ]
        
        for key in required_keys:
            if key not in self._config:
                raise ValueError(f"Missing required configuration key: {key}")
        
        # Validate model configurations
        if not self._config["model_configs"]:
            raise ValueError("At least one model configuration is required")
        
        # Validate agent configurations
        if not self._config["agent_configs"]:
            raise ValueError("At least one agent configuration is required")
    
    async def initialize(self):
        """Initialize the system"""
        if self._is_initialized:
            self._logger.warning("System already initialized")
            return
        
        try:
            self._logger.info("Initializing multi-agent crypto trading system...")
            
            # Initialize database manager
            await self._initialize_database()
            
            # Initialize AutoGen runtime
            await self._initialize_runtime()
            
            # Initialize agents
            await self._initialize_agents()
            
            # Store system configuration
            await self._store_system_config()
            
            self._is_initialized = True
            self._logger.info("System initialization completed successfully")
            
        except Exception as e:
            self._logger.error(f"System initialization failed: {e}")
            await self.cleanup()
            raise
    
    async def _initialize_database(self):
        """Initialize database manager and verify connection"""
        try:
            self._db_manager = DatabaseManager(
                supabase_url=self._config["supabase_url"],
                supabase_key=self._config["supabase_key"]
            )
            
            # Test database connection by checking system config
            test_config = await self._db_manager._client.table("system_config").select("*").limit(1).execute()
            self._logger.info("Database connection established successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def _initialize_runtime(self):
        """Initialize AutoGen runtime"""
        if not AUTOGEN_AVAILABLE:
            self._logger.warning("AutoGen not available, using mock runtime")
            return
        
        try:
            self._runtime = SingleThreadedAgentRuntime()
            self._logger.info("AutoGen runtime initialized")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize runtime: {e}")
            raise
    
    async def _initialize_agents(self):
        """Initialize all configured agents"""
        try:
            agent_configs = self._config["agent_configs"]
            model_configs = self._config["model_configs"]
            
            for agent_name, agent_config in agent_configs.items():
                await self._initialize_single_agent(agent_name, agent_config, model_configs)
            
            self._logger.info(f"Initialized {len(self._agents)} agents")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize agents: {e}")
            raise
    
    async def _initialize_single_agent(self, agent_name: str, agent_config: Dict[str, Any], 
                                     model_configs: Dict[str, Any]):
        """Initialize a single agent"""
        try:
            # Get model client for this agent
            model_name = agent_config.get("model", "default")
            model_config = model_configs.get(model_name, model_configs["default"])
            
            # Create model client (mock for now)
            model_client = self._create_model_client(model_config)
            
            # Import and create agent instance
            agent_class = self._get_agent_class(agent_config["class"])
            agent = agent_class(
                description=agent_config.get("description", f"{agent_name} agent"),
                model_client=model_client,
                database_manager=self._db_manager,
                agent_config=agent_config
            )
            
            # Initialize agent
            await agent.initialize()
            
            # Register with runtime
            if self._runtime:
                await self._runtime.register(agent_name, lambda: agent)
            
            self._agents[agent_name] = agent
            self._logger.info(f"Initialized agent: {agent_name}")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize agent {agent_name}: {e}")
            raise
    
    def _create_model_client(self, model_config: Dict[str, Any]) -> ChatCompletionClient:
        """Create model client (mock implementation)"""
        # This would create actual model clients in production
        class MockModelClient:
            async def create(self, messages, temperature=0.7, max_tokens=2000):
                class MockResponse:
                    content = "Mock LLM response"
                return MockResponse()
        
        return MockModelClient()
    
    def _get_agent_class(self, class_name: str):
        """Get agent class by name"""
        # Import agent classes dynamically
        from ..agents.intelligence_collection import (
            WebIntelligenceAgent, WhaleActivityAgent, TVLMonitoringAgent, MemeTrendAgent
        )
        from ..agents.market_analysis import (
            TrendAggregationAgent, ContractAnalysisAgent
        )
        from ..agents.strategy_generation import (
            StrategyGenerationAgent, BacktestingAgent
        )
        from ..agents.execution_risk_management import (
            StrategyDeploymentAgent, TradeExecutionAgent, PerformanceTrackingAgent, 
            RiskManagementAgent, MemoryManagementAgent
        )
        from ..agents.control_interaction import (
            ParameterOptimizationAgent, TelegramNotificationAgent, SlackNotificationAgent
        )
        
        agent_classes = {
            "WebIntelligenceAgent": WebIntelligenceAgent,
            "WhaleActivityAgent": WhaleActivityAgent,
            "TVLMonitoringAgent": TVLMonitoringAgent,
            "MemeTrendAgent": MemeTrendAgent,
            "TrendAggregationAgent": TrendAggregationAgent,
            "ContractAnalysisAgent": ContractAnalysisAgent,
            "StrategyGenerationAgent": StrategyGenerationAgent,
            "BacktestingAgent": BacktestingAgent,
            "StrategyDeploymentAgent": StrategyDeploymentAgent,
            "TradeExecutionAgent": TradeExecutionAgent,
            "PerformanceTrackingAgent": PerformanceTrackingAgent,
            "RiskManagementAgent": RiskManagementAgent,
            "MemoryManagementAgent": MemoryManagementAgent,
            "ParameterOptimizationAgent": ParameterOptimizationAgent,
            "TelegramNotificationAgent": TelegramNotificationAgent,
            "SlackNotificationAgent": SlackNotificationAgent
        }
        
        if class_name not in agent_classes:
            raise ValueError(f"Unknown agent class: {class_name}")
        
        return agent_classes[class_name]
    
    async def _store_system_config(self):
        """Store system configuration in database"""
        try:
            config_data = {
                "config_key": "system_startup",
                "config_value": {
                    "startup_time": datetime.now(timezone.utc).isoformat(),
                    "agent_count": len(self._agents),
                    "agents": list(self._agents.keys()),
                    "version": "1.0.0"
                }
            }
            
            await self._db_manager._client.table("system_config").upsert(
                config_data, on_conflict="config_key"
            ).execute()
            
            self._logger.info("System configuration stored in database")
            
        except Exception as e:
            self._logger.error(f"Failed to store system config: {e}")
    
    async def start(self):
        """Start the system"""
        if not self._is_initialized:
            raise RuntimeError("System not initialized. Call initialize() first.")
        
        if self._is_running:
            self._logger.warning("System already running")
            return
        
        try:
            self._logger.info("Starting multi-agent crypto trading system...")
            
            # Start runtime
            if self._runtime:
                await self._runtime.start()
            
            # Start all agents
            for agent_name, agent in self._agents.items():
                try:
                    # Agents are already initialized, just mark as running
                    await agent.set_state("running", True)
                    self._logger.info(f"Started agent: {agent_name}")
                except Exception as e:
                    self._logger.error(f"Failed to start agent {agent_name}: {e}")
            
            self._is_running = True
            self._logger.info("System started successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to start system: {e}")
            raise
    
    async def stop(self):
        """Stop the system"""
        if not self._is_running:
            self._logger.warning("System not running")
            return
        
        try:
            self._logger.info("Stopping multi-agent crypto trading system...")
            
            # Stop all agents
            for agent_name, agent in self._agents.items():
                try:
                    await agent.set_state("running", False)
                    await agent.cleanup()
                    self._logger.info(f"Stopped agent: {agent_name}")
                except Exception as e:
                    self._logger.error(f"Failed to stop agent {agent_name}: {e}")
            
            # Stop runtime
            if self._runtime:
                await self._runtime.stop()
            
            self._is_running = False
            self._logger.info("System stopped successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to stop system: {e}")
    
    async def cleanup(self):
        """Cleanup system resources"""
        try:
            if self._is_running:
                await self.stop()
            
            # Cleanup agents
            for agent in self._agents.values():
                try:
                    await agent.cleanup()
                except Exception as e:
                    self._logger.error(f"Error cleaning up agent: {e}")
            
            self._agents.clear()
            self._db_manager = None
            self._runtime = None
            self._is_initialized = False
            
            self._logger.info("System cleanup completed")
            
        except Exception as e:
            self._logger.error(f"Error during cleanup: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        return {
            "initialized": self._is_initialized,
            "running": self._is_running,
            "agent_count": len(self._agents),
            "agents": {name: agent.get_agent_info() for name, agent in self._agents.items()},
            "database_connected": self._db_manager is not None,
            "runtime_active": self._runtime is not None
        }
    
    def get_agent(self, agent_name: str) -> Optional[BaseAgentWithDB]:
        """Get agent by name"""
        return self._agents.get(agent_name)
    
    def get_database_manager(self) -> Optional[DatabaseManager]:
        """Get database manager"""
        return self._db_manager
