"""
Message Protocol Definitions for Multi-Agent Crypto Trading System

This module defines all message types used for communication between agents
following AutoGen's latest message protocol patterns.
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


# ============================================================================
# Base Message Types
# ============================================================================

@dataclass
class BaseMessage:
    """Base message class for all agent communications"""
    timestamp: datetime
    source_agent: str
    message_id: str


# ============================================================================
# Intelligence Collection Layer Messages
# ============================================================================

@dataclass
class WebIntelligenceData(BaseMessage):
    """Web intelligence data from social media and news sources"""
    platform: str  # twitter, reddit, news
    content: str
    sentiment_score: float
    engagement_metrics: Dict[str, int]
    keywords: List[str]
    confidence_score: float


@dataclass
class WhaleActivityData(BaseMessage):
    """Whale tracking data for large transactions"""
    transaction_hash: str
    from_address: str
    to_address: str
    token_symbol: str
    amount: float
    usd_value: float
    chain: str
    transaction_type: str  # buy, sell, transfer


@dataclass
class TVLData(BaseMessage):
    """Total Value Locked data for DeFi protocols"""
    protocol_name: str
    chain: str
    current_tvl: float
    tvl_change_24h: float
    tvl_change_7d: float
    category: str  # dex, lending, yield_farming


@dataclass
class MemeTokenTrend(BaseMessage):
    """Meme token trending data"""
    token_symbol: str
    token_address: str
    chain: str
    mention_count: int
    sentiment_score: float
    price_change_24h: float
    volume_24h: float
    trending_rank: int


# ============================================================================
# Market Analysis Layer Messages
# ============================================================================

@dataclass
class TrendSignal(BaseMessage):
    """Aggregated trend signal from multiple intelligence sources"""
    signal_type: str  # bullish, bearish, neutral
    strength: float  # 0.0 to 1.0
    timeframe: str  # short, medium, long
    supporting_data: List[str]
    confidence_level: float
    target_assets: List[str]


@dataclass
class ContractAnalysisResult(BaseMessage):
    """Smart contract analysis results"""
    contract_address: str
    chain: str
    risk_score: float  # 0.0 to 1.0
    vulnerabilities: List[str]
    audit_status: str
    liquidity_analysis: Dict[str, Any]
    recommendation: str  # safe, caution, avoid


# ============================================================================
# Strategy Generation & Backtesting Layer Messages
# ============================================================================

@dataclass
class StrategyRequest(BaseMessage):
    """Request for strategy generation"""
    strategy_type: str  # momentum, mean_reversion, arbitrage
    target_assets: List[str]
    timeframe: str
    risk_tolerance: float
    market_conditions: Dict[str, Any]
    constraints: Dict[str, Any]


@dataclass
class GeneratedStrategy(BaseMessage):
    """Generated trading strategy"""
    strategy_id: str
    strategy_name: str
    description: str
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_management: Dict[str, Any]
    expected_return: float
    max_drawdown: float
    parameters: Dict[str, Any]


@dataclass
class StrategyCode(BaseMessage):
    """Executable strategy code"""
    strategy_id: str
    code: str
    language: str  # python, pine_script
    dependencies: List[str]
    entry_points: Dict[str, str]
    test_cases: List[Dict[str, Any]]


@dataclass
class BacktestRequest(BaseMessage):
    """Backtest execution request"""
    strategy_id: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    assets: List[str]
    benchmark: str


@dataclass
class BacktestResult(BaseMessage):
    """Backtest execution results"""
    strategy_id: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    trades_count: int
    performance_metrics: Dict[str, float]
    equity_curve: List[Dict[str, Any]]


# ============================================================================
# Execution & Risk Management Layer Messages
# ============================================================================

@dataclass
class DeploymentRequest(BaseMessage):
    """Strategy deployment request"""
    strategy_id: str
    allocation: float
    exchange: str
    account_type: str  # paper, live


@dataclass
class StrategyDeployment(BaseMessage):
    """Strategy deployment status and configuration"""
    deployment_id: str
    strategy_name: str
    deployment_status: str  # paper_trading, live_small, live_full, paused, stopped
    allocated_capital: float
    risk_limits: Dict[str, Any]
    performance_targets: Dict[str, Any]


@dataclass
class TradeSignal(BaseMessage):
    """Trading signal for execution"""
    strategy_id: str
    symbol: str
    action: str  # buy, sell, hold
    quantity: float
    price: Optional[float]
    order_type: str  # market, limit, stop
    urgency: str  # low, medium, high


@dataclass
class TradeExecution(BaseMessage):
    """Trade execution result"""
    trade_id: str
    strategy_id: str
    symbol: str
    action: str
    quantity: float
    executed_price: float
    execution_time: datetime
    fees: float
    status: str  # filled, partial, failed


@dataclass
class RiskAlert(BaseMessage):
    """Risk management alert"""
    alert_type: str  # position_limit, drawdown, volatility
    severity: str  # low, medium, high, critical
    strategy_id: str
    current_value: float
    threshold: float
    recommended_action: str


@dataclass
class PerformanceUpdate(BaseMessage):
    """Real-time performance update"""
    strategy_id: str
    current_pnl: float
    daily_pnl: float
    total_return: float
    current_drawdown: float
    active_positions: List[Dict[str, Any]]


# ============================================================================
# Control & Interaction Layer Messages
# ============================================================================

@dataclass
class OptimizationRequest(BaseMessage):
    """Parameter optimization request"""
    strategy_id: str
    parameters_to_optimize: List[str]
    optimization_metric: str  # sharpe, return, drawdown
    constraints: Dict[str, Any]


@dataclass
class OptimizationResult(BaseMessage):
    """Parameter optimization results"""
    strategy_id: str
    optimized_parameters: Dict[str, Any]
    performance_improvement: float
    confidence_score: float


@dataclass
class TelegramNotification(BaseMessage):
    """Telegram bot notification"""
    chat_id: str
    message_text: str
    message_type: str  # alert, update, report
    priority: str  # low, medium, high


@dataclass
class SlackNotification(BaseMessage):
    """Slack notification"""
    channel: str
    message_text: str
    attachments: Optional[List[Dict[str, Any]]]
    priority: str


# ============================================================================
# System Control Messages
# ============================================================================

@dataclass
class SystemCommand(BaseMessage):
    """System-level command"""
    command: str  # start, stop, pause, resume
    target_agent: Optional[str]
    parameters: Dict[str, Any]


@dataclass
class HealthCheck(BaseMessage):
    """Agent health check"""
    agent_name: str
    status: str  # healthy, warning, error
    last_activity: datetime
    performance_metrics: Dict[str, float]


@dataclass
class ConfigUpdate(BaseMessage):
    """Configuration update message"""
    config_section: str
    updates: Dict[str, Any]
    apply_immediately: bool


# ============================================================================
# Control & Interaction Layer Messages (Additional)
# ============================================================================

@dataclass
class TelegramMessage(BaseMessage):
    """Telegram bot message"""
    chat_id: int
    text: str
    parse_mode: Optional[str] = None
    reply_markup: Optional[Dict[str, Any]] = None

@dataclass
class SlackMessage(BaseMessage):
    """Slack notification message"""
    channel: str
    text: str
    blocks: Optional[List[Dict[str, Any]]] = None
    attachments: Optional[List[Dict[str, Any]]] = None

@dataclass
class PerformanceAlert(BaseMessage):
    """Performance alert message"""
    strategy_name: str
    alert_type: str
    alert_level: str  # info, warning, critical, emergency
    message: str
    current_value: Any
    threshold_value: Any
    recommended_actions: List[str]

@dataclass
class PerformanceReport(BaseMessage):
    """Performance analysis report"""
    strategy_name: str
    report_period: str
    performance_metrics: Dict[str, Any]
    analysis_summary: str
    key_insights: List[str]
    recommendations: List[str]

@dataclass
class MemoryQuery(BaseMessage):
    """Memory system query"""
    strategy_name: str
    query_type: str
    parameters: Dict[str, Any]

@dataclass
class MemoryResponse(BaseMessage):
    """Memory system response"""
    strategy_name: str
    query_type: str
    data: Dict[str, Any]
    insights: List[str]

@dataclass
class StrategyDefinition(BaseMessage):
    """Strategy definition for optimization"""
    strategy_name: str
    parameters: Dict[str, Any]
    constraints: Dict[str, Any]
    objectives: List[str]

# ============================================================================
# Message Type Registry
# ============================================================================

MESSAGE_TYPES = {
    # Intelligence Collection
    "web_intelligence": WebIntelligenceData,
    "whale_activity": WhaleActivityData,
    "tvl_data": TVLData,
    "meme_trend": MemeTokenTrend,

    # Market Analysis
    "trend_signal": TrendSignal,
    "contract_analysis": ContractAnalysisResult,

    # Strategy Generation & Backtesting
    "strategy_request": StrategyRequest,
    "generated_strategy": GeneratedStrategy,
    "strategy_code": StrategyCode,
    "backtest_request": BacktestRequest,
    "backtest_result": BacktestResult,

    # Execution & Risk Management
    "deployment_request": DeploymentRequest,
    "strategy_deployment": StrategyDeployment,
    "trade_signal": TradeSignal,
    "trade_execution": TradeExecution,
    "risk_alert": RiskAlert,
    "performance_update": PerformanceUpdate,
    "performance_alert": PerformanceAlert,
    "performance_report": PerformanceReport,
    "memory_query": MemoryQuery,
    "memory_response": MemoryResponse,

    # Control & Interaction
    "optimization_request": OptimizationRequest,
    "optimization_result": OptimizationResult,
    "telegram_notification": TelegramNotification,
    "slack_notification": SlackNotification,
    "telegram_message": TelegramMessage,
    "slack_message": SlackMessage,
    "strategy_definition": StrategyDefinition,

    # System Control
    "system_command": SystemCommand,
    "health_check": HealthCheck,
    "config_update": ConfigUpdate,
}
