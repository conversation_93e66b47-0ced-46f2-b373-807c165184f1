"""
AutoGen Runtime Configuration for Multi-Agent Crypto Trading System

This module sets up the AutoGen runtime environment and manages agent
communication using the latest AutoGen patterns.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from autogen_core import (
    SingleThreadedAgentRuntime,
    DistributedAgentRuntime,
    AgentId,
    TopicId,
    TypeSubscription,
    MessageContext,
    RoutedAgent,
    message_handler,
)
from autogen_core.models import ChatCompletionClient
from autogen_ext.models.openai import OpenAIChatCompletionClient

from .messages import *


class CryptoTradingRuntime:
    """
    Main runtime manager for the crypto trading multi-agent system.
    
    This class orchestrates all agents and manages their communication
    using AutoGen's latest patterns.
    """
    
    def __init__(
        self,
        use_distributed: bool = False,
        model_configs: Optional[Dict[str, Dict[str, Any]]] = None
    ):
        self.use_distributed = use_distributed
        self.model_configs = model_configs or self._default_model_configs()
        self.runtime: Optional[SingleThreadedAgentRuntime] = None
        self.model_clients: Dict[str, ChatCompletionClient] = {}
        self.agents: Dict[str, AgentId] = {}
        self.subscriptions: List[TypeSubscription] = []
        self.logger = logging.getLogger(__name__)
        
    def _default_model_configs(self) -> Dict[str, Dict[str, Any]]:
        """Default model configurations for different agent types"""
        return {
            "intelligence": {
                "model": "gpt-4o-mini",
                "temperature": 0.3,
                "max_tokens": 2000,
            },
            "analysis": {
                "model": "gpt-4o",
                "temperature": 0.1,
                "max_tokens": 4000,
            },
            "strategy": {
                "model": "gpt-4o",
                "temperature": 0.2,
                "max_tokens": 8000,
            },
            "execution": {
                "model": "gpt-4o-mini",
                "temperature": 0.0,
                "max_tokens": 1000,
            },
            "control": {
                "model": "gpt-4o-mini",
                "temperature": 0.1,
                "max_tokens": 2000,
            }
        }
    
    async def initialize(self) -> None:
        """Initialize the runtime and model clients"""
        self.logger.info("Initializing CryptoTradingRuntime...")
        
        # Initialize runtime
        if self.use_distributed:
            # For production deployment with multiple nodes
            self.runtime = DistributedAgentRuntime(host="localhost", port=50051)
        else:
            # For development and single-node deployment
            self.runtime = SingleThreadedAgentRuntime()
        
        # Initialize model clients
        for agent_type, config in self.model_configs.items():
            self.model_clients[agent_type] = OpenAIChatCompletionClient(
                model=config["model"],
                # Add other configuration parameters as needed
            )
        
        self.logger.info("Runtime initialization complete")
    
    async def register_intelligence_agents(self) -> None:
        """Register all intelligence collection layer agents"""
        from ..agents.intelligence import (
            WebIntelligenceAgent,
            WhaleTrackingAgent,
            TVLMonitoringAgent,
            MemeTokenTrendAgent
        )
        
        # Web Intelligence Agent
        web_intel_agent = await WebIntelligenceAgent.register(
            self.runtime,
            "WebIntelligenceAgent",
            lambda: WebIntelligenceAgent(
                model_client=self.model_clients["intelligence"],
                description="Monitors social media and news for crypto trends"
            )
        )
        self.agents["web_intelligence"] = web_intel_agent
        
        # Whale Tracking Agent
        whale_agent = await WhaleTrackingAgent.register(
            self.runtime,
            "WhaleTrackingAgent", 
            lambda: WhaleTrackingAgent(
                model_client=self.model_clients["intelligence"],
                description="Tracks large on-chain transactions and whale activity"
            )
        )
        self.agents["whale_tracking"] = whale_agent
        
        # TVL Monitoring Agent
        tvl_agent = await TVLMonitoringAgent.register(
            self.runtime,
            "TVLMonitoringAgent",
            lambda: TVLMonitoringAgent(
                model_client=self.model_clients["intelligence"],
                description="Monitors TVL changes across DeFi protocols"
            )
        )
        self.agents["tvl_monitoring"] = tvl_agent
        
        # Meme Token Trend Agent
        meme_agent = await MemeTokenTrendAgent.register(
            self.runtime,
            "MemeTokenTrendAgent",
            lambda: MemeTokenTrendAgent(
                model_client=self.model_clients["intelligence"],
                description="Tracks meme token trends and social sentiment"
            )
        )
        self.agents["meme_trend"] = meme_agent
        
        self.logger.info("Intelligence collection agents registered")
    
    async def register_analysis_agents(self) -> None:
        """Register all market analysis layer agents"""
        from ..agents.analysis import (
            TrendAggregationAgent,
            ContractAnalysisAgent
        )
        
        # Trend Aggregation Agent
        trend_agent = await TrendAggregationAgent.register(
            self.runtime,
            "TrendAggregationAgent",
            lambda: TrendAggregationAgent(
                model_client=self.model_clients["analysis"],
                description="Aggregates and analyzes trends from multiple sources"
            )
        )
        self.agents["trend_aggregation"] = trend_agent
        
        # Contract Analysis Agent
        contract_agent = await ContractAnalysisAgent.register(
            self.runtime,
            "ContractAnalysisAgent",
            lambda: ContractAnalysisAgent(
                model_client=self.model_clients["analysis"],
                description="Analyzes smart contracts for risks and opportunities"
            )
        )
        self.agents["contract_analysis"] = contract_agent
        
        self.logger.info("Market analysis agents registered")
    
    async def register_strategy_agents(self) -> None:
        """Register all strategy generation and backtesting agents"""
        from ..agents.strategy import (
            StrategyGenerationAgent,
            StrategyCodingAgent,
            StrategyBacktestingAgent
        )
        
        # Strategy Generation Agent
        strategy_gen_agent = await StrategyGenerationAgent.register(
            self.runtime,
            "StrategyGenerationAgent",
            lambda: StrategyGenerationAgent(
                model_client=self.model_clients["strategy"],
                description="Generates trading strategies based on market analysis"
            )
        )
        self.agents["strategy_generation"] = strategy_gen_agent
        
        # Strategy Coding Agent
        strategy_code_agent = await StrategyCodingAgent.register(
            self.runtime,
            "StrategyCodingAgent",
            lambda: StrategyCodingAgent(
                model_client=self.model_clients["strategy"],
                description="Converts strategy logic into executable code"
            )
        )
        self.agents["strategy_coding"] = strategy_code_agent
        
        # Strategy Backtesting Agent
        backtest_agent = await StrategyBacktestingAgent.register(
            self.runtime,
            "StrategyBacktestingAgent",
            lambda: StrategyBacktestingAgent(
                model_client=self.model_clients["strategy"],
                description="Executes backtests and evaluates strategy performance"
            )
        )
        self.agents["strategy_backtesting"] = backtest_agent
        
        self.logger.info("Strategy generation and backtesting agents registered")
    
    async def register_execution_agents(self) -> None:
        """Register all execution and risk management agents"""
        from ..agents.execution import (
            StrategyDeploymentAgent,
            RealTimeMonitoringAgent,
            PerformanceAnalysisAgent,
            StrategyMemoryAgent
        )
        
        # Strategy Deployment Agent
        deployment_agent = await StrategyDeploymentAgent.register(
            self.runtime,
            "StrategyDeploymentAgent",
            lambda: StrategyDeploymentAgent(
                model_client=self.model_clients["execution"],
                description="Deploys strategies to live trading environments"
            )
        )
        self.agents["strategy_deployment"] = deployment_agent
        
        # Real-time Monitoring Agent
        monitoring_agent = await RealTimeMonitoringAgent.register(
            self.runtime,
            "RealTimeMonitoringAgent",
            lambda: RealTimeMonitoringAgent(
                model_client=self.model_clients["execution"],
                description="Monitors strategy performance and market conditions"
            )
        )
        self.agents["realtime_monitoring"] = monitoring_agent
        
        # Performance Analysis Agent
        performance_agent = await PerformanceAnalysisAgent.register(
            self.runtime,
            "PerformanceAnalysisAgent",
            lambda: PerformanceAnalysisAgent(
                model_client=self.model_clients["execution"],
                description="Analyzes strategy performance and generates reports"
            )
        )
        self.agents["performance_analysis"] = performance_agent
        
        # Strategy Memory Agent
        memory_agent = await StrategyMemoryAgent.register(
            self.runtime,
            "StrategyMemoryAgent",
            lambda: StrategyMemoryAgent(
                model_client=self.model_clients["execution"],
                description="Stores and retrieves strategy performance data"
            )
        )
        self.agents["strategy_memory"] = memory_agent
        
        self.logger.info("Execution and risk management agents registered")
    
    async def register_control_agents(self) -> None:
        """Register all control and interaction layer agents"""
        from ..agents.control import (
            ParameterOptimizationAgent,
            TelegramBotAgent,
            SlackNotificationAgent
        )
        
        # Parameter Optimization Agent
        optimization_agent = await ParameterOptimizationAgent.register(
            self.runtime,
            "ParameterOptimizationAgent",
            lambda: ParameterOptimizationAgent(
                model_client=self.model_clients["control"],
                description="Optimizes strategy parameters based on performance"
            )
        )
        self.agents["parameter_optimization"] = optimization_agent
        
        # Telegram Bot Agent
        telegram_agent = await TelegramBotAgent.register(
            self.runtime,
            "TelegramBotAgent",
            lambda: TelegramBotAgent(
                model_client=self.model_clients["control"],
                description="Provides mobile interface for system control"
            )
        )
        self.agents["telegram_bot"] = telegram_agent
        
        # Slack Notification Agent
        slack_agent = await SlackNotificationAgent.register(
            self.runtime,
            "SlackNotificationAgent",
            lambda: SlackNotificationAgent(
                model_client=self.model_clients["control"],
                description="Sends notifications and alerts to Slack channels"
            )
        )
        self.agents["slack_notification"] = slack_agent
        
        self.logger.info("Control and interaction agents registered")
    
    async def setup_subscriptions(self) -> None:
        """Set up message subscriptions between agents"""
        # Intelligence -> Analysis subscriptions
        intelligence_to_analysis = [
            TypeSubscription("WebIntelligenceAgent", "TrendAggregationAgent"),
            TypeSubscription("WhaleTrackingAgent", "TrendAggregationAgent"),
            TypeSubscription("TVLMonitoringAgent", "TrendAggregationAgent"),
            TypeSubscription("MemeTokenTrendAgent", "TrendAggregationAgent"),
        ]
        
        # Analysis -> Strategy subscriptions
        analysis_to_strategy = [
            TypeSubscription("TrendAggregationAgent", "StrategyGenerationAgent"),
            TypeSubscription("ContractAnalysisAgent", "StrategyGenerationAgent"),
        ]
        
        # Strategy -> Execution subscriptions
        strategy_to_execution = [
            TypeSubscription("StrategyGenerationAgent", "StrategyCodingAgent"),
            TypeSubscription("StrategyCodingAgent", "StrategyBacktestingAgent"),
            TypeSubscription("StrategyBacktestingAgent", "StrategyDeploymentAgent"),
        ]
        
        # Execution -> Control subscriptions
        execution_to_control = [
            TypeSubscription("PerformanceAnalysisAgent", "ParameterOptimizationAgent"),
            TypeSubscription("RealTimeMonitoringAgent", "TelegramBotAgent"),
            TypeSubscription("RealTimeMonitoringAgent", "SlackNotificationAgent"),
        ]
        
        # Add all subscriptions
        all_subscriptions = (
            intelligence_to_analysis + 
            analysis_to_strategy + 
            strategy_to_execution + 
            execution_to_control
        )
        
        for subscription in all_subscriptions:
            await self.runtime.add_subscription(subscription)
            self.subscriptions.append(subscription)
        
        self.logger.info(f"Set up {len(all_subscriptions)} agent subscriptions")
    
    async def start_system(self) -> None:
        """Start the entire multi-agent system"""
        self.logger.info("Starting crypto trading multi-agent system...")
        
        await self.initialize()
        await self.register_intelligence_agents()
        await self.register_analysis_agents()
        await self.register_strategy_agents()
        await self.register_execution_agents()
        await self.register_control_agents()
        await self.setup_subscriptions()
        
        # Start the runtime
        self.runtime.start()
        
        self.logger.info("Multi-agent system started successfully")
    
    async def stop_system(self) -> None:
        """Stop the multi-agent system gracefully"""
        self.logger.info("Stopping crypto trading multi-agent system...")
        
        if self.runtime:
            await self.runtime.stop_when_idle()
        
        # Close model clients
        for client in self.model_clients.values():
            await client.close()
        
        self.logger.info("Multi-agent system stopped")
    
    async def send_message(self, message: BaseMessage, recipient_agent: str) -> Any:
        """Send a message to a specific agent"""
        if recipient_agent not in self.agents:
            raise ValueError(f"Agent {recipient_agent} not found")
        
        agent_id = self.agents[recipient_agent]
        return await self.runtime.send_message(message, agent_id)
    
    async def publish_message(self, message: BaseMessage, topic_type: str) -> None:
        """Publish a message to a topic"""
        topic_id = TopicId(topic_type, source="system")
        await self.runtime.publish_message(message, topic_id)
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all registered agents"""
        return {
            "total_agents": len(self.agents),
            "agents": list(self.agents.keys()),
            "subscriptions": len(self.subscriptions),
            "runtime_type": "distributed" if self.use_distributed else "single_threaded"
        }
