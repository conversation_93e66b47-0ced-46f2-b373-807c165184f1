"""
Base Agent Class with Database Integration
Provides common functionality for all agents in the multi-agent crypto trading system
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

try:
    from autogen_core import RoutedAgent, message_handler, TopicId
    from autogen_core.models import ChatCompletionClient
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    # Fallback classes for development
    class RoutedAgent:
        def __init__(self, description: str): pass
    def message_handler(func): return func
    class TopicId: pass
    class ChatCompletionClient: pass

from .database_manager import DatabaseManager
from .messages import BaseMessage


class BaseAgentWithDB(RoutedAgent):
    """Base agent class with database integration capabilities"""
    
    def __init__(self, description: str, model_client: ChatCompletionClient,
                 database_manager: DatabaseManager, agent_config: Dict[str, Any] = None):
        """Initialize base agent with database integration"""
        super().__init__(description)
        
        self._model_client = model_client
        self._db = database_manager
        self._config = agent_config or {}
        self._logger = logging.getLogger(self.__class__.__name__)
        
        # Agent metadata
        self._agent_name = self.__class__.__name__
        self._agent_type = self._get_agent_type()
        self._layer = self._get_layer()
        
        # State management
        self._state = {}
        self._last_heartbeat = None
        
        # Initialize agent in database
        asyncio.create_task(self._initialize_in_database())
    
    def _get_agent_type(self) -> str:
        """Get agent type based on class name"""
        class_name = self.__class__.__name__
        if "Intelligence" in class_name or "Monitoring" in class_name or "Tracking" in class_name or "Trend" in class_name:
            return "intelligence_collector"
        elif "Analysis" in class_name or "Aggregation" in class_name:
            return "market_analyzer"
        elif "Strategy" in class_name or "Backtest" in class_name:
            return "strategy_manager"
        elif "Deployment" in class_name or "Execution" in class_name or "Risk" in class_name or "Performance" in class_name or "Memory" in class_name:
            return "execution_manager"
        elif "Optimization" in class_name or "Telegram" in class_name or "Slack" in class_name:
            return "control_interface"
        else:
            return "unknown"
    
    def _get_layer(self) -> str:
        """Get layer based on agent type"""
        agent_type = self._agent_type
        if agent_type == "intelligence_collector":
            return "intelligence_collection"
        elif agent_type == "market_analyzer":
            return "market_analysis"
        elif agent_type == "strategy_manager":
            return "strategy_generation"
        elif agent_type == "execution_manager":
            return "execution_risk_management"
        elif agent_type == "control_interface":
            return "control_interaction"
        else:
            return "unknown"
    
    async def _initialize_in_database(self):
        """Initialize agent in database"""
        try:
            agent_id = await self._db.register_agent(
                agent_name=self._agent_name,
                agent_type=self._agent_type,
                layer=self._layer,
                configuration=self._config
            )
            self._state["agent_id"] = agent_id
            self._logger.info(f"Agent {self._agent_name} initialized in database with ID {agent_id}")
            
            # Start heartbeat
            asyncio.create_task(self._heartbeat_loop())
            
        except Exception as e:
            self._logger.error(f"Failed to initialize agent in database: {e}")
    
    async def _heartbeat_loop(self):
        """Send periodic heartbeats to database"""
        while True:
            try:
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
                success = await self._db.update_agent_heartbeat(self._agent_name)
                if success:
                    self._last_heartbeat = datetime.now(timezone.utc)
                else:
                    self._logger.warning(f"Failed to update heartbeat for {self._agent_name}")
                    
            except Exception as e:
                self._logger.error(f"Error in heartbeat loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def log_message(self, message: BaseMessage, target_agent: str = None, 
                         topic_id: str = None) -> str:
        """Log a message to the database"""
        try:
            return await self._db.log_message(message, target_agent, topic_id)
        except Exception as e:
            self._logger.error(f"Failed to log message: {e}")
            return ""
    
    async def mark_message_processed(self, message_id: str) -> bool:
        """Mark a message as processed"""
        try:
            return await self._db.mark_message_processed(message_id)
        except Exception as e:
            self._logger.error(f"Failed to mark message processed: {e}")
            return False
    
    async def get_state(self, key: str = None) -> Any:
        """Get agent state"""
        if key:
            return self._state.get(key)
        return self._state.copy()
    
    async def set_state(self, key: str, value: Any):
        """Set agent state"""
        self._state[key] = value
    
    async def update_state(self, updates: Dict[str, Any]):
        """Update multiple state values"""
        self._state.update(updates)
    
    async def get_agent_status(self) -> Optional[Dict[str, Any]]:
        """Get agent status from database"""
        try:
            return await self._db.get_agent_status(self._agent_name)
        except Exception as e:
            self._logger.error(f"Failed to get agent status: {e}")
            return None
    
    async def get_message_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get message history for this agent"""
        try:
            return await self._db.get_message_history(self._agent_name, limit)
        except Exception as e:
            self._logger.error(f"Failed to get message history: {e}")
            return []
    
    # ============================================================================
    # Abstract Methods - Must be implemented by subclasses
    # ============================================================================
    
    @abstractmethod
    async def initialize(self):
        """Initialize agent-specific resources"""
        pass
    
    @abstractmethod
    async def process_message(self, message: BaseMessage) -> Optional[BaseMessage]:
        """Process incoming message and return response if needed"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Cleanup agent resources"""
        pass
    
    # ============================================================================
    # Utility Methods
    # ============================================================================
    
    async def send_message_with_logging(self, message: BaseMessage, topic_id: TopicId):
        """Send message and log to database"""
        try:
            # Log message to database
            await self.log_message(message, topic_id=str(topic_id))
            
            # Send message via AutoGen
            await self.publish_message(message, topic_id)
            
            self._logger.debug(f"Sent message {message.message_id} to topic {topic_id}")
            
        except Exception as e:
            self._logger.error(f"Failed to send message: {e}")
    
    async def get_llm_response(self, system_message: str, user_message: str, 
                             temperature: float = 0.7) -> str:
        """Get response from LLM model"""
        try:
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ]
            
            response = await self._model_client.create(
                messages=messages,
                temperature=temperature,
                max_tokens=2000
            )
            
            return response.content
            
        except Exception as e:
            self._logger.error(f"Failed to get LLM response: {e}")
            return ""
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information"""
        return {
            "agent_name": self._agent_name,
            "agent_type": self._agent_type,
            "layer": self._layer,
            "last_heartbeat": self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            "state_keys": list(self._state.keys()),
            "config": self._config
        }


class IntelligenceCollectorAgent(BaseAgentWithDB):
    """Base class for intelligence collection agents"""
    
    def __init__(self, description: str, model_client: ChatCompletionClient,
                 database_manager: DatabaseManager, agent_config: Dict[str, Any] = None):
        super().__init__(description, model_client, database_manager, agent_config)
    
    async def store_intelligence_data(self, data_type: str, data: Dict[str, Any]) -> str:
        """Store intelligence data in database"""
        try:
            if data_type == "web_intelligence":
                return await self._db.store_web_intelligence(**data)
            elif data_type == "whale_activity":
                return await self._db.store_whale_activity(**data)
            elif data_type == "tvl_data":
                return await self._db.store_tvl_data(**data)
            elif data_type == "meme_trend":
                return await self._db.store_meme_trend(**data)
            else:
                raise ValueError(f"Unknown data type: {data_type}")
                
        except Exception as e:
            self._logger.error(f"Failed to store intelligence data: {e}")
            raise


class MarketAnalyzerAgent(BaseAgentWithDB):
    """Base class for market analysis agents"""
    
    def __init__(self, description: str, model_client: ChatCompletionClient,
                 database_manager: DatabaseManager, agent_config: Dict[str, Any] = None):
        super().__init__(description, model_client, database_manager, agent_config)
    
    async def store_analysis_result(self, analysis_type: str, data: Dict[str, Any]) -> str:
        """Store analysis result in database"""
        try:
            if analysis_type == "trend_signal":
                return await self._db.store_trend_signal(**data)
            elif analysis_type == "contract_analysis":
                return await self._db.store_contract_analysis(**data)
            else:
                raise ValueError(f"Unknown analysis type: {analysis_type}")
                
        except Exception as e:
            self._logger.error(f"Failed to store analysis result: {e}")
            raise


class StrategyManagerAgent(BaseAgentWithDB):
    """Base class for strategy management agents"""
    
    def __init__(self, description: str, model_client: ChatCompletionClient,
                 database_manager: DatabaseManager, agent_config: Dict[str, Any] = None):
        super().__init__(description, model_client, database_manager, agent_config)
    
    async def store_strategy(self, **kwargs) -> str:
        """Store strategy in database"""
        return await self._db.store_strategy(**kwargs)
    
    async def store_backtest_result(self, **kwargs) -> str:
        """Store backtest result in database"""
        return await self._db.store_backtest_result(**kwargs)


class ExecutionManagerAgent(BaseAgentWithDB):
    """Base class for execution and risk management agents"""
    
    def __init__(self, description: str, model_client: ChatCompletionClient,
                 database_manager: DatabaseManager, agent_config: Dict[str, Any] = None):
        super().__init__(description, model_client, database_manager, agent_config)
    
    async def create_deployment(self, **kwargs) -> str:
        """Create strategy deployment"""
        return await self._db.create_strategy_deployment(**kwargs)
    
    async def store_trade(self, **kwargs) -> str:
        """Store trade execution"""
        return await self._db.store_trade_execution(**kwargs)
    
    async def store_performance(self, **kwargs) -> str:
        """Store performance data"""
        return await self._db.store_performance_data(**kwargs)


class ControlInterfaceAgent(BaseAgentWithDB):
    """Base class for control and interface agents"""
    
    def __init__(self, description: str, model_client: ChatCompletionClient,
                 database_manager: DatabaseManager, agent_config: Dict[str, Any] = None):
        super().__init__(description, model_client, database_manager, agent_config)
    
    async def store_optimization_result(self, **kwargs) -> str:
        """Store optimization result"""
        return await self._db.store_optimization_result(**kwargs)
    
    async def store_notification(self, **kwargs) -> str:
        """Store notification"""
        return await self._db.store_notification(**kwargs)
