"""
Database Manager for Multi-Agent Crypto Trading System
Provides centralized database operations and state management for all agents
"""

import asyncio
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import asdict
import uuid

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False

from .messages import BaseMessage, MESSAGE_TYPES


class DatabaseManager:
    """Centralized database manager for all agent state and data persistence"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """Initialize database manager with Supabase connection"""
        if not SUPABASE_AVAILABLE:
            raise ImportError("supabase-py is required for database operations")
        
        self._client: Client = create_client(supabase_url, supabase_key)
        self._logger = logging.getLogger(__name__)
        
    # ============================================================================
    # Agent Management
    # ============================================================================
    
    async def register_agent(self, agent_name: str, agent_type: str, layer: str, 
                           configuration: Dict[str, Any] = None) -> str:
        """Register an agent in the database"""
        try:
            data = {
                "agent_name": agent_name,
                "agent_type": agent_type,
                "layer": layer,
                "status": "active",
                "configuration": configuration or {},
                "last_heartbeat": datetime.now(timezone.utc).isoformat()
            }
            
            result = self._client.table("agents").upsert(data, on_conflict="agent_name").execute()
            agent_id = result.data[0]["id"]
            self._logger.info(f"Registered agent {agent_name} with ID {agent_id}")
            return agent_id
            
        except Exception as e:
            self._logger.error(f"Failed to register agent {agent_name}: {e}")
            raise
    
    async def update_agent_heartbeat(self, agent_name: str) -> bool:
        """Update agent heartbeat timestamp"""
        try:
            result = self._client.table("agents").update({
                "last_heartbeat": datetime.now(timezone.utc).isoformat(),
                "status": "active"
            }).eq("agent_name", agent_name).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            self._logger.error(f"Failed to update heartbeat for {agent_name}: {e}")
            return False
    
    async def get_agent_status(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get agent status and configuration"""
        try:
            result = self._client.table("agents").select("*").eq("agent_name", agent_name).execute()
            return result.data[0] if result.data else None
            
        except Exception as e:
            self._logger.error(f"Failed to get agent status for {agent_name}: {e}")
            return None
    
    # ============================================================================
    # Message Management
    # ============================================================================
    
    async def log_message(self, message: BaseMessage, target_agent: str = None, 
                         topic_id: str = None) -> str:
        """Log a message to the database"""
        try:
            message_data = {
                "message_id": message.message_id,
                "message_type": message.__class__.__name__,
                "source_agent": message.source_agent,
                "target_agent": target_agent,
                "topic_id": topic_id,
                "payload": asdict(message),
                "status": "sent"
            }
            
            result = self._client.table("messages").insert(message_data).execute()
            db_id = result.data[0]["id"]
            self._logger.debug(f"Logged message {message.message_id} with DB ID {db_id}")
            return db_id
            
        except Exception as e:
            self._logger.error(f"Failed to log message {message.message_id}: {e}")
            raise
    
    async def mark_message_processed(self, message_id: str) -> bool:
        """Mark a message as processed"""
        try:
            result = self._client.table("messages").update({
                "status": "processed",
                "processed_at": datetime.now(timezone.utc).isoformat()
            }).eq("message_id", message_id).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            self._logger.error(f"Failed to mark message {message_id} as processed: {e}")
            return False
    
    async def get_message_history(self, agent_name: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get message history for an agent"""
        try:
            result = self._client.table("messages").select("*").or_(
                f"source_agent.eq.{agent_name},target_agent.eq.{agent_name}"
            ).order("created_at", desc=True).limit(limit).execute()
            
            return result.data
            
        except Exception as e:
            self._logger.error(f"Failed to get message history for {agent_name}: {e}")
            return []
    
    # ============================================================================
    # Intelligence Collection Data
    # ============================================================================
    
    async def store_web_intelligence(self, platform: str, content: str, 
                                   sentiment_score: float = None, 
                                   engagement_metrics: Dict[str, Any] = None,
                                   keywords: List[str] = None,
                                   confidence_score: float = None,
                                   source_url: str = None,
                                   author: str = None) -> str:
        """Store web intelligence data"""
        try:
            data = {
                "platform": platform,
                "content": content,
                "sentiment_score": sentiment_score,
                "engagement_metrics": engagement_metrics or {},
                "keywords": keywords or [],
                "confidence_score": confidence_score,
                "source_url": source_url,
                "author": author
            }
            
            result = self._client.table("web_intelligence").insert(data).execute()
            return result.data[0]["id"]
            
        except Exception as e:
            self._logger.error(f"Failed to store web intelligence: {e}")
            raise
    
    async def store_whale_activity(self, transaction_hash: str, from_address: str,
                                 to_address: str, token_symbol: str, amount: float,
                                 usd_value: float = None, chain: str = "ethereum",
                                 block_number: int = None, 
                                 transaction_type: str = None) -> str:
        """Store whale activity data"""
        try:
            data = {
                "transaction_hash": transaction_hash,
                "from_address": from_address,
                "to_address": to_address,
                "token_symbol": token_symbol,
                "amount": str(amount),  # Store as string to preserve precision
                "usd_value": usd_value,
                "chain": chain,
                "block_number": block_number,
                "transaction_type": transaction_type
            }
            
            result = self._client.table("whale_activity").insert(data).execute()
            return result.data[0]["id"]
            
        except Exception as e:
            self._logger.error(f"Failed to store whale activity: {e}")
            raise
    
    async def store_tvl_data(self, protocol: str, chain: str, tvl_usd: float,
                           tvl_change_24h: float = None, inflow_24h: float = None,
                           outflow_24h: float = None, 
                           token_breakdown: Dict[str, Any] = None) -> str:
        """Store TVL data"""
        try:
            data = {
                "protocol": protocol,
                "chain": chain,
                "tvl_usd": tvl_usd,
                "tvl_change_24h": tvl_change_24h,
                "inflow_24h": inflow_24h,
                "outflow_24h": outflow_24h,
                "token_breakdown": token_breakdown or {}
            }
            
            result = self._client.table("tvl_data").insert(data).execute()
            return result.data[0]["id"]
            
        except Exception as e:
            self._logger.error(f"Failed to store TVL data: {e}")
            raise
    
    async def store_meme_trend(self, token_symbol: str, chain: str,
                             trend_score: float = None, social_mentions: int = 0,
                             price_change_24h: float = None, volume_24h: float = None,
                             market_cap: float = None, token_address: str = None,
                             viral_content: Dict[str, Any] = None) -> str:
        """Store meme trend data"""
        try:
            data = {
                "token_symbol": token_symbol,
                "token_address": token_address,
                "chain": chain,
                "trend_score": trend_score,
                "social_mentions": social_mentions,
                "price_change_24h": price_change_24h,
                "volume_24h": volume_24h,
                "market_cap": market_cap,
                "viral_content": viral_content or {}
            }
            
            result = self._client.table("meme_trends").insert(data).execute()
            return result.data[0]["id"]
            
        except Exception as e:
            self._logger.error(f"Failed to store meme trend: {e}")
            raise
    
    # ============================================================================
    # Market Analysis Data
    # ============================================================================
    
    async def store_trend_signal(self, signal_type: str, symbol: str, timeframe: str,
                               signal_strength: float, direction: str,
                               confidence_score: float = None,
                               supporting_data: Dict[str, Any] = None,
                               expires_at: datetime = None) -> str:
        """Store trend signal"""
        try:
            data = {
                "signal_type": signal_type,
                "symbol": symbol,
                "timeframe": timeframe,
                "signal_strength": signal_strength,
                "direction": direction,
                "confidence_score": confidence_score,
                "supporting_data": supporting_data or {},
                "expires_at": expires_at.isoformat() if expires_at else None
            }
            
            result = self._client.table("trend_signals").insert(data).execute()
            return result.data[0]["id"]
            
        except Exception as e:
            self._logger.error(f"Failed to store trend signal: {e}")
            raise
    
    async def store_contract_analysis(self, contract_address: str, chain: str,
                                    token_symbol: str = None, security_score: float = None,
                                    audit_status: str = None, risk_factors: List[str] = None,
                                    tokenomics: Dict[str, Any] = None,
                                    liquidity_analysis: Dict[str, Any] = None,
                                    holder_analysis: Dict[str, Any] = None) -> str:
        """Store contract analysis"""
        try:
            data = {
                "contract_address": contract_address,
                "chain": chain,
                "token_symbol": token_symbol,
                "security_score": security_score,
                "audit_status": audit_status,
                "risk_factors": risk_factors or [],
                "tokenomics": tokenomics or {},
                "liquidity_analysis": liquidity_analysis or {},
                "holder_analysis": holder_analysis or {}
            }
            
            result = self._client.table("contract_analysis").insert(data).execute()
            return result.data[0]["id"]
            
        except Exception as e:
            self._logger.error(f"Failed to store contract analysis: {e}")
            raise

    # ============================================================================
    # Strategy Generation & Backtesting Data
    # ============================================================================

    async def store_strategy(self, strategy_name: str, strategy_type: str,
                           description: str = None, parameters: Dict[str, Any] = None,
                           code: str = None, status: str = "draft",
                           created_by: str = "StrategyGenerationAgent") -> str:
        """Store a trading strategy"""
        try:
            data = {
                "strategy_name": strategy_name,
                "strategy_type": strategy_type,
                "description": description,
                "parameters": parameters or {},
                "code": code,
                "status": status,
                "created_by": created_by
            }

            result = self._client.table("strategies").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store strategy: {e}")
            raise

    async def update_strategy_status(self, strategy_id: str, status: str) -> bool:
        """Update strategy status"""
        try:
            result = self._client.table("strategies").update({
                "status": status
            }).eq("id", strategy_id).execute()

            return len(result.data) > 0

        except Exception as e:
            self._logger.error(f"Failed to update strategy status: {e}")
            return False

    async def get_strategy(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get strategy by ID"""
        try:
            result = self._client.table("strategies").select("*").eq("id", strategy_id).execute()
            return result.data[0] if result.data else None

        except Exception as e:
            self._logger.error(f"Failed to get strategy: {e}")
            return None

    async def get_strategies_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Get strategies by status"""
        try:
            result = self._client.table("strategies").select("*").eq("status", status).execute()
            return result.data

        except Exception as e:
            self._logger.error(f"Failed to get strategies by status: {e}")
            return []

    async def store_backtest_result(self, strategy_id: str,
                                  backtest_period_start: datetime,
                                  backtest_period_end: datetime,
                                  total_return: float = None,
                                  sharpe_ratio: float = None,
                                  max_drawdown: float = None,
                                  win_rate: float = None,
                                  total_trades: int = None,
                                  performance_metrics: Dict[str, Any] = None,
                                  trade_history: Dict[str, Any] = None) -> str:
        """Store backtest results"""
        try:
            data = {
                "strategy_id": strategy_id,
                "backtest_period_start": backtest_period_start.isoformat(),
                "backtest_period_end": backtest_period_end.isoformat(),
                "total_return": total_return,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "win_rate": win_rate,
                "total_trades": total_trades,
                "performance_metrics": performance_metrics or {},
                "trade_history": trade_history or {}
            }

            result = self._client.table("backtest_results").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store backtest result: {e}")
            raise

    # ============================================================================
    # Execution & Risk Management Data
    # ============================================================================

    async def create_strategy_deployment(self, strategy_id: str, deployment_name: str,
                                       allocated_capital: float = None,
                                       risk_parameters: Dict[str, Any] = None) -> str:
        """Create a new strategy deployment"""
        try:
            data = {
                "strategy_id": strategy_id,
                "deployment_name": deployment_name,
                "status": "inactive",
                "allocated_capital": allocated_capital,
                "current_positions": {},
                "risk_parameters": risk_parameters or {}
            }

            result = self._client.table("strategy_deployments").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to create strategy deployment: {e}")
            raise

    async def update_deployment_status(self, deployment_id: str, status: str) -> bool:
        """Update deployment status"""
        try:
            update_data = {"status": status}
            if status == "active":
                update_data["deployed_at"] = datetime.now(timezone.utc).isoformat()
            elif status == "stopped":
                update_data["stopped_at"] = datetime.now(timezone.utc).isoformat()

            result = self._client.table("strategy_deployments").update(update_data).eq("id", deployment_id).execute()
            return len(result.data) > 0

        except Exception as e:
            self._logger.error(f"Failed to update deployment status: {e}")
            return False

    async def store_trade_execution(self, deployment_id: str, symbol: str, side: str,
                                  quantity: float, price: float = None,
                                  order_type: str = "market", exchange: str = None,
                                  order_id: str = None) -> str:
        """Store trade execution"""
        try:
            data = {
                "deployment_id": deployment_id,
                "symbol": symbol,
                "side": side,
                "quantity": str(quantity),  # Store as string for precision
                "price": str(price) if price else None,
                "order_type": order_type,
                "status": "pending",
                "exchange": exchange,
                "order_id": order_id,
                "filled_quantity": "0",
                "fees": "0"
            }

            result = self._client.table("trade_executions").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store trade execution: {e}")
            raise

    async def update_trade_execution(self, execution_id: str, status: str,
                                   filled_quantity: float = None, fees: float = None) -> bool:
        """Update trade execution status"""
        try:
            update_data = {"status": status}
            if filled_quantity is not None:
                update_data["filled_quantity"] = str(filled_quantity)
            if fees is not None:
                update_data["fees"] = str(fees)
            if status == "filled":
                update_data["executed_at"] = datetime.now(timezone.utc).isoformat()

            result = self._client.table("trade_executions").update(update_data).eq("id", execution_id).execute()
            return len(result.data) > 0

        except Exception as e:
            self._logger.error(f"Failed to update trade execution: {e}")
            return False

    async def store_performance_data(self, deployment_id: str, portfolio_value: float,
                                   pnl_realized: float = None, pnl_unrealized: float = None,
                                   drawdown: float = None, positions: Dict[str, Any] = None,
                                   metrics: Dict[str, Any] = None) -> str:
        """Store performance data"""
        try:
            data = {
                "deployment_id": deployment_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "portfolio_value": portfolio_value,
                "pnl_realized": pnl_realized,
                "pnl_unrealized": pnl_unrealized,
                "drawdown": drawdown,
                "positions": positions or {},
                "metrics": metrics or {}
            }

            result = self._client.table("performance_data").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store performance data: {e}")
            raise

    async def create_risk_alert(self, deployment_id: str, alert_type: str, severity: str,
                              message: str, current_value: float = None,
                              threshold_value: float = None) -> str:
        """Create a risk alert"""
        try:
            data = {
                "deployment_id": deployment_id,
                "alert_type": alert_type,
                "severity": severity,
                "message": message,
                "current_value": str(current_value) if current_value is not None else None,
                "threshold_value": str(threshold_value) if threshold_value is not None else None,
                "status": "active"
            }

            result = self._client.table("risk_alerts").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to create risk alert: {e}")
            raise

    async def acknowledge_risk_alert(self, alert_id: str) -> bool:
        """Acknowledge a risk alert"""
        try:
            result = self._client.table("risk_alerts").update({
                "acknowledged_at": datetime.now(timezone.utc).isoformat()
            }).eq("id", alert_id).execute()

            return len(result.data) > 0

        except Exception as e:
            self._logger.error(f"Failed to acknowledge risk alert: {e}")
            return False

    # ============================================================================
    # Control & Interaction Layer Data
    # ============================================================================

    async def store_optimization_result(self, strategy_id: str, optimization_algorithm: str,
                                      original_parameters: Dict[str, Any],
                                      optimized_parameters: Dict[str, Any],
                                      performance_improvement: float = None,
                                      confidence_score: float = None,
                                      optimization_details: Dict[str, Any] = None) -> str:
        """Store parameter optimization result"""
        try:
            data = {
                "strategy_id": strategy_id,
                "optimization_algorithm": optimization_algorithm,
                "original_parameters": original_parameters,
                "optimized_parameters": optimized_parameters,
                "performance_improvement": performance_improvement,
                "confidence_score": confidence_score,
                "optimization_details": optimization_details or {}
            }

            result = self._client.table("optimization_history").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store optimization result: {e}")
            raise

    async def store_notification(self, notification_type: str, recipient: str,
                               message: str, subject: str = None) -> str:
        """Store notification record"""
        try:
            data = {
                "notification_type": notification_type,
                "recipient": recipient,
                "subject": subject,
                "message": message,
                "status": "pending"
            }

            result = self._client.table("notifications").insert(data).execute()
            return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store notification: {e}")
            raise

    async def update_notification_status(self, notification_id: str, status: str,
                                       delivery_status: str = None) -> bool:
        """Update notification delivery status"""
        try:
            update_data = {"status": status}
            if delivery_status:
                update_data["delivery_status"] = delivery_status
            if status == "sent":
                update_data["sent_at"] = datetime.now(timezone.utc).isoformat()

            result = self._client.table("notifications").update(update_data).eq("id", notification_id).execute()
            return len(result.data) > 0

        except Exception as e:
            self._logger.error(f"Failed to update notification status: {e}")
            return False

    async def store_user_session(self, user_id: str, platform: str,
                               session_data: Dict[str, Any] = None,
                               preferences: Dict[str, Any] = None) -> str:
        """Store or update user session"""
        try:
            data = {
                "user_id": user_id,
                "platform": platform,
                "session_data": session_data or {},
                "preferences": preferences or {},
                "last_activity": datetime.now(timezone.utc).isoformat()
            }

            # Try to update existing session first
            existing = self._client.table("user_sessions").select("id").eq("user_id", user_id).eq("platform", platform).execute()

            if existing.data:
                result = self._client.table("user_sessions").update(data).eq("id", existing.data[0]["id"]).execute()
                return existing.data[0]["id"]
            else:
                result = self._client.table("user_sessions").insert(data).execute()
                return result.data[0]["id"]

        except Exception as e:
            self._logger.error(f"Failed to store user session: {e}")
            raise

    # ============================================================================
    # Query Methods
    # ============================================================================

    async def get_recent_intelligence(self, limit: int = 50) -> Dict[str, List[Dict[str, Any]]]:
        """Get recent intelligence data from all sources"""
        try:
            results = {}

            # Web intelligence
            web_intel = self._client.table("web_intelligence").select("*").order("created_at", desc=True).limit(limit).execute()
            results["web_intelligence"] = web_intel.data

            # Whale activity
            whale_data = self._client.table("whale_activity").select("*").order("created_at", desc=True).limit(limit).execute()
            results["whale_activity"] = whale_data.data

            # TVL data
            tvl_data = self._client.table("tvl_data").select("*").order("created_at", desc=True).limit(limit).execute()
            results["tvl_data"] = tvl_data.data

            # Meme trends
            meme_data = self._client.table("meme_trends").select("*").order("created_at", desc=True).limit(limit).execute()
            results["meme_trends"] = meme_data.data

            return results

        except Exception as e:
            self._logger.error(f"Failed to get recent intelligence: {e}")
            return {}

    async def get_active_deployments(self) -> List[Dict[str, Any]]:
        """Get all active strategy deployments"""
        try:
            result = self._client.table("strategy_deployments").select("*").eq("status", "active").execute()
            return result.data

        except Exception as e:
            self._logger.error(f"Failed to get active deployments: {e}")
            return []

    async def get_deployment_performance(self, deployment_id: str,
                                       hours: int = 24) -> List[Dict[str, Any]]:
        """Get performance data for a deployment"""
        try:
            from_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            result = self._client.table("performance_data").select("*").eq(
                "deployment_id", deployment_id
            ).gte("timestamp", from_time.isoformat()).order("timestamp", desc=True).execute()

            return result.data

        except Exception as e:
            self._logger.error(f"Failed to get deployment performance: {e}")
            return []
