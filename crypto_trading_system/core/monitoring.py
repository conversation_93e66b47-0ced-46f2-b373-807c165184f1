"""
Monitoring and Logging System for Multi-Agent Crypto Trading System
Provides comprehensive system monitoring, logging, and alerting
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .database_manager import DatabaseManager


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class SystemMetric:
    """System performance metric"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    tags: Dict[str, str] = None


@dataclass
class SystemAlert:
    """System alert"""
    level: AlertLevel
    title: str
    message: str
    source: str
    timestamp: datetime
    metadata: Dict[str, Any] = None


class SystemMonitor:
    """Comprehensive system monitoring"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.metrics_buffer: List[SystemMetric] = []
        self.alerts_buffer: List[SystemAlert] = []
        self.monitoring_active = False
        self.monitoring_task = None
        
        # Performance tracking
        self.start_time = time.time()
        self.operation_counts = {}
        self.error_counts = {}
        
    async def start_monitoring(self, interval: int = 60):
        """Start system monitoring"""
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop(interval))
        self.logger.info("System monitoring started")
    
    async def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        self.logger.info("System monitoring stopped")
    
    async def _monitoring_loop(self, interval: int):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                await self._collect_system_metrics()
                await self._check_system_health()
                await self._flush_metrics()
                await self._flush_alerts()
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(interval)
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # System uptime
            uptime = time.time() - self.start_time
            self.add_metric("system.uptime", uptime, "seconds")
            
            # Database metrics
            await self._collect_database_metrics()
            
            # Agent metrics
            await self._collect_agent_metrics()
            
            # Operation metrics
            self._collect_operation_metrics()
            
        except Exception as e:
            self.logger.error(f"Error collecting metrics: {e}")
    
    async def _collect_database_metrics(self):
        """Collect database performance metrics"""
        try:
            # Agent count
            agents_result = await self.db_manager._client.table("agents").select("count", count="exact").execute()
            self.add_metric("database.agents.count", agents_result.count, "count")
            
            # Message count (last hour)
            one_hour_ago = datetime.now(timezone.utc).replace(minute=0, second=0, microsecond=0)
            messages_result = await self.db_manager._client.table("messages").select("count", count="exact").gte("created_at", one_hour_ago.isoformat()).execute()
            self.add_metric("database.messages.hourly", messages_result.count, "count")
            
            # Active deployments
            deployments = await self.db_manager.get_active_deployments()
            self.add_metric("trading.deployments.active", len(deployments), "count")
            
        except Exception as e:
            self.logger.error(f"Error collecting database metrics: {e}")
    
    async def _collect_agent_metrics(self):
        """Collect agent performance metrics"""
        try:
            # Get agent heartbeats
            agents_result = await self.db_manager._client.table("agents").select("agent_name", "last_heartbeat", "status").execute()
            
            active_agents = 0
            inactive_agents = 0
            
            for agent in agents_result.data:
                if agent.get("status") == "active":
                    active_agents += 1
                else:
                    inactive_agents += 1
            
            self.add_metric("agents.active", active_agents, "count")
            self.add_metric("agents.inactive", inactive_agents, "count")
            
        except Exception as e:
            self.logger.error(f"Error collecting agent metrics: {e}")
    
    def _collect_operation_metrics(self):
        """Collect operation performance metrics"""
        for operation, count in self.operation_counts.items():
            self.add_metric(f"operations.{operation}", count, "count")
        
        for error_type, count in self.error_counts.items():
            self.add_metric(f"errors.{error_type}", count, "count")
    
    async def _check_system_health(self):
        """Check system health and generate alerts"""
        try:
            # Check database connectivity
            try:
                await self.db_manager._client.table("agents").select("count", count="exact").limit(1).execute()
            except Exception as e:
                self.add_alert(
                    AlertLevel.CRITICAL,
                    "Database Connection Failed",
                    f"Unable to connect to database: {e}",
                    "system_monitor"
                )
            
            # Check agent health
            await self._check_agent_health()
            
            # Check error rates
            self._check_error_rates()
            
        except Exception as e:
            self.logger.error(f"Error checking system health: {e}")
    
    async def _check_agent_health(self):
        """Check individual agent health"""
        try:
            agents_result = await self.db_manager._client.table("agents").select("*").execute()
            
            for agent in agents_result.data:
                # Check last heartbeat
                if agent.get("last_heartbeat"):
                    last_heartbeat = datetime.fromisoformat(agent["last_heartbeat"].replace("Z", "+00:00"))
                    time_since_heartbeat = datetime.now(timezone.utc) - last_heartbeat
                    
                    if time_since_heartbeat.total_seconds() > 300:  # 5 minutes
                        self.add_alert(
                            AlertLevel.WARNING,
                            f"Agent Heartbeat Missing",
                            f"Agent {agent['agent_name']} hasn't sent heartbeat for {time_since_heartbeat}",
                            "agent_monitor"
                        )
                
        except Exception as e:
            self.logger.error(f"Error checking agent health: {e}")
    
    def _check_error_rates(self):
        """Check system error rates"""
        total_operations = sum(self.operation_counts.values())
        total_errors = sum(self.error_counts.values())
        
        if total_operations > 0:
            error_rate = total_errors / total_operations
            
            if error_rate > 0.1:  # 10% error rate
                self.add_alert(
                    AlertLevel.ERROR,
                    "High Error Rate",
                    f"System error rate is {error_rate:.2%} ({total_errors}/{total_operations})",
                    "error_monitor"
                )
    
    def add_metric(self, name: str, value: float, unit: str, tags: Dict[str, str] = None):
        """Add a system metric"""
        metric = SystemMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=datetime.now(timezone.utc),
            tags=tags or {}
        )
        self.metrics_buffer.append(metric)
    
    def add_alert(self, level: AlertLevel, title: str, message: str, source: str, metadata: Dict[str, Any] = None):
        """Add a system alert"""
        alert = SystemAlert(
            level=level,
            title=title,
            message=message,
            source=source,
            timestamp=datetime.now(timezone.utc),
            metadata=metadata or {}
        )
        self.alerts_buffer.append(alert)
        
        # Log alert immediately
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }[level]
        
        self.logger.log(log_level, f"ALERT [{level.value.upper()}] {title}: {message}")
    
    async def _flush_metrics(self):
        """Flush metrics to database"""
        if not self.metrics_buffer:
            return
        
        try:
            # Store metrics in system_config table as time series data
            metrics_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metrics": [
                    {
                        "name": metric.name,
                        "value": metric.value,
                        "unit": metric.unit,
                        "timestamp": metric.timestamp.isoformat(),
                        "tags": metric.tags
                    }
                    for metric in self.metrics_buffer
                ]
            }
            
            await self.db_manager._client.table("system_config").upsert({
                "config_key": f"metrics_{int(time.time())}",
                "config_value": metrics_data
            }).execute()
            
            self.metrics_buffer.clear()
            
        except Exception as e:
            self.logger.error(f"Error flushing metrics: {e}")
    
    async def _flush_alerts(self):
        """Flush alerts to database"""
        if not self.alerts_buffer:
            return
        
        try:
            for alert in self.alerts_buffer:
                await self.db_manager._client.table("notifications").insert({
                    "notification_type": "system_alert",
                    "title": alert.title,
                    "message": alert.message,
                    "priority": alert.level.value,
                    "metadata": {
                        "source": alert.source,
                        "alert_metadata": alert.metadata
                    },
                    "status": "pending"
                }).execute()
            
            self.alerts_buffer.clear()
            
        except Exception as e:
            self.logger.error(f"Error flushing alerts: {e}")
    
    def record_operation(self, operation_name: str):
        """Record an operation for metrics"""
        self.operation_counts[operation_name] = self.operation_counts.get(operation_name, 0) + 1
    
    def record_error(self, error_type: str):
        """Record an error for metrics"""
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "monitoring_active": self.monitoring_active,
            "uptime_seconds": time.time() - self.start_time,
            "total_operations": sum(self.operation_counts.values()),
            "total_errors": sum(self.error_counts.values()),
            "pending_metrics": len(self.metrics_buffer),
            "pending_alerts": len(self.alerts_buffer),
            "operation_counts": dict(self.operation_counts),
            "error_counts": dict(self.error_counts)
        }


class PerformanceLogger:
    """Performance logging utility"""
    
    def __init__(self, monitor: SystemMonitor):
        self.monitor = monitor
        self.logger = logging.getLogger(__name__)
    
    def log_operation_start(self, operation: str, metadata: Dict[str, Any] = None):
        """Log operation start"""
        self.logger.debug(f"Starting operation: {operation}", extra=metadata or {})
        return time.time()
    
    def log_operation_end(self, operation: str, start_time: float, success: bool = True, metadata: Dict[str, Any] = None):
        """Log operation completion"""
        duration = time.time() - start_time
        
        self.monitor.record_operation(operation)
        self.monitor.add_metric(f"operations.{operation}.duration", duration, "seconds")
        
        if success:
            self.logger.debug(f"Completed operation: {operation} in {duration:.3f}s", extra=metadata or {})
        else:
            self.monitor.record_error(operation)
            self.logger.error(f"Failed operation: {operation} after {duration:.3f}s", extra=metadata or {})
    
    async def log_async_operation(self, operation: str, coro, metadata: Dict[str, Any] = None):
        """Log async operation with automatic timing"""
        start_time = self.log_operation_start(operation, metadata)
        
        try:
            result = await coro
            self.log_operation_end(operation, start_time, True, metadata)
            return result
        except Exception as e:
            self.log_operation_end(operation, start_time, False, {**(metadata or {}), "error": str(e)})
            raise
