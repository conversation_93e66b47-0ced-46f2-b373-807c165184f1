# Multi-Agent Cryptocurrency Trading System

A comprehensive AI-powered cryptocurrency trading system built with Microsoft AutoGen, featuring intelligent data collection, market analysis, strategy generation, and automated execution.

## System Architecture

This system implements a 5-layer architecture using AutoGen's latest multi-agent patterns:

### 1. Intelligence Collection Layer (情报采集层)
- **Web Intelligence Agent**: Monitors Twitter, news, Reddit for market trends and airdrop rumors
- **Whale Tracking Agent**: Tracks large on-chain transfers and whale MEME purchases
- **TVL/Chain Inflow Agent**: Monitors multi-chain TVL changes and identifies hot chains
- **Meme Trend Agent**: Scrapes social media for MEME token frequency and trending data

### 2. Market Analysis Layer (市场解析层)
- **Trend Aggregation Agent**: Integrates signals from intelligence agents
- **Contract Analysis Agent**: Analyzes DeFi protocol code for vulnerabilities and patterns

### 3. Strategy Generation & Backtesting Layer (策略生成 & 回测)
- **Strategy Generation Agent**: Designs trading strategies based on trend signals
- **Strategy Coding Agent**: Converts strategy logic into executable code
- **Strategy Backtesting Agent**: Tests strategies against historical data

### 4. Execution & Risk Management Layer (执行 & 风控层)
- **Strategy Deployment Agent**: Deploys strategies to live trading accounts
- **Real-time Monitoring Agent**: Monitors strategy performance and market volatility
- **Performance Analysis Agent**: Analyzes returns, Sharpe ratios, drawdowns
- **Strategy Memory Agent**: Stores strategy performance and creates prompt databases

### 5. Control & Interaction Layer (控制与交互层)
- **Parameter Optimization Agent**: Auto-tunes parameters based on performance
- **Telegram Bot Agent**: Mobile control interface for real-time updates
- **Slack Notification System**: Pushes strategy alerts to Slack channels

## Key Features

- **Multi-Agent Orchestration**: Uses AutoGen's latest patterns for agent communication
- **Real-time Data Processing**: Integrates with multiple data sources and APIs
- **Automated Strategy Generation**: AI-powered quantitative strategy creation
- **Risk Management**: Built-in safety mechanisms and monitoring
- **Multi-Modal Support**: Supports various AI models (Claude 3.5, Gemini 1.5 Pro)
- **Scalable Architecture**: Distributed agent runtime support

## Technology Stack

- **Framework**: Microsoft AutoGen (latest version)
- **Database**: Supabase for real-time data storage and triggers
- **AI Models**: Claude 3.5 Sonnet, Gemini 1.5 Pro, GPT-4
- **Trading APIs**: Binance, other major exchanges
- **Data Sources**: Twitter API, Reddit API, on-chain data providers
- **Notifications**: Telegram Bot API, Slack API
- **Backtesting**: Custom backtesting engine with historical data

## Getting Started

1. **Installation**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configuration**
   - Set up API keys in `.env` file
   - Configure Supabase connection
   - Set up exchange API credentials

3. **Run the System**
   ```bash
   python main.py
   ```

## Project Structure

```
crypto_trading_system/
├── agents/                 # Agent implementations
│   ├── intelligence/      # Intelligence collection agents
│   ├── analysis/          # Market analysis agents
│   ├── strategy/          # Strategy generation agents
│   ├── execution/         # Execution and risk management
│   └── control/           # Control and interaction agents
├── core/                  # Core system components
│   ├── runtime.py         # Agent runtime configuration
│   ├── messages.py        # Message protocol definitions
│   └── tools.py           # Shared tools and utilities
├── data/                  # Data management
│   ├── collectors/        # Data collection modules
│   └── storage/           # Database schemas and models
├── strategies/            # Generated trading strategies
├── tests/                 # Test suite
└── config/                # Configuration files
```

## Safety and Risk Management

- **Paper Trading Mode**: Test strategies without real money
- **Position Limits**: Automatic position sizing and risk limits
- **Stop Loss Mechanisms**: Built-in stop loss and take profit
- **Performance Monitoring**: Real-time performance tracking
- **Alert Systems**: Immediate notifications for critical events

## Contributing

Please read our contributing guidelines and ensure all tests pass before submitting PRs.

## License

MIT License - see LICENSE file for details.
