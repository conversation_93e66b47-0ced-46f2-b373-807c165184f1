"""
Real-Time Monitoring Agent

This agent monitors live trading strategies, tracks performance,
and triggers alerts when risk limits are breached.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque, defaultdict
import numpy as np

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    TradeSignal,
    StrategyDeployment,
    PerformanceAlert,
    SystemCommand,
    ConfigUpdate
)


class AlertLevel(str):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@default_subscription
class RealTimeMonitoringAgent(RoutedAgent):
    """
    Agent responsible for real-time monitoring of live trading strategies.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Real-Time Monitoring Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._monitoring_interval = 30  # seconds
        
        # Monitoring data structures
        self._active_strategies = {}  # strategy_name -> monitoring data
        self._performance_history = defaultdict(lambda: deque(maxlen=1000))
        self._alert_history = deque(maxlen=500)
        
        # Risk thresholds
        self._risk_thresholds = {
            "max_drawdown": -0.1,           # 10% max drawdown
            "daily_loss_limit": -0.02,      # 2% daily loss limit
            "position_size_limit": 0.05,    # 5% max position size
            "total_exposure_limit": 0.3,    # 30% total exposure
            "sharpe_degradation": 0.5,      # 50% Sharpe ratio degradation
            "consecutive_losses": 5,        # Max consecutive losing trades
            "volatility_spike": 2.0         # 2x normal volatility
        }
        
        # Performance tracking windows
        self._tracking_windows = {
            "1m": timedelta(minutes=1),
            "5m": timedelta(minutes=5),
            "15m": timedelta(minutes=15),
            "1h": timedelta(hours=1),
            "4h": timedelta(hours=4),
            "1d": timedelta(days=1),
            "7d": timedelta(days=7)
        }
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Real-Time Monitoring Agent responsible for monitoring live trading strategies and identifying potential issues.

Your responsibilities:
1. Monitor strategy performance in real-time
2. Detect anomalies and performance degradation
3. Generate alerts when risk thresholds are breached
4. Analyze market conditions affecting strategy performance
5. Recommend immediate actions for risk mitigation

When analyzing performance issues, consider:
- Statistical significance of performance changes
- Market regime changes and their impact
- Strategy-specific risk factors
- Correlation with market events
- Historical performance patterns
- Risk-adjusted metrics trends

Always prioritize capital preservation and provide actionable recommendations."""
        )
        
        # Start monitoring loops
        asyncio.create_task(self._start_monitoring_loop())
        asyncio.create_task(self._start_alert_processor())
    
    async def _start_monitoring_loop(self):
        """Main monitoring loop"""
        while self._is_active:
            try:
                await self._monitor_all_strategies()
                await asyncio.sleep(self._monitoring_interval)
            except Exception as e:
                self._logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _start_alert_processor(self):
        """Process and escalate alerts"""
        while self._is_active:
            try:
                await self._process_pending_alerts()
                await asyncio.sleep(10)  # Check alerts every 10 seconds
            except Exception as e:
                self._logger.error(f"Error in alert processor: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_all_strategies(self):
        """Monitor all active strategies"""
        for strategy_name, strategy_data in list(self._active_strategies.items()):
            try:
                await self._monitor_strategy(strategy_name, strategy_data)
            except Exception as e:
                self._logger.error(f"Error monitoring strategy {strategy_name}: {e}")
    
    async def _monitor_strategy(self, strategy_name: str, strategy_data: Dict[str, Any]):
        """Monitor a single strategy"""
        try:
            # Get current performance data
            current_performance = await self._get_current_performance(strategy_name)
            
            if not current_performance:
                return
            
            # Update performance history
            self._performance_history[strategy_name].append({
                "timestamp": datetime.now(),
                "performance": current_performance
            })
            
            # Check risk thresholds
            alerts = await self._check_risk_thresholds(strategy_name, current_performance)
            
            # Process any alerts
            for alert in alerts:
                await self._generate_alert(alert)
            
            # Update strategy data
            strategy_data["last_update"] = datetime.now()
            strategy_data["current_performance"] = current_performance
            
        except Exception as e:
            self._logger.error(f"Error monitoring strategy {strategy_name}: {e}")
    
    async def _get_current_performance(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """Get current performance metrics for a strategy"""
        try:
            # In production, this would fetch from exchange APIs or database
            # For now, simulate performance data
            
            # Get historical performance
            history = list(self._performance_history[strategy_name])
            
            if not history:
                # Initial performance data
                return {
                    "pnl": 0.0,
                    "pnl_pct": 0.0,
                    "drawdown": 0.0,
                    "positions": {},
                    "total_exposure": 0.0,
                    "daily_pnl": 0.0,
                    "trades_today": 0,
                    "win_rate_24h": 0.5,
                    "sharpe_7d": 1.0,
                    "volatility": 0.2
                }
            
            # Simulate performance evolution
            last_performance = history[-1]["performance"]
            
            # Add some random walk to simulate real performance
            pnl_change = np.random.normal(0.0001, 0.01)  # Small random changes
            new_pnl_pct = last_performance["pnl_pct"] + pnl_change
            
            return {
                "pnl": new_pnl_pct * 10000,  # Assuming $10k initial capital
                "pnl_pct": new_pnl_pct,
                "drawdown": min(0, new_pnl_pct - max(h["performance"]["pnl_pct"] for h in history)),
                "positions": last_performance.get("positions", {}),
                "total_exposure": np.random.uniform(0.1, 0.25),
                "daily_pnl": np.random.normal(0.001, 0.02),
                "trades_today": last_performance.get("trades_today", 0) + np.random.poisson(0.1),
                "win_rate_24h": np.random.beta(6, 4),  # Biased towards 60% win rate
                "sharpe_7d": max(0, np.random.normal(1.2, 0.3)),
                "volatility": np.random.lognormal(-1.6, 0.3)  # Log-normal volatility
            }
            
        except Exception as e:
            self._logger.error(f"Error getting performance for {strategy_name}: {e}")
            return None
    
    async def _check_risk_thresholds(self, strategy_name: str, performance: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check if any risk thresholds are breached"""
        alerts = []
        
        try:
            # Check drawdown
            if performance["drawdown"] < self._risk_thresholds["max_drawdown"]:
                alerts.append({
                    "strategy": strategy_name,
                    "type": "max_drawdown_breach",
                    "level": AlertLevel.CRITICAL,
                    "value": performance["drawdown"],
                    "threshold": self._risk_thresholds["max_drawdown"],
                    "message": f"Max drawdown breached: {performance['drawdown']:.1%}"
                })
            
            # Check daily loss limit
            if performance["daily_pnl"] < self._risk_thresholds["daily_loss_limit"]:
                alerts.append({
                    "strategy": strategy_name,
                    "type": "daily_loss_limit",
                    "level": AlertLevel.WARNING,
                    "value": performance["daily_pnl"],
                    "threshold": self._risk_thresholds["daily_loss_limit"],
                    "message": f"Daily loss limit approached: {performance['daily_pnl']:.1%}"
                })
            
            # Check total exposure
            if performance["total_exposure"] > self._risk_thresholds["total_exposure_limit"]:
                alerts.append({
                    "strategy": strategy_name,
                    "type": "exposure_limit",
                    "level": AlertLevel.WARNING,
                    "value": performance["total_exposure"],
                    "threshold": self._risk_thresholds["total_exposure_limit"],
                    "message": f"Total exposure high: {performance['total_exposure']:.1%}"
                })
            
            # Check Sharpe ratio degradation
            baseline_sharpe = self._active_strategies[strategy_name].get("baseline_sharpe", 1.5)
            current_sharpe = performance["sharpe_7d"]
            
            if current_sharpe < baseline_sharpe * self._risk_thresholds["sharpe_degradation"]:
                alerts.append({
                    "strategy": strategy_name,
                    "type": "performance_degradation",
                    "level": AlertLevel.WARNING,
                    "value": current_sharpe,
                    "threshold": baseline_sharpe * self._risk_thresholds["sharpe_degradation"],
                    "message": f"Sharpe ratio degraded: {current_sharpe:.2f} vs baseline {baseline_sharpe:.2f}"
                })
            
            # Check volatility spike
            baseline_vol = self._active_strategies[strategy_name].get("baseline_volatility", 0.2)
            current_vol = performance["volatility"]
            
            if current_vol > baseline_vol * self._risk_thresholds["volatility_spike"]:
                alerts.append({
                    "strategy": strategy_name,
                    "type": "volatility_spike",
                    "level": AlertLevel.INFO,
                    "value": current_vol,
                    "threshold": baseline_vol * self._risk_thresholds["volatility_spike"],
                    "message": f"Volatility spike detected: {current_vol:.1%} vs baseline {baseline_vol:.1%}"
                })
            
        except Exception as e:
            self._logger.error(f"Error checking risk thresholds: {e}")
        
        return alerts
    
    async def _generate_alert(self, alert_data: Dict[str, Any]):
        """Generate and publish an alert"""
        try:
            alert = PerformanceAlert(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"alert_{alert_data['strategy']}_{datetime.now().timestamp()}",
                strategy_name=alert_data["strategy"],
                alert_type=alert_data["type"],
                alert_level=alert_data["level"],
                message=alert_data["message"],
                current_value=alert_data["value"],
                threshold_value=alert_data["threshold"],
                recommended_actions=await self._get_recommended_actions(alert_data)
            )
            
            # Add to alert history
            self._alert_history.append(alert)
            
            # Publish alert
            await self._publish_alert(alert)
            
            self._logger.warning(f"Generated {alert.alert_level} alert for {alert.strategy_name}: {alert.message}")
            
        except Exception as e:
            self._logger.error(f"Error generating alert: {e}")
    
    async def _get_recommended_actions(self, alert_data: Dict[str, Any]) -> List[str]:
        """Get recommended actions for an alert"""
        alert_type = alert_data["type"]
        
        action_map = {
            "max_drawdown_breach": [
                "Immediately reduce position sizes",
                "Consider pausing strategy",
                "Review recent trades for issues",
                "Check market conditions"
            ],
            "daily_loss_limit": [
                "Monitor closely",
                "Reduce position sizes if trend continues",
                "Review intraday performance"
            ],
            "exposure_limit": [
                "Reduce new position sizes",
                "Close some existing positions",
                "Review position correlation"
            ],
            "performance_degradation": [
                "Analyze recent performance",
                "Check for market regime changes",
                "Consider parameter adjustments"
            ],
            "volatility_spike": [
                "Monitor market conditions",
                "Consider reducing position sizes",
                "Check for news events"
            ]
        }
        
        return action_map.get(alert_type, ["Monitor situation closely"])
    
    async def _publish_alert(self, alert: PerformanceAlert):
        """Publish alert to relevant agents"""
        try:
            # Send to deployment agent for potential action
            await self.publish_message(
                alert,
                topic_id=TopicId("StrategyDeploymentAgent", source=self.id.key)
            )
            
            # Send to notification agents
            await self.publish_message(
                alert,
                topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
            )
            
            await self.publish_message(
                alert,
                topic_id=TopicId("TelegramBotAgent", source=self.id.key)
            )
            
        except Exception as e:
            self._logger.error(f"Error publishing alert: {e}")
    
    async def _process_pending_alerts(self):
        """Process and escalate pending alerts"""
        try:
            # Check for critical alerts that need immediate attention
            recent_alerts = [
                alert for alert in self._alert_history
                if (datetime.now() - alert.timestamp) < timedelta(minutes=5)
                and alert.alert_level == AlertLevel.CRITICAL
            ]
            
            if len(recent_alerts) > 2:  # Multiple critical alerts
                await self._escalate_emergency(recent_alerts)
            
        except Exception as e:
            self._logger.error(f"Error processing pending alerts: {e}")
    
    async def _escalate_emergency(self, alerts: List[PerformanceAlert]):
        """Escalate to emergency status"""
        try:
            emergency_alert = PerformanceAlert(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"emergency_{datetime.now().timestamp()}",
                strategy_name="MULTIPLE",
                alert_type="emergency_escalation",
                alert_level=AlertLevel.EMERGENCY,
                message=f"Multiple critical alerts detected: {len(alerts)} alerts",
                current_value=len(alerts),
                threshold_value=2,
                recommended_actions=[
                    "IMMEDIATE ATTENTION REQUIRED",
                    "Consider pausing all affected strategies",
                    "Review system-wide risk exposure",
                    "Contact risk management team"
                ]
            )
            
            await self._publish_alert(emergency_alert)
            
            self._logger.critical(f"EMERGENCY: Multiple critical alerts - {len(alerts)} alerts")
            
        except Exception as e:
            self._logger.error(f"Error escalating emergency: {e}")
    
    @message_handler
    async def handle_trading_signal(self, message: TradeSignal, ctx: MessageContext) -> None:
        """Handle trading signals to track strategy activity"""
        try:
            strategy_name = message.strategy_name
            
            if message.signal_type == "start_paper_trading":
                # Initialize monitoring for new strategy
                self._active_strategies[strategy_name] = {
                    "status": "paper_trading",
                    "start_time": datetime.now(),
                    "baseline_sharpe": 1.5,
                    "baseline_volatility": 0.2,
                    "deployment_id": message.parameters.get("deployment_id")
                }
                self._logger.info(f"Started monitoring paper trading for {strategy_name}")
                
            elif message.signal_type == "start_live_trading":
                # Update to live trading status
                if strategy_name in self._active_strategies:
                    self._active_strategies[strategy_name]["status"] = "live_trading"
                    self._active_strategies[strategy_name]["live_start"] = datetime.now()
                    self._logger.info(f"Updated monitoring for live trading: {strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error handling trading signal: {e}")
    
    @message_handler
    async def handle_strategy_deployment(self, message: StrategyDeployment, ctx: MessageContext) -> None:
        """Handle strategy deployment updates"""
        try:
            strategy_name = message.strategy_name
            
            # Initialize or update monitoring
            self._active_strategies[strategy_name] = {
                "status": message.deployment_status,
                "deployment_id": message.deployment_id,
                "allocated_capital": message.allocated_capital,
                "risk_limits": message.risk_limits,
                "performance_targets": message.performance_targets,
                "start_time": datetime.now(),
                "baseline_sharpe": message.performance_targets.get("min_sharpe", 1.0),
                "baseline_volatility": 0.2
            }
            
            self._logger.info(f"Updated monitoring for deployment: {strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error handling strategy deployment: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Real-time monitoring started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Real-time monitoring stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_alerts":
            self._alert_history.clear()
            self._logger.info("Cleared alert history")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "monitoring":
            updates = message.updates
            
            if "risk_thresholds" in updates:
                self._risk_thresholds.update(updates["risk_thresholds"])
                self._logger.info("Updated risk thresholds")
            
            if "monitoring_interval" in updates:
                self._monitoring_interval = updates["monitoring_interval"]
                self._logger.info(f"Updated monitoring interval to {self._monitoring_interval}s")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "RealTimeMonitoringAgent",
            "active": self._is_active,
            "monitoring_interval": self._monitoring_interval,
            "active_strategies": len(self._active_strategies),
            "recent_alerts": len([
                alert for alert in self._alert_history
                if (datetime.now() - alert.timestamp) < timedelta(hours=1)
            ]),
            "risk_thresholds": self._risk_thresholds,
            "strategy_statuses": {
                name: data.get("status", "unknown")
                for name, data in self._active_strategies.items()
            },
            "last_update": datetime.now().isoformat()
        }
