"""
Strategy Deployment Agent

This agent manages the deployment of backtested strategies to live trading,
including paper trading, risk validation, and gradual rollout.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque
from enum import Enum

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    BacktestResult,
    StrategyCode,
    StrategyDeployment,
    TradeSignal,
    SystemCommand,
    ConfigUpdate
)


class DeploymentStatus(Enum):
    PENDING = "pending"
    PAPER_TRADING = "paper_trading"
    LIVE_SMALL = "live_small"
    LIVE_FULL = "live_full"
    PAUSED = "paused"
    STOPPED = "stopped"


@default_subscription
class StrategyDeploymentAgent(RoutedAgent):
    """
    Agent responsible for deploying strategies to live trading environments.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Strategy Deployment Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._deployment_queue = asyncio.Queue()
        
        # Deployment criteria
        self._deployment_criteria = {
            "min_sharpe_ratio": 1.2,
            "max_drawdown": -0.2,
            "min_total_return": 0.1,
            "min_backtest_period": 90,  # days
            "min_trades": 20
        }
        
        # Active deployments tracking
        self._active_deployments = {}
        self._deployment_history = deque(maxlen=100)
        
        # Risk limits for live trading
        self._risk_limits = {
            "max_position_size": 0.05,      # 5% of portfolio per position
            "max_total_exposure": 0.3,      # 30% total exposure
            "max_daily_loss": 0.02,         # 2% daily loss limit
            "max_drawdown_live": 0.1,       # 10% live drawdown limit
            "paper_trading_days": 7         # Days of paper trading before live
        }
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Strategy Deployment Agent responsible for safely deploying trading strategies to live markets.

Your responsibilities:
1. Evaluate backtest results against deployment criteria
2. Manage gradual strategy rollout from paper to live trading
3. Monitor deployed strategies for performance degradation
4. Implement risk controls and position limits
5. Make deployment decisions based on risk-adjusted performance

When evaluating strategies for deployment, consider:
- Statistical significance of backtest results
- Robustness across different market conditions
- Risk-adjusted returns and drawdown characteristics
- Strategy complexity and execution requirements
- Market capacity and liquidity constraints
- Correlation with existing deployed strategies

Always prioritize capital preservation and risk management over potential returns."""
        )
        
        # Start background processing
        asyncio.create_task(self._start_deployment_worker())
        asyncio.create_task(self._start_monitoring_loop())
    
    async def _start_deployment_worker(self):
        """Start background worker for deployment processing"""
        while self._is_active:
            try:
                # Get deployment request from queue
                deployment_request = await asyncio.wait_for(
                    self._deployment_queue.get(), timeout=10.0
                )
                
                await self._process_deployment_request(deployment_request)
                self._deployment_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in deployment worker: {e}")
                await asyncio.sleep(5)
    
    async def _start_monitoring_loop(self):
        """Monitor active deployments"""
        while self._is_active:
            try:
                await self._monitor_active_deployments()
                await asyncio.sleep(300)  # Check every 5 minutes
            except Exception as e:
                self._logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _process_deployment_request(self, request: Dict[str, Any]):
        """Process a strategy deployment request"""
        try:
            backtest_results = request["backtest_results"]
            strategy_code = request.get("strategy_code")
            
            # Evaluate deployment eligibility
            evaluation = await self._evaluate_deployment_eligibility(backtest_results)
            
            if evaluation["eligible"]:
                # Create deployment
                deployment = await self._create_deployment(
                    backtest_results, strategy_code, evaluation
                )
                
                if deployment:
                    # Start with paper trading
                    await self._start_paper_trading(deployment)
                    
                    # Track deployment
                    self._active_deployments[deployment.deployment_id] = {
                        "deployment": deployment,
                        "status": DeploymentStatus.PAPER_TRADING,
                        "start_time": datetime.now(),
                        "performance": {}
                    }
                    
                    # Publish deployment notification
                    await self._publish_deployment_update(deployment)
            else:
                self._logger.info(
                    f"Strategy {backtest_results.strategy_name} not eligible for deployment: "
                    f"{evaluation['reasons']}"
                )
            
        except Exception as e:
            self._logger.error(f"Error processing deployment request: {e}")
    
    async def _evaluate_deployment_eligibility(self, backtest_results: BacktestResult) -> Dict[str, Any]:
        """Evaluate if a strategy is eligible for deployment"""
        try:
            metrics = backtest_results.performance_metrics
            reasons = []
            eligible = True
            
            # Check Sharpe ratio
            sharpe = metrics.get("sharpe_ratio", 0)
            if sharpe < self._deployment_criteria["min_sharpe_ratio"]:
                eligible = False
                reasons.append(f"Sharpe ratio {sharpe:.2f} below minimum {self._deployment_criteria['min_sharpe_ratio']}")
            
            # Check maximum drawdown
            max_dd = metrics.get("max_drawdown", 0)
            if max_dd < self._deployment_criteria["max_drawdown"]:
                eligible = False
                reasons.append(f"Max drawdown {max_dd:.1%} exceeds limit {self._deployment_criteria['max_drawdown']:.1%}")
            
            # Check total return
            total_return = metrics.get("total_return", 0)
            if total_return < self._deployment_criteria["min_total_return"]:
                eligible = False
                reasons.append(f"Total return {total_return:.1%} below minimum {self._deployment_criteria['min_total_return']:.1%}")
            
            # Check number of trades
            total_trades = metrics.get("total_trades", 0)
            if total_trades < self._deployment_criteria["min_trades"]:
                eligible = False
                reasons.append(f"Only {total_trades} trades, minimum {self._deployment_criteria['min_trades']} required")
            
            # LLM-based evaluation for additional insights
            llm_evaluation = await self._llm_evaluate_strategy(backtest_results)
            
            if not llm_evaluation.get("recommended", True):
                eligible = False
                reasons.extend(llm_evaluation.get("concerns", []))
            
            return {
                "eligible": eligible,
                "reasons": reasons,
                "score": self._calculate_deployment_score(metrics),
                "llm_insights": llm_evaluation
            }
            
        except Exception as e:
            self._logger.error(f"Error evaluating deployment eligibility: {e}")
            return {"eligible": False, "reasons": ["Evaluation error"], "score": 0}
    
    async def _llm_evaluate_strategy(self, backtest_results: BacktestResult) -> Dict[str, Any]:
        """Use LLM to evaluate strategy for deployment"""
        try:
            user_message = UserMessage(
                content=f"""Evaluate this strategy for live trading deployment:

Strategy: {backtest_results.strategy_name}
Backtest Period: {backtest_results.backtest_period}

Performance Metrics:
{json.dumps(backtest_results.performance_metrics, indent=2)}

Analysis Report:
{backtest_results.analysis_report}

Recommendations:
{json.dumps(backtest_results.recommendations, indent=2)}

Provide evaluation in JSON format with:
1. recommended: boolean (recommend for deployment)
2. confidence: float (0.0 to 1.0, confidence in recommendation)
3. concerns: list (specific concerns about deployment)
4. strengths: list (strategy strengths)
5. suggested_allocation: float (recommended portfolio allocation)
6. monitoring_priorities: list (key metrics to monitor)
7. risk_assessment: string (overall risk assessment)

Focus on practical deployment considerations and risk management.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    evaluation = json.loads(response.content)
                    return evaluation
                except json.JSONDecodeError:
                    return self._basic_llm_evaluation(backtest_results)
            
            return self._basic_llm_evaluation(backtest_results)
            
        except Exception as e:
            self._logger.error(f"Error in LLM evaluation: {e}")
            return self._basic_llm_evaluation(backtest_results)
    
    def _basic_llm_evaluation(self, backtest_results: BacktestResult) -> Dict[str, Any]:
        """Basic evaluation fallback"""
        metrics = backtest_results.performance_metrics
        sharpe = metrics.get("sharpe_ratio", 0)
        total_return = metrics.get("total_return", 0)
        
        recommended = sharpe > 1.0 and total_return > 0.1
        
        return {
            "recommended": recommended,
            "confidence": 0.6,
            "concerns": [] if recommended else ["Low performance metrics"],
            "strengths": ["Meets basic criteria"] if recommended else [],
            "suggested_allocation": 0.02 if recommended else 0.0,
            "monitoring_priorities": ["drawdown", "sharpe_ratio", "win_rate"],
            "risk_assessment": "moderate"
        }
    
    def _calculate_deployment_score(self, metrics: Dict[str, float]) -> float:
        """Calculate deployment score based on metrics"""
        try:
            # Weighted scoring
            sharpe_score = min(metrics.get("sharpe_ratio", 0) / 2.0, 1.0) * 0.4
            return_score = min(metrics.get("total_return", 0) / 0.3, 1.0) * 0.3
            drawdown_score = max(0, 1 + metrics.get("max_drawdown", -1) / 0.3) * 0.3
            
            return sharpe_score + return_score + drawdown_score
            
        except Exception:
            return 0.0
    
    async def _create_deployment(
        self,
        backtest_results: BacktestResult,
        strategy_code: Optional[StrategyCode],
        evaluation: Dict[str, Any]
    ) -> Optional[StrategyDeployment]:
        """Create a new strategy deployment"""
        try:
            deployment_id = f"deploy_{backtest_results.strategy_name}_{datetime.now().timestamp()}"
            
            deployment = StrategyDeployment(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=deployment_id,
                deployment_id=deployment_id,
                strategy_name=backtest_results.strategy_name,
                deployment_status="paper_trading",
                allocated_capital=evaluation["llm_insights"].get("suggested_allocation", 0.02),
                risk_limits=self._risk_limits.copy(),
                performance_targets={
                    "target_return": backtest_results.performance_metrics.get("annual_return", 0.2),
                    "max_drawdown": self._risk_limits["max_drawdown_live"],
                    "min_sharpe": 1.0
                }
            )
            
            return deployment
            
        except Exception as e:
            self._logger.error(f"Error creating deployment: {e}")
            return None
    
    async def _start_paper_trading(self, deployment: StrategyDeployment):
        """Start paper trading for a deployment"""
        try:
            # Send paper trading signal
            paper_signal = TradingSignal(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"paper_{deployment.deployment_id}_{datetime.now().timestamp()}",
                signal_type="start_paper_trading",
                strategy_name=deployment.strategy_name,
                action="start",
                parameters={
                    "deployment_id": deployment.deployment_id,
                    "paper_trading_days": self._risk_limits["paper_trading_days"]
                }
            )
            
            # Send to monitoring agent
            await self.publish_message(
                paper_signal,
                topic_id=TopicId("RealTimeMonitoringAgent", source=self.id.key)
            )
            
            self._logger.info(f"Started paper trading for {deployment.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error starting paper trading: {e}")
    
    async def _monitor_active_deployments(self):
        """Monitor active deployments and manage progression"""
        for deployment_id, deployment_info in list(self._active_deployments.items()):
            try:
                deployment = deployment_info["deployment"]
                status = deployment_info["status"]
                start_time = deployment_info["start_time"]
                
                # Check if paper trading period is complete
                if (status == DeploymentStatus.PAPER_TRADING and 
                    datetime.now() - start_time > timedelta(days=self._risk_limits["paper_trading_days"])):
                    
                    # Evaluate paper trading performance
                    paper_performance = await self._evaluate_paper_performance(deployment_id)
                    
                    if paper_performance.get("approved", False):
                        await self._promote_to_live_trading(deployment_id, "small")
                    else:
                        await self._pause_deployment(deployment_id, "Poor paper trading performance")
                
            except Exception as e:
                self._logger.error(f"Error monitoring deployment {deployment_id}: {e}")
    
    async def _evaluate_paper_performance(self, deployment_id: str) -> Dict[str, Any]:
        """Evaluate paper trading performance"""
        # In production, this would fetch actual paper trading results
        # For now, simulate evaluation
        return {
            "approved": True,
            "return": 0.05,
            "sharpe": 1.3,
            "max_drawdown": -0.03
        }
    
    async def _promote_to_live_trading(self, deployment_id: str, size: str):
        """Promote deployment to live trading"""
        try:
            deployment_info = self._active_deployments[deployment_id]
            deployment = deployment_info["deployment"]
            
            # Update status
            new_status = DeploymentStatus.LIVE_SMALL if size == "small" else DeploymentStatus.LIVE_FULL
            deployment_info["status"] = new_status
            
            # Send live trading signal
            live_signal = TradingSignal(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"live_{deployment_id}_{datetime.now().timestamp()}",
                signal_type="start_live_trading",
                strategy_name=deployment.strategy_name,
                action="start",
                parameters={
                    "deployment_id": deployment_id,
                    "size": size,
                    "allocated_capital": deployment.allocated_capital
                }
            )
            
            await self.publish_message(
                live_signal,
                topic_id=TopicId("RealTimeMonitoringAgent", source=self.id.key)
            )
            
            self._logger.info(f"Promoted {deployment.strategy_name} to {size} live trading")
            
        except Exception as e:
            self._logger.error(f"Error promoting to live trading: {e}")
    
    async def _pause_deployment(self, deployment_id: str, reason: str):
        """Pause a deployment"""
        try:
            deployment_info = self._active_deployments[deployment_id]
            deployment_info["status"] = DeploymentStatus.PAUSED
            
            self._logger.info(f"Paused deployment {deployment_id}: {reason}")
            
        except Exception as e:
            self._logger.error(f"Error pausing deployment: {e}")
    
    async def _publish_deployment_update(self, deployment: StrategyDeployment):
        """Publish deployment update"""
        try:
            # Send to control layer for notifications
            await self.publish_message(
                deployment,
                topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
            )
            
            self._logger.info(f"Published deployment update for {deployment.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error publishing deployment update: {e}")
    
    @message_handler
    async def handle_backtest_results(self, message: BacktestResult, ctx: MessageContext) -> None:
        """Handle backtest results for deployment evaluation"""
        await self._deployment_queue.put({
            "backtest_results": message,
            "strategy_code": None
        })
        
        self._logger.debug(f"Queued strategy for deployment evaluation: {message.strategy_name}")
    
    @message_handler
    async def handle_strategy_code(self, message: StrategyCode, ctx: MessageContext) -> None:
        """Handle strategy code for deployment"""
        # This would typically be paired with backtest results
        self._logger.debug(f"Received strategy code: {message.strategy_name}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Strategy deployment started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Strategy deployment stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "pause_all":
            for deployment_id in self._active_deployments:
                await self._pause_deployment(deployment_id, "Manual pause")
            self._logger.info("Paused all deployments")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "deployment":
            updates = message.updates
            
            if "deployment_criteria" in updates:
                self._deployment_criteria.update(updates["deployment_criteria"])
                self._logger.info("Updated deployment criteria")
            
            if "risk_limits" in updates:
                self._risk_limits.update(updates["risk_limits"])
                self._logger.info("Updated risk limits")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "StrategyDeploymentAgent",
            "active": self._is_active,
            "queue_size": self._deployment_queue.qsize(),
            "active_deployments": len(self._active_deployments),
            "deployment_criteria": self._deployment_criteria,
            "risk_limits": self._risk_limits,
            "deployment_statuses": {
                deployment_id: info["status"].value 
                for deployment_id, info in self._active_deployments.items()
            },
            "last_update": datetime.now().isoformat()
        }
