"""
Strategy Memory Agent

This agent manages long-term memory and learning for trading strategies,
storing performance data, market conditions, and optimization history.
"""

import asyncio
import json
import pickle
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import defaultdict, deque
from pathlib import Path

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    PerformanceReport,
    BacktestResults,
    StrategyDefinition,
    MemoryQuery,
    MemoryResponse,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class StrategyMemoryAgent(RoutedAgent):
    """
    Agent responsible for managing strategy memory and learning from historical performance.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Strategy Memory Agent",
        memory_path: str = "strategy_memory"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._memory_path = Path(memory_path)
        self._memory_path.mkdir(exist_ok=True)
        
        # Memory storage
        self._strategy_memory = defaultdict(lambda: {
            "performance_history": deque(maxlen=1000),
            "backtest_results": deque(maxlen=100),
            "optimization_history": deque(maxlen=50),
            "market_conditions": deque(maxlen=500),
            "learned_patterns": {},
            "success_factors": [],
            "failure_patterns": []
        })
        
        # Learning configuration
        self._learning_config = {
            "min_samples_for_learning": 10,
            "pattern_confidence_threshold": 0.7,
            "memory_retention_days": 365,
            "auto_save_interval": 3600,  # seconds
            "learning_update_frequency": 86400  # daily
        }
        
        # Pattern recognition
        self._pattern_types = [
            "market_regime_performance",
            "volatility_adaptation",
            "correlation_patterns",
            "seasonal_effects",
            "parameter_sensitivity"
        ]
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Strategy Memory Agent responsible for learning from trading strategy performance and identifying patterns.

Your responsibilities:
1. Store and organize historical strategy performance data
2. Identify patterns in strategy success and failure
3. Learn from market conditions and their impact on strategies
4. Provide insights based on historical performance
5. Recommend strategy improvements based on learned patterns
6. Maintain long-term memory of strategy evolution

When analyzing patterns, consider:
- Market regime changes and their impact on strategy performance
- Parameter sensitivity and optimization history
- Correlation between market conditions and strategy success
- Seasonal and cyclical patterns in performance
- Risk factor exposures and their evolution
- Strategy degradation patterns and early warning signs

Provide actionable insights based on historical data and learned patterns."""
        )
        
        # Load existing memory
        asyncio.create_task(self._load_memory())
        
        # Start background processes
        asyncio.create_task(self._start_learning_loop())
        asyncio.create_task(self._start_auto_save())
    
    async def _load_memory(self):
        """Load existing memory from disk"""
        try:
            memory_file = self._memory_path / "strategy_memory.pkl"
            if memory_file.exists():
                with open(memory_file, 'rb') as f:
                    loaded_memory = pickle.load(f)
                    self._strategy_memory.update(loaded_memory)
                self._logger.info(f"Loaded memory for {len(self._strategy_memory)} strategies")
            
        except Exception as e:
            self._logger.error(f"Error loading memory: {e}")
    
    async def _save_memory(self):
        """Save memory to disk"""
        try:
            memory_file = self._memory_path / "strategy_memory.pkl"
            with open(memory_file, 'wb') as f:
                pickle.dump(dict(self._strategy_memory), f)
            
            # Also save as JSON for human readability
            json_file = self._memory_path / "strategy_memory.json"
            with open(json_file, 'w') as f:
                json.dump(
                    {k: self._serialize_memory(v) for k, v in self._strategy_memory.items()},
                    f, indent=2, default=str
                )
            
            self._logger.debug("Saved strategy memory to disk")
            
        except Exception as e:
            self._logger.error(f"Error saving memory: {e}")
    
    def _serialize_memory(self, memory_data: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize memory data for JSON storage"""
        return {
            "performance_history": list(memory_data["performance_history"])[-10:],  # Last 10 entries
            "backtest_results": list(memory_data["backtest_results"])[-5:],  # Last 5 entries
            "optimization_history": list(memory_data["optimization_history"])[-5:],
            "learned_patterns": memory_data["learned_patterns"],
            "success_factors": memory_data["success_factors"],
            "failure_patterns": memory_data["failure_patterns"]
        }
    
    async def _start_learning_loop(self):
        """Start background learning process"""
        while self._is_active:
            try:
                await self._update_learned_patterns()
                await asyncio.sleep(self._learning_config["learning_update_frequency"])
            except Exception as e:
                self._logger.error(f"Error in learning loop: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour on error
    
    async def _start_auto_save(self):
        """Start automatic memory saving"""
        while self._is_active:
            try:
                await asyncio.sleep(self._learning_config["auto_save_interval"])
                await self._save_memory()
            except Exception as e:
                self._logger.error(f"Error in auto-save: {e}")
                await asyncio.sleep(1800)  # Wait 30 minutes on error
    
    async def _update_learned_patterns(self):
        """Update learned patterns from accumulated data"""
        try:
            for strategy_name, memory_data in self._strategy_memory.items():
                if len(memory_data["performance_history"]) >= self._learning_config["min_samples_for_learning"]:
                    patterns = await self._identify_patterns(strategy_name, memory_data)
                    memory_data["learned_patterns"].update(patterns)
                    
                    # Update success/failure factors
                    success_factors, failure_patterns = await self._analyze_success_failure(memory_data)
                    memory_data["success_factors"] = success_factors
                    memory_data["failure_patterns"] = failure_patterns
            
            self._logger.debug("Updated learned patterns for all strategies")
            
        except Exception as e:
            self._logger.error(f"Error updating learned patterns: {e}")
    
    async def _identify_patterns(self, strategy_name: str, memory_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify patterns in strategy performance"""
        try:
            patterns = {}
            
            # Analyze performance history
            performance_data = list(memory_data["performance_history"])
            
            if len(performance_data) < 5:
                return patterns
            
            # Market regime performance pattern
            regime_performance = self._analyze_market_regime_performance(performance_data)
            if regime_performance:
                patterns["market_regime_performance"] = regime_performance
            
            # Volatility adaptation pattern
            volatility_pattern = self._analyze_volatility_adaptation(performance_data)
            if volatility_pattern:
                patterns["volatility_adaptation"] = volatility_pattern
            
            # Parameter sensitivity analysis
            if memory_data["optimization_history"]:
                param_sensitivity = self._analyze_parameter_sensitivity(memory_data["optimization_history"])
                if param_sensitivity:
                    patterns["parameter_sensitivity"] = param_sensitivity
            
            # Use LLM for advanced pattern recognition
            llm_patterns = await self._llm_pattern_analysis(strategy_name, performance_data)
            patterns.update(llm_patterns)
            
            return patterns
            
        except Exception as e:
            self._logger.error(f"Error identifying patterns: {e}")
            return {}
    
    def _analyze_market_regime_performance(self, performance_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Analyze performance across different market regimes"""
        try:
            # Group performance by market conditions
            regime_performance = defaultdict(list)
            
            for entry in performance_data:
                market_condition = entry.get("market_condition", "unknown")
                performance = entry.get("performance_metrics", {})
                
                if "total_return" in performance:
                    regime_performance[market_condition].append(performance["total_return"])
            
            # Calculate average performance per regime
            regime_stats = {}
            for regime, returns in regime_performance.items():
                if len(returns) >= 3:  # Minimum samples
                    regime_stats[regime] = {
                        "avg_return": sum(returns) / len(returns),
                        "sample_count": len(returns),
                        "best_regime": regime == max(regime_performance.keys(), 
                                                  key=lambda k: sum(regime_performance[k]) / len(regime_performance[k]))
                    }
            
            return regime_stats if regime_stats else None
            
        except Exception as e:
            self._logger.error(f"Error analyzing market regime performance: {e}")
            return None
    
    def _analyze_volatility_adaptation(self, performance_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Analyze how strategy adapts to different volatility levels"""
        try:
            volatility_performance = {"low": [], "medium": [], "high": []}
            
            for entry in performance_data:
                volatility = entry.get("market_volatility", 0.2)
                performance = entry.get("performance_metrics", {})
                
                if "total_return" in performance:
                    if volatility < 0.15:
                        volatility_performance["low"].append(performance["total_return"])
                    elif volatility < 0.3:
                        volatility_performance["medium"].append(performance["total_return"])
                    else:
                        volatility_performance["high"].append(performance["total_return"])
            
            # Calculate statistics
            vol_stats = {}
            for vol_level, returns in volatility_performance.items():
                if len(returns) >= 2:
                    vol_stats[vol_level] = {
                        "avg_return": sum(returns) / len(returns),
                        "sample_count": len(returns)
                    }
            
            return vol_stats if vol_stats else None
            
        except Exception as e:
            self._logger.error(f"Error analyzing volatility adaptation: {e}")
            return None
    
    def _analyze_parameter_sensitivity(self, optimization_history: deque) -> Optional[Dict[str, Any]]:
        """Analyze parameter sensitivity from optimization history"""
        try:
            if len(optimization_history) < 3:
                return None
            
            # Analyze which parameters have the most impact
            parameter_impacts = defaultdict(list)
            
            for opt_result in optimization_history:
                parameters = opt_result.get("parameters", {})
                performance = opt_result.get("performance", 0)
                
                for param_name, param_value in parameters.items():
                    parameter_impacts[param_name].append((param_value, performance))
            
            # Calculate sensitivity scores
            sensitivity_scores = {}
            for param_name, values_performance in parameter_impacts.items():
                if len(values_performance) >= 3:
                    # Simple correlation analysis
                    values = [vp[0] for vp in values_performance]
                    performances = [vp[1] for vp in values_performance]
                    
                    # Calculate correlation coefficient
                    correlation = self._calculate_correlation(values, performances)
                    sensitivity_scores[param_name] = {
                        "correlation": correlation,
                        "sensitivity": abs(correlation)
                    }
            
            return sensitivity_scores if sensitivity_scores else None
            
        except Exception as e:
            self._logger.error(f"Error analyzing parameter sensitivity: {e}")
            return None
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """Calculate correlation coefficient"""
        try:
            if len(x) != len(y) or len(x) < 2:
                return 0.0
            
            n = len(x)
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)
            sum_y2 = sum(yi * yi for yi in y)
            
            numerator = n * sum_xy - sum_x * sum_y
            denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
            
            if denominator == 0:
                return 0.0
            
            return numerator / denominator
            
        except Exception:
            return 0.0
    
    async def _llm_pattern_analysis(self, strategy_name: str, performance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Use LLM for advanced pattern analysis"""
        try:
            # Prepare data summary for LLM
            data_summary = {
                "strategy_name": strategy_name,
                "total_entries": len(performance_data),
                "recent_performance": performance_data[-5:] if len(performance_data) >= 5 else performance_data,
                "performance_trend": self._calculate_performance_trend(performance_data)
            }
            
            user_message = UserMessage(
                content=f"""Analyze patterns in this strategy's performance data:

{json.dumps(data_summary, indent=2, default=str)}

Identify patterns in JSON format with:
1. trend_patterns: dict (performance trends and cycles)
2. risk_patterns: dict (risk behavior patterns)
3. market_sensitivity: dict (sensitivity to market conditions)
4. optimization_insights: list (insights for optimization)
5. warning_signals: list (early warning patterns)
6. success_indicators: list (patterns associated with success)

Focus on actionable patterns that can improve strategy performance.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    patterns = json.loads(response.content)
                    return patterns
                except json.JSONDecodeError:
                    return {}
            
            return {}
            
        except Exception as e:
            self._logger.error(f"Error in LLM pattern analysis: {e}")
            return {}
    
    def _calculate_performance_trend(self, performance_data: List[Dict[str, Any]]) -> str:
        """Calculate overall performance trend"""
        try:
            if len(performance_data) < 3:
                return "insufficient_data"
            
            recent_returns = []
            for entry in performance_data[-5:]:
                metrics = entry.get("performance_metrics", {})
                if "total_return" in metrics:
                    recent_returns.append(metrics["total_return"])
            
            if len(recent_returns) < 2:
                return "insufficient_data"
            
            # Simple trend calculation
            if recent_returns[-1] > recent_returns[0]:
                return "improving"
            elif recent_returns[-1] < recent_returns[0]:
                return "declining"
            else:
                return "stable"
                
        except Exception:
            return "unknown"
    
    async def _analyze_success_failure(self, memory_data: Dict[str, Any]) -> tuple:
        """Analyze success and failure patterns"""
        try:
            performance_history = list(memory_data["performance_history"])
            
            if len(performance_history) < 5:
                return [], []
            
            # Classify entries as success or failure
            successes = []
            failures = []
            
            for entry in performance_history:
                metrics = entry.get("performance_metrics", {})
                total_return = metrics.get("total_return", 0)
                sharpe_ratio = metrics.get("sharpe_ratio", 0)
                
                # Simple success criteria
                if total_return > 0.05 and sharpe_ratio > 1.0:  # 5% return and Sharpe > 1
                    successes.append(entry)
                elif total_return < -0.02 or sharpe_ratio < 0.5:  # -2% return or low Sharpe
                    failures.append(entry)
            
            # Extract common factors
            success_factors = self._extract_common_factors(successes)
            failure_patterns = self._extract_common_factors(failures)
            
            return success_factors, failure_patterns
            
        except Exception as e:
            self._logger.error(f"Error analyzing success/failure: {e}")
            return [], []
    
    def _extract_common_factors(self, entries: List[Dict[str, Any]]) -> List[str]:
        """Extract common factors from a list of entries"""
        try:
            if len(entries) < 2:
                return []
            
            factors = []
            
            # Analyze market conditions
            market_conditions = [e.get("market_condition", "unknown") for e in entries]
            most_common_condition = max(set(market_conditions), key=market_conditions.count)
            if market_conditions.count(most_common_condition) / len(market_conditions) > 0.6:
                factors.append(f"market_condition_{most_common_condition}")
            
            # Analyze volatility levels
            volatilities = [e.get("market_volatility", 0.2) for e in entries]
            avg_volatility = sum(volatilities) / len(volatilities)
            if avg_volatility < 0.15:
                factors.append("low_volatility_environment")
            elif avg_volatility > 0.3:
                factors.append("high_volatility_environment")
            
            return factors
            
        except Exception as e:
            self._logger.error(f"Error extracting common factors: {e}")
            return []
    
    async def _query_memory(self, query: MemoryQuery) -> MemoryResponse:
        """Query strategy memory"""
        try:
            strategy_name = query.strategy_name
            query_type = query.query_type
            
            if strategy_name not in self._strategy_memory:
                return MemoryResponse(
                    timestamp=datetime.now(),
                    source_agent=self.id.type,
                    message_id=f"memory_response_{datetime.now().timestamp()}",
                    query_id=query.message_id,
                    results={},
                    insights=[],
                    recommendations=[]
                )
            
            memory_data = self._strategy_memory[strategy_name]
            results = {}
            insights = []
            recommendations = []
            
            if query_type == "performance_history":
                results["performance_history"] = list(memory_data["performance_history"])[-10:]
                
            elif query_type == "learned_patterns":
                results["learned_patterns"] = memory_data["learned_patterns"]
                insights = [f"Pattern: {k}" for k in memory_data["learned_patterns"].keys()]
                
            elif query_type == "success_factors":
                results["success_factors"] = memory_data["success_factors"]
                results["failure_patterns"] = memory_data["failure_patterns"]
                recommendations = ["Focus on success factors", "Avoid failure patterns"]
                
            elif query_type == "optimization_insights":
                if memory_data["optimization_history"]:
                    results["optimization_history"] = list(memory_data["optimization_history"])[-5:]
                    insights = ["Parameter optimization history available"]
            
            return MemoryResponse(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"memory_response_{datetime.now().timestamp()}",
                query_id=query.message_id,
                results=results,
                insights=insights,
                recommendations=recommendations
            )
            
        except Exception as e:
            self._logger.error(f"Error querying memory: {e}")
            return MemoryResponse(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"memory_error_{datetime.now().timestamp()}",
                query_id=query.message_id,
                results={"error": str(e)},
                insights=[],
                recommendations=[]
            )
    
    @message_handler
    async def handle_performance_report(self, message: PerformanceReport, ctx: MessageContext) -> None:
        """Handle performance reports for memory storage"""
        try:
            strategy_name = message.strategy_name
            
            # Store performance data
            memory_entry = {
                "timestamp": message.timestamp,
                "report_period": message.report_period,
                "performance_metrics": message.performance_metrics,
                "analysis_summary": message.analysis_summary,
                "key_insights": message.key_insights,
                "recommendations": message.recommendations,
                "risk_assessment": message.risk_assessment,
                "market_condition": "unknown",  # Would be enriched with market data
                "market_volatility": 0.2  # Would be calculated from market data
            }
            
            self._strategy_memory[strategy_name]["performance_history"].append(memory_entry)
            
            self._logger.debug(f"Stored performance report for {strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error handling performance report: {e}")
    
    @message_handler
    async def handle_backtest_results(self, message: BacktestResults, ctx: MessageContext) -> None:
        """Handle backtest results for memory storage"""
        try:
            strategy_name = message.strategy_name
            
            backtest_entry = {
                "timestamp": message.timestamp,
                "backtest_period": message.backtest_period,
                "performance_metrics": message.performance_metrics,
                "trade_summary": message.trade_summary,
                "analysis_report": message.analysis_report,
                "recommendations": message.recommendations
            }
            
            self._strategy_memory[strategy_name]["backtest_results"].append(backtest_entry)
            
            self._logger.debug(f"Stored backtest results for {strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error handling backtest results: {e}")
    
    @message_handler
    async def handle_memory_query(self, message: MemoryQuery, ctx: MessageContext) -> None:
        """Handle memory queries"""
        try:
            response = await self._query_memory(message)
            
            # Send response back to requester
            await self.publish_message(
                response,
                topic_id=TopicId(message.source_agent, source=self.id.key)
            )
            
        except Exception as e:
            self._logger.error(f"Error handling memory query: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Strategy memory started")
        elif command == "stop":
            self._is_active = False
            await self._save_memory()
            self._logger.info("Strategy memory stopped and saved")
        elif command == "save_memory":
            await self._save_memory()
            self._logger.info("Memory saved manually")
        elif command == "update_patterns":
            await self._update_learned_patterns()
            self._logger.info("Updated learned patterns")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "memory":
            updates = message.updates
            
            if "learning_config" in updates:
                self._learning_config.update(updates["learning_config"])
                self._logger.info("Updated learning configuration")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        total_entries = sum(
            len(data["performance_history"]) + len(data["backtest_results"])
            for data in self._strategy_memory.values()
        )
        
        return {
            "agent_type": "StrategyMemoryAgent",
            "active": self._is_active,
            "tracked_strategies": len(self._strategy_memory),
            "total_memory_entries": total_entries,
            "learning_config": self._learning_config,
            "memory_path": str(self._memory_path),
            "patterns_learned": sum(
                len(data["learned_patterns"]) for data in self._strategy_memory.values()
            ),
            "last_update": datetime.now().isoformat()
        }
