"""
Performance Analysis Agent

This agent performs comprehensive performance analysis of live trading strategies,
generates detailed reports, and provides optimization recommendations.
"""

import asyncio
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque, defaultdict
from dataclasses import asdict

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    PerformanceAlert,
    PerformanceReport,
    TradingSignal,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class PerformanceAnalysisAgent(RoutedAgent):
    """
    Agent responsible for comprehensive performance analysis and reporting.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Performance Analysis Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._analysis_queue = asyncio.Queue()
        
        # Performance data storage
        self._strategy_performance = defaultdict(lambda: {
            "trades": deque(maxlen=10000),
            "daily_pnl": deque(maxlen=365),
            "positions": deque(maxlen=1000),
            "metrics_history": deque(maxlen=100)
        })
        
        # Analysis configuration
        self._analysis_config = {
            "report_frequency": "daily",  # daily, weekly, monthly
            "lookback_periods": [1, 7, 30, 90],  # days
            "benchmark_assets": ["BTC", "ETH", "SPY"],
            "risk_free_rate": 0.02,  # 2% annual risk-free rate
            "confidence_levels": [0.95, 0.99]  # For VaR calculations
        }
        
        # Performance metrics to calculate
        self._metrics_config = {
            "return_metrics": ["total_return", "annual_return", "monthly_returns"],
            "risk_metrics": ["volatility", "max_drawdown", "var_95", "var_99"],
            "ratio_metrics": ["sharpe_ratio", "sortino_ratio", "calmar_ratio"],
            "trade_metrics": ["win_rate", "profit_factor", "avg_trade_duration"],
            "advanced_metrics": ["beta", "alpha", "information_ratio", "treynor_ratio"]
        }
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Performance Analysis Agent specialized in analyzing trading strategy performance and generating comprehensive reports.

Your responsibilities:
1. Calculate comprehensive performance metrics and risk statistics
2. Analyze strategy performance across different time periods and market conditions
3. Compare strategy performance against benchmarks
4. Identify performance patterns, trends, and anomalies
5. Generate actionable insights and optimization recommendations
6. Provide statistical significance testing for performance claims

When analyzing performance, consider:
- Risk-adjusted returns and their statistical significance
- Performance consistency across different market regimes
- Correlation with market factors and other strategies
- Transaction costs and implementation considerations
- Capacity constraints and scalability
- Behavioral biases in strategy design and execution

Provide detailed, data-driven analysis with clear recommendations for improvement."""
        )
        
        # Start background processing
        asyncio.create_task(self._start_analysis_worker())
        asyncio.create_task(self._start_periodic_reporting())
    
    async def _start_analysis_worker(self):
        """Start background worker for performance analysis"""
        while self._is_active:
            try:
                # Get analysis request from queue
                analysis_request = await asyncio.wait_for(
                    self._analysis_queue.get(), timeout=30.0
                )
                
                await self._process_analysis_request(analysis_request)
                self._analysis_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in analysis worker: {e}")
                await asyncio.sleep(10)
    
    async def _start_periodic_reporting(self):
        """Generate periodic performance reports"""
        while self._is_active:
            try:
                # Generate daily reports
                await self._generate_periodic_reports()
                
                # Wait until next reporting time
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                self._logger.error(f"Error in periodic reporting: {e}")
                await asyncio.sleep(1800)  # Wait 30 minutes on error
    
    async def _process_analysis_request(self, request: Dict[str, Any]):
        """Process a performance analysis request"""
        try:
            request_type = request.get("type", "comprehensive")
            strategy_name = request.get("strategy_name")
            
            if request_type == "comprehensive":
                await self._generate_comprehensive_analysis(strategy_name)
            elif request_type == "alert_analysis":
                await self._analyze_performance_alert(request["alert"])
            elif request_type == "benchmark_comparison":
                await self._generate_benchmark_comparison(strategy_name)
            
        except Exception as e:
            self._logger.error(f"Error processing analysis request: {e}")
    
    async def _generate_comprehensive_analysis(self, strategy_name: str):
        """Generate comprehensive performance analysis for a strategy"""
        try:
            if strategy_name not in self._strategy_performance:
                self._logger.warning(f"No performance data for strategy: {strategy_name}")
                return
            
            # Calculate performance metrics
            metrics = await self._calculate_comprehensive_metrics(strategy_name)
            
            # Generate LLM analysis
            analysis = await self._generate_llm_analysis(strategy_name, metrics)
            
            # Create performance report
            report = PerformanceReport(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"report_{strategy_name}_{datetime.now().timestamp()}",
                strategy_name=strategy_name,
                report_period="comprehensive",
                performance_metrics=metrics,
                analysis_summary=analysis.get("summary", ""),
                key_insights=analysis.get("insights", []),
                recommendations=analysis.get("recommendations", []),
                risk_assessment=analysis.get("risk_assessment", "")
            )
            
            # Publish report
            await self._publish_performance_report(report)
            
        except Exception as e:
            self._logger.error(f"Error generating comprehensive analysis: {e}")
    
    async def _calculate_comprehensive_metrics(self, strategy_name: str) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        try:
            strategy_data = self._strategy_performance[strategy_name]
            
            # Convert trade data to DataFrame for analysis
            trades_data = list(strategy_data["trades"])
            daily_pnl_data = list(strategy_data["daily_pnl"])
            
            if not trades_data and not daily_pnl_data:
                return {}
            
            metrics = {}
            
            # Basic return metrics
            if daily_pnl_data:
                returns = np.array([d["pnl_pct"] for d in daily_pnl_data])
                
                metrics.update({
                    "total_return": float(np.sum(returns)),
                    "annual_return": float(np.mean(returns) * 252),  # Assuming daily data
                    "volatility": float(np.std(returns) * np.sqrt(252)),
                    "sharpe_ratio": float(self._calculate_sharpe_ratio(returns)),
                    "sortino_ratio": float(self._calculate_sortino_ratio(returns)),
                    "max_drawdown": float(self._calculate_max_drawdown(returns)),
                    "calmar_ratio": float(self._calculate_calmar_ratio(returns))
                })
                
                # VaR calculations
                metrics.update({
                    "var_95": float(np.percentile(returns, 5)),
                    "var_99": float(np.percentile(returns, 1)),
                    "cvar_95": float(np.mean(returns[returns <= np.percentile(returns, 5)])),
                    "cvar_99": float(np.mean(returns[returns <= np.percentile(returns, 1)]))
                })
            
            # Trade-based metrics
            if trades_data:
                trade_returns = [t.get("return", 0) for t in trades_data if "return" in t]
                
                if trade_returns:
                    winning_trades = [r for r in trade_returns if r > 0]
                    losing_trades = [r for r in trade_returns if r < 0]
                    
                    metrics.update({
                        "total_trades": len(trade_returns),
                        "win_rate": len(winning_trades) / len(trade_returns),
                        "avg_win": float(np.mean(winning_trades)) if winning_trades else 0,
                        "avg_loss": float(np.mean(losing_trades)) if losing_trades else 0,
                        "profit_factor": abs(sum(winning_trades) / sum(losing_trades)) if losing_trades else float('inf'),
                        "largest_win": float(max(trade_returns)) if trade_returns else 0,
                        "largest_loss": float(min(trade_returns)) if trade_returns else 0
                    })
            
            # Time-based analysis
            for period in self._analysis_config["lookback_periods"]:
                period_metrics = self._calculate_period_metrics(strategy_name, period)
                metrics[f"metrics_{period}d"] = period_metrics
            
            return metrics
            
        except Exception as e:
            self._logger.error(f"Error calculating metrics: {e}")
            return {}
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) == 0 or np.std(returns) == 0:
            return 0.0
        
        excess_returns = returns - (self._analysis_config["risk_free_rate"] / 252)
        return np.mean(excess_returns) / np.std(returns) * np.sqrt(252)
    
    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sortino ratio"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - (self._analysis_config["risk_free_rate"] / 252)
        downside_returns = returns[returns < 0]
        
        if len(downside_returns) == 0:
            return float('inf')
        
        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return float('inf')
        
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252)
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calculate maximum drawdown"""
        if len(returns) == 0:
            return 0.0
        
        cumulative = np.cumprod(1 + returns)
        peak = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - peak) / peak
        
        return np.min(drawdown)
    
    def _calculate_calmar_ratio(self, returns: np.ndarray) -> float:
        """Calculate Calmar ratio"""
        if len(returns) == 0:
            return 0.0
        
        annual_return = np.mean(returns) * 252
        max_dd = abs(self._calculate_max_drawdown(returns))
        
        if max_dd == 0:
            return float('inf')
        
        return annual_return / max_dd
    
    def _calculate_period_metrics(self, strategy_name: str, period_days: int) -> Dict[str, float]:
        """Calculate metrics for a specific period"""
        try:
            strategy_data = self._strategy_performance[strategy_name]
            cutoff_date = datetime.now() - timedelta(days=period_days)
            
            # Filter data for the period
            period_pnl = [
                d for d in strategy_data["daily_pnl"]
                if d.get("timestamp", datetime.now()) >= cutoff_date
            ]
            
            if not period_pnl:
                return {}
            
            returns = np.array([d["pnl_pct"] for d in period_pnl])
            
            return {
                "return": float(np.sum(returns)),
                "volatility": float(np.std(returns) * np.sqrt(252)),
                "sharpe": float(self._calculate_sharpe_ratio(returns)),
                "max_drawdown": float(self._calculate_max_drawdown(returns))
            }
            
        except Exception as e:
            self._logger.error(f"Error calculating period metrics: {e}")
            return {}
    
    async def _generate_llm_analysis(self, strategy_name: str, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate LLM-based analysis of performance metrics"""
        try:
            user_message = UserMessage(
                content=f"""Analyze the performance of trading strategy '{strategy_name}':

Performance Metrics:
{json.dumps(metrics, indent=2, default=str)}

Provide analysis in JSON format with:
1. summary: string (executive summary of performance)
2. insights: list (key insights about strategy performance)
3. recommendations: list (specific recommendations for improvement)
4. risk_assessment: string (assessment of risk characteristics)
5. strengths: list (strategy strengths)
6. weaknesses: list (areas for improvement)
7. market_suitability: string (suitable market conditions)
8. optimization_priorities: list (prioritized optimization areas)

Focus on actionable insights and data-driven recommendations.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_analysis_fallback(metrics)
            
            return self._basic_analysis_fallback(metrics)
            
        except Exception as e:
            self._logger.error(f"Error in LLM analysis: {e}")
            return self._basic_analysis_fallback(metrics)
    
    def _basic_analysis_fallback(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Basic analysis fallback when LLM fails"""
        total_return = metrics.get("total_return", 0)
        sharpe_ratio = metrics.get("sharpe_ratio", 0)
        max_drawdown = metrics.get("max_drawdown", 0)
        
        return {
            "summary": f"Strategy achieved {total_return:.1%} return with {sharpe_ratio:.2f} Sharpe ratio",
            "insights": [
                f"Total return: {total_return:.1%}",
                f"Risk-adjusted return (Sharpe): {sharpe_ratio:.2f}",
                f"Maximum drawdown: {max_drawdown:.1%}"
            ],
            "recommendations": ["Monitor performance closely", "Consider optimization"],
            "risk_assessment": "Moderate risk based on drawdown characteristics",
            "strengths": ["Positive returns"] if total_return > 0 else [],
            "weaknesses": ["High drawdown"] if max_drawdown < -0.15 else [],
            "market_suitability": "Trending markets",
            "optimization_priorities": ["Risk management", "Entry/exit timing"]
        }
    
    async def _analyze_performance_alert(self, alert: PerformanceAlert):
        """Analyze a performance alert and provide detailed insights"""
        try:
            strategy_name = alert.strategy_name
            
            # Get recent performance data
            recent_metrics = self._calculate_period_metrics(strategy_name, 7)  # Last 7 days
            
            # Generate alert-specific analysis
            analysis = await self._generate_alert_analysis(alert, recent_metrics)
            
            # Create detailed report
            report = PerformanceReport(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"alert_analysis_{alert.message_id}",
                strategy_name=strategy_name,
                report_period="alert_response",
                performance_metrics=recent_metrics,
                analysis_summary=analysis.get("summary", ""),
                key_insights=analysis.get("insights", []),
                recommendations=analysis.get("recommendations", []),
                risk_assessment=analysis.get("risk_assessment", "")
            )
            
            await self._publish_performance_report(report)
            
        except Exception as e:
            self._logger.error(f"Error analyzing performance alert: {e}")
    
    async def _generate_alert_analysis(self, alert: PerformanceAlert, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate analysis specific to a performance alert"""
        return {
            "summary": f"Alert analysis for {alert.alert_type}: {alert.message}",
            "insights": [
                f"Alert triggered: {alert.message}",
                f"Current value: {alert.current_value}",
                f"Threshold: {alert.threshold_value}"
            ],
            "recommendations": alert.recommended_actions,
            "risk_assessment": f"Elevated risk due to {alert.alert_type}"
        }
    
    async def _generate_periodic_reports(self):
        """Generate periodic performance reports"""
        try:
            current_hour = datetime.now().hour
            
            # Generate daily reports at 9 AM
            if current_hour == 9 and self._analysis_config["report_frequency"] in ["daily", "weekly", "monthly"]:
                for strategy_name in self._strategy_performance.keys():
                    await self._generate_comprehensive_analysis(strategy_name)
            
        except Exception as e:
            self._logger.error(f"Error generating periodic reports: {e}")
    
    async def _publish_performance_report(self, report: PerformanceReport):
        """Publish performance report"""
        try:
            # Send to control layer for notifications
            await self.publish_message(
                report,
                topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
            )
            
            # Send to strategy memory for storage
            await self.publish_message(
                report,
                topic_id=TopicId("StrategyMemoryAgent", source=self.id.key)
            )
            
            self._logger.info(f"Published performance report for {report.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error publishing performance report: {e}")
    
    @message_handler
    async def handle_performance_alert(self, message: PerformanceAlert, ctx: MessageContext) -> None:
        """Handle performance alerts for detailed analysis"""
        await self._analysis_queue.put({
            "type": "alert_analysis",
            "alert": message
        })
        
        self._logger.debug(f"Queued alert analysis for {message.strategy_name}")
    
    @message_handler
    async def handle_trading_signal(self, message: TradingSignal, ctx: MessageContext) -> None:
        """Handle trading signals to track performance data"""
        try:
            strategy_name = message.strategy_name
            
            # Store trade data for analysis
            if hasattr(message, 'trade_data'):
                self._strategy_performance[strategy_name]["trades"].append({
                    "timestamp": message.timestamp,
                    "signal_type": message.signal_type,
                    "action": message.action,
                    "parameters": message.parameters
                })
            
        except Exception as e:
            self._logger.error(f"Error handling trading signal: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Performance analysis started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Performance analysis stopped")
        elif command == "generate_reports":
            for strategy_name in self._strategy_performance.keys():
                await self._analysis_queue.put({
                    "type": "comprehensive",
                    "strategy_name": strategy_name
                })
            self._logger.info("Queued comprehensive reports for all strategies")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "performance_analysis":
            updates = message.updates
            
            if "analysis_config" in updates:
                self._analysis_config.update(updates["analysis_config"])
                self._logger.info("Updated analysis configuration")
            
            if "metrics_config" in updates:
                self._metrics_config.update(updates["metrics_config"])
                self._logger.info("Updated metrics configuration")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "PerformanceAnalysisAgent",
            "active": self._is_active,
            "queue_size": self._analysis_queue.qsize(),
            "tracked_strategies": len(self._strategy_performance),
            "analysis_config": self._analysis_config,
            "total_trades_tracked": sum(
                len(data["trades"]) for data in self._strategy_performance.values()
            ),
            "last_update": datetime.now().isoformat()
        }
