"""
Strategy Coding Agent

This agent converts strategy definitions into executable Python code
for backtesting and live trading implementation.
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
import textwrap

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    StrategyDefinition,
    StrategyCode,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class StrategyCodingAgent(RoutedAgent):
    """
    Agent responsible for converting strategy definitions into executable code.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Strategy Coding Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._coding_queue = asyncio.Queue()
        
        # Code templates and frameworks
        self._base_strategy_template = '''
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

class {strategy_class_name}:
    """
    {strategy_description}
    
    Generated automatically by StrategyCodingAgent
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{{__name__}}.{{self.__class__.__name__}}")
        
        # Strategy parameters
        self.position_size = config.get("position_size", 0.02)
        self.stop_loss = config.get("stop_loss", 0.05)
        self.take_profit = config.get("take_profit", 0.15)
        self.max_exposure = config.get("max_exposure", 0.1)
        
        # State tracking
        self.positions = {{}}
        self.signals = []
        self.performance_metrics = {{}}
        
    def generate_signals(self, market_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on market data
        """
        signals = []
        
        try:
            {signal_generation_code}
        except Exception as e:
            self.logger.error(f"Error generating signals: {{e}}")
            
        return signals
    
    def check_entry_conditions(self, data: pd.DataFrame, asset: str) -> bool:
        """
        Check if entry conditions are met for an asset
        """
        try:
            {entry_conditions_code}
        except Exception as e:
            self.logger.error(f"Error checking entry conditions: {{e}}")
            return False
    
    def check_exit_conditions(self, data: pd.DataFrame, asset: str, position: Dict[str, Any]) -> bool:
        """
        Check if exit conditions are met for a position
        """
        try:
            {exit_conditions_code}
        except Exception as e:
            self.logger.error(f"Error checking exit conditions: {{e}}")
            return False
    
    def calculate_position_size(self, asset: str, signal_strength: float) -> float:
        """
        Calculate position size based on risk parameters
        """
        base_size = self.position_size * signal_strength
        
        # Apply maximum exposure limits
        current_exposure = sum(
            pos.get("size", 0) for pos in self.positions.values()
            if pos.get("asset") == asset
        )
        
        max_additional = max(0, self.max_exposure - current_exposure)
        return min(base_size, max_additional)
    
    def execute_strategy(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Execute the complete strategy logic
        """
        results = {{
            "signals": [],
            "trades": [],
            "positions": self.positions.copy(),
            "metrics": {{}}
        }}
        
        try:
            # Generate signals
            signals = self.generate_signals(market_data)
            results["signals"] = signals
            
            # Process signals and execute trades
            trades = self._process_signals(signals, market_data)
            results["trades"] = trades
            
            # Update performance metrics
            results["metrics"] = self._calculate_metrics(market_data)
            
        except Exception as e:
            self.logger.error(f"Error executing strategy: {{e}}")
            
        return results
    
    def _process_signals(self, signals: List[Dict[str, Any]], market_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Process signals and generate trades
        """
        trades = []
        
        for signal in signals:
            try:
                asset = signal.get("asset")
                signal_type = signal.get("type")
                strength = signal.get("strength", 0.5)
                
                if signal_type == "buy" and self.check_entry_conditions(market_data, asset):
                    size = self.calculate_position_size(asset, strength)
                    if size > 0:
                        trade = {{
                            "asset": asset,
                            "type": "buy",
                            "size": size,
                            "timestamp": datetime.now(),
                            "signal_strength": strength
                        }}
                        trades.append(trade)
                        
                elif signal_type == "sell" and asset in self.positions:
                    position = self.positions[asset]
                    if self.check_exit_conditions(market_data, asset, position):
                        trade = {{
                            "asset": asset,
                            "type": "sell",
                            "size": position.get("size", 0),
                            "timestamp": datetime.now(),
                            "reason": "exit_condition"
                        }}
                        trades.append(trade)
                        
            except Exception as e:
                self.logger.error(f"Error processing signal: {{e}}")
                
        return trades
    
    def _calculate_metrics(self, market_data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate performance metrics
        """
        metrics = {{
            "total_return": 0.0,
            "sharpe_ratio": 0.0,
            "max_drawdown": 0.0,
            "win_rate": 0.0,
            "profit_factor": 0.0
        }}
        
        # Implementation would calculate actual metrics based on trade history
        # This is a placeholder for the backtesting framework
        
        return metrics
'''
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Strategy Coding Agent specialized in converting trading strategy definitions into executable Python code.

Your responsibilities:
1. Convert strategy definitions into clean, executable Python code
2. Implement proper error handling and logging
3. Create modular, testable code structures
4. Ensure code follows best practices and is well-documented
5. Generate code that integrates with backtesting frameworks

When coding strategies, ensure:
- Clear separation of signal generation, entry/exit logic, and risk management
- Proper error handling and edge case management
- Efficient data processing and memory usage
- Comprehensive logging for debugging and monitoring
- Modular design for easy testing and modification
- Integration with common trading libraries (pandas, numpy, etc.)

Generate production-ready code that can be safely executed in backtesting and live trading environments."""
        )
        
        # Start background processing
        asyncio.create_task(self._start_coding_worker())
    
    async def _start_coding_worker(self):
        """Start background worker for strategy coding"""
        while self._is_active:
            try:
                # Get coding request from queue
                strategy_def = await asyncio.wait_for(
                    self._coding_queue.get(), timeout=10.0
                )
                
                await self._process_strategy_coding(strategy_def)
                self._coding_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in coding worker: {e}")
                await asyncio.sleep(5)
    
    async def _process_strategy_coding(self, strategy_def: StrategyDefinition):
        """Process strategy definition and generate code"""
        try:
            # Generate strategy code
            strategy_code = await self._generate_strategy_code(strategy_def)
            
            if strategy_code:
                # Create strategy code message
                code_msg = StrategyCode(
                    timestamp=datetime.now(),
                    source_agent=self.id.type,
                    message_id=f"code_{strategy_def.strategy_name}_{datetime.now().timestamp()}",
                    strategy_name=strategy_def.strategy_name,
                    strategy_type=strategy_def.strategy_type,
                    code_content=strategy_code["main_code"],
                    test_code=strategy_code.get("test_code", ""),
                    dependencies=strategy_code.get("dependencies", []),
                    performance_estimate=strategy_code.get("performance_estimate", {})
                )
                
                # Publish coded strategy
                await self._publish_strategy_code(code_msg)
            
        except Exception as e:
            self._logger.error(f"Error processing strategy coding: {e}")
    
    async def _generate_strategy_code(self, strategy_def: StrategyDefinition) -> Optional[Dict[str, Any]]:
        """Generate executable code for strategy definition"""
        try:
            # Prepare strategy information for LLM
            strategy_info = {
                "name": strategy_def.strategy_name,
                "type": strategy_def.strategy_type,
                "assets": strategy_def.target_assets,
                "entry_conditions": strategy_def.entry_conditions,
                "exit_conditions": strategy_def.exit_conditions,
                "risk_parameters": strategy_def.risk_parameters,
                "expected_return": strategy_def.expected_return,
                "max_drawdown": strategy_def.max_drawdown
            }
            
            # Generate code with LLM
            code_result = await self._generate_code_with_llm(strategy_info)
            
            if not code_result:
                return self._generate_basic_strategy_code(strategy_def)
            
            return code_result
            
        except Exception as e:
            self._logger.error(f"Error generating strategy code: {e}")
            return self._generate_basic_strategy_code(strategy_def)
    
    async def _generate_code_with_llm(self, strategy_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate strategy code using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Generate executable Python code for this trading strategy:

Strategy Information:
{json.dumps(strategy_info, indent=2)}

Requirements:
1. Create a complete Python class that implements the strategy
2. Include proper signal generation logic based on entry/exit conditions
3. Implement risk management with the specified parameters
4. Add comprehensive error handling and logging
5. Make the code modular and testable
6. Include docstrings and comments
7. Generate accompanying test code

Provide the response in JSON format with:
1. main_code: string (complete strategy class implementation)
2. test_code: string (unit tests for the strategy)
3. dependencies: list (required Python packages)
4. performance_estimate: dict (estimated performance characteristics)
5. usage_example: string (example of how to use the strategy)

Focus on creating production-ready, well-structured code.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    code_result = json.loads(response.content)
                    return code_result
                except json.JSONDecodeError:
                    # Try to extract code from response
                    return self._extract_code_from_response(response.content, strategy_info)
            
            return None
            
        except Exception as e:
            self._logger.error(f"Error in LLM code generation: {e}")
            return None
    
    def _extract_code_from_response(self, response: str, strategy_info: Dict[str, Any]) -> Dict[str, Any]:
        """Extract code from LLM response if JSON parsing fails"""
        # Simple extraction - look for Python code blocks
        import re
        
        code_blocks = re.findall(r'```python\n(.*?)\n```', response, re.DOTALL)
        
        if code_blocks:
            return {
                "main_code": code_blocks[0],
                "test_code": code_blocks[1] if len(code_blocks) > 1 else "",
                "dependencies": ["pandas", "numpy", "logging"],
                "performance_estimate": {},
                "usage_example": "# Usage example needed"
            }
        
        return None
    
    def _generate_basic_strategy_code(self, strategy_def: StrategyDefinition) -> Dict[str, Any]:
        """Generate basic strategy code as fallback"""
        class_name = f"{strategy_def.strategy_name.replace(' ', '').replace('-', '')}Strategy"
        
        # Generate basic signal generation code
        signal_code = """
        # Basic signal generation
        if len(market_data) < 20:
            return signals
            
        # Simple moving average crossover example
        short_ma = market_data['close'].rolling(5).mean()
        long_ma = market_data['close'].rolling(20).mean()
        
        for asset in self.config.get('target_assets', []):
            if asset in market_data.columns:
                current_price = market_data[asset].iloc[-1]
                short_signal = short_ma.iloc[-1] > long_ma.iloc[-1]
                
                if short_signal:
                    signals.append({
                        'asset': asset,
                        'type': 'buy',
                        'strength': 0.7,
                        'price': current_price,
                        'timestamp': datetime.now()
                    })
        """
        
        # Generate basic entry conditions
        entry_code = """
        # Basic entry conditions
        if len(data) < 20:
            return False
            
        # Check if we have sufficient data and conditions
        current_price = data['close'].iloc[-1]
        ma_20 = data['close'].rolling(20).mean().iloc[-1]
        
        # Simple condition: price above moving average
        return current_price > ma_20
        """
        
        # Generate basic exit conditions
        exit_code = """
        # Basic exit conditions
        if not position:
            return False
            
        current_price = data['close'].iloc[-1]
        entry_price = position.get('entry_price', current_price)
        
        # Stop loss and take profit
        pnl_pct = (current_price - entry_price) / entry_price
        
        if pnl_pct <= -self.stop_loss:
            return True  # Stop loss
        elif pnl_pct >= self.take_profit:
            return True  # Take profit
            
        return False
        """
        
        # Fill in the template
        main_code = self._base_strategy_template.format(
            strategy_class_name=class_name,
            strategy_description=f"Strategy: {strategy_def.strategy_name}",
            signal_generation_code=textwrap.indent(signal_code.strip(), "            "),
            entry_conditions_code=textwrap.indent(entry_code.strip(), "            "),
            exit_conditions_code=textwrap.indent(exit_code.strip(), "            ")
        )
        
        # Basic test code
        test_code = f"""
import unittest
import pandas as pd
import numpy as np
from datetime import datetime

class Test{class_name}(unittest.TestCase):
    def setUp(self):
        self.config = {{
            'position_size': 0.02,
            'stop_loss': 0.05,
            'take_profit': 0.15,
            'target_assets': ['BTC', 'ETH']
        }}
        self.strategy = {class_name}(self.config)
    
    def test_signal_generation(self):
        # Create sample data
        data = pd.DataFrame({{
            'close': np.random.randn(100).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 100)
        }})
        
        signals = self.strategy.generate_signals(data)
        self.assertIsInstance(signals, list)
    
    def test_entry_conditions(self):
        data = pd.DataFrame({{
            'close': [100, 101, 102, 103, 104]
        }})
        
        result = self.strategy.check_entry_conditions(data, 'BTC')
        self.assertIsInstance(result, bool)

if __name__ == '__main__':
    unittest.main()
        """
        
        return {
            "main_code": main_code,
            "test_code": test_code.strip(),
            "dependencies": ["pandas", "numpy", "logging"],
            "performance_estimate": {
                "expected_return": strategy_def.expected_return,
                "max_drawdown": strategy_def.max_drawdown,
                "sharpe_estimate": 1.2
            },
            "usage_example": f"""
# Example usage
config = {{
    'position_size': 0.02,
    'stop_loss': 0.05,
    'take_profit': 0.15,
    'target_assets': {strategy_def.target_assets}
}}

strategy = {class_name}(config)
results = strategy.execute_strategy(market_data)
"""
        }
    
    async def _publish_strategy_code(self, code_msg: StrategyCode):
        """Publish generated strategy code"""
        try:
            # Send to backtesting agent
            await self.publish_message(
                code_msg,
                topic_id=TopicId("StrategyBacktestingAgent", source=self.id.key)
            )
            
            # Send to execution layer for potential deployment
            await self.publish_message(
                code_msg,
                topic_id=TopicId("StrategyDeploymentAgent", source=self.id.key)
            )
            
            self._logger.info(f"Published strategy code: {code_msg.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error publishing strategy code: {e}")
    
    @message_handler
    async def handle_strategy_definition(self, message: StrategyDefinition, ctx: MessageContext) -> None:
        """Handle strategy definitions from generation agent"""
        await self._coding_queue.put(message)
        self._logger.debug(f"Queued strategy for coding: {message.strategy_name}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Strategy coding started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Strategy coding stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_queue":
            while not self._coding_queue.empty():
                try:
                    self._coding_queue.get_nowait()
                    self._coding_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            self._logger.info("Cleared coding queue")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "StrategyCodingAgent",
            "active": self._is_active,
            "queue_size": self._coding_queue.qsize(),
            "last_update": datetime.now().isoformat()
        }
