"""
Strategy Generation Agent

This agent generates quantitative trading strategies based on trend signals
and market analysis data from the analysis layer.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    TrendSignal,
    ContractAnalysisData,
    StrategyRequest,
    StrategyDefinition,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class StrategyGenerationAgent(RoutedAgent):
    """
    Agent responsible for generating quantitative trading strategies.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Strategy Generation Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Signal processing
        self._trend_signals = deque(maxlen=100)
        self._contract_analyses = deque(maxlen=50)
        self._strategy_queue = asyncio.Queue()
        
        # Configuration
        self._is_active = True
        self._min_signal_confidence = 0.7
        self._strategy_generation_interval = 600  # 10 minutes
        
        # Strategy templates and patterns
        self._strategy_templates = {
            "momentum": {
                "description": "Momentum-based strategy following trend signals",
                "entry_conditions": ["strong_bullish_signal", "volume_confirmation"],
                "exit_conditions": ["trend_reversal", "profit_target", "stop_loss"],
                "risk_management": ["position_sizing", "stop_loss", "take_profit"]
            },
            "mean_reversion": {
                "description": "Mean reversion strategy for oversold/overbought conditions",
                "entry_conditions": ["oversold_condition", "support_level"],
                "exit_conditions": ["mean_reversion", "resistance_level"],
                "risk_management": ["tight_stops", "quick_exits"]
            },
            "arbitrage": {
                "description": "Cross-exchange arbitrage opportunities",
                "entry_conditions": ["price_differential", "liquidity_check"],
                "exit_conditions": ["price_convergence", "time_limit"],
                "risk_management": ["execution_speed", "slippage_control"]
            },
            "meme_momentum": {
                "description": "Meme token momentum strategy with viral indicators",
                "entry_conditions": ["viral_trend", "social_momentum", "volume_spike"],
                "exit_conditions": ["viral_decline", "profit_target", "time_decay"],
                "risk_management": ["small_position", "quick_exits", "profit_taking"]
            }
        }
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Strategy Generation Agent specialized in creating quantitative cryptocurrency trading strategies.

Your responsibilities:
1. Analyze trend signals and market data to identify trading opportunities
2. Generate comprehensive trading strategies with clear entry/exit rules
3. Define risk management parameters and position sizing rules
4. Create backtestable strategy specifications
5. Optimize strategies for different market conditions

When generating strategies, consider:
- Signal strength and confidence levels
- Market volatility and liquidity
- Risk-reward ratios and drawdown limits
- Execution complexity and slippage
- Correlation with existing strategies
- Market regime and conditions

Provide detailed strategy specifications that can be coded and backtested."""
        )
        
        # Start background processing
        asyncio.create_task(self._start_strategy_generation_loop())
    
    async def _start_strategy_generation_loop(self):
        """Start the continuous strategy generation loop"""
        while self._is_active:
            try:
                await self._process_signals_and_generate_strategies()
                await asyncio.sleep(self._strategy_generation_interval)
            except Exception as e:
                self._logger.error(f"Error in strategy generation loop: {e}")
                await asyncio.sleep(60)
    
    async def _process_signals_and_generate_strategies(self):
        """Process accumulated signals and generate strategies"""
        try:
            # Get recent high-confidence signals
            recent_signals = self._get_recent_high_confidence_signals()
            
            if not recent_signals:
                return
            
            # Group signals by asset and signal type
            signal_groups = self._group_signals(recent_signals)
            
            # Generate strategies for each group
            for group_key, signals in signal_groups.items():
                strategy = await self._generate_strategy_for_signals(group_key, signals)
                if strategy:
                    await self._publish_strategy(strategy)
            
        except Exception as e:
            self._logger.error(f"Error processing signals: {e}")
    
    def _get_recent_high_confidence_signals(self) -> List[TrendSignal]:
        """Get recent signals above confidence threshold"""
        cutoff_time = datetime.now() - timedelta(hours=2)
        
        return [
            signal for signal in self._trend_signals
            if (signal.timestamp > cutoff_time and 
                signal.confidence_level >= self._min_signal_confidence)
        ]
    
    def _group_signals(self, signals: List[TrendSignal]) -> Dict[str, List[TrendSignal]]:
        """Group signals by asset and signal type"""
        groups = {}
        
        for signal in signals:
            for asset in signal.target_assets:
                key = f"{asset}_{signal.signal_type}_{signal.timeframe}"
                if key not in groups:
                    groups[key] = []
                groups[key].append(signal)
        
        return groups
    
    async def _generate_strategy_for_signals(self, group_key: str, signals: List[TrendSignal]) -> Optional[StrategyDefinition]:
        """Generate a strategy for a group of signals"""
        try:
            # Analyze signal group
            signal_analysis = self._analyze_signal_group(signals)
            
            # Select appropriate strategy template
            template_name = self._select_strategy_template(signal_analysis)
            template = self._strategy_templates[template_name]
            
            # Generate detailed strategy with LLM
            strategy_spec = await self._generate_strategy_with_llm(
                group_key, signals, template, signal_analysis
            )
            
            if not strategy_spec:
                return None
            
            # Create strategy definition
            strategy = StrategyDefinition(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"strategy_{group_key}_{datetime.now().timestamp()}",
                strategy_name=strategy_spec.get("name", f"Strategy_{group_key}"),
                strategy_type=template_name,
                target_assets=list(set(asset for signal in signals for asset in signal.target_assets)),
                entry_conditions=strategy_spec.get("entry_conditions", []),
                exit_conditions=strategy_spec.get("exit_conditions", []),
                risk_parameters=strategy_spec.get("risk_parameters", {}),
                expected_return=strategy_spec.get("expected_return", 0.0),
                max_drawdown=strategy_spec.get("max_drawdown", 0.2),
                strategy_code=strategy_spec.get("strategy_code", "")
            )
            
            return strategy
            
        except Exception as e:
            self._logger.error(f"Error generating strategy for {group_key}: {e}")
            return None
    
    def _analyze_signal_group(self, signals: List[TrendSignal]) -> Dict[str, Any]:
        """Analyze a group of signals"""
        if not signals:
            return {}
        
        # Calculate aggregate metrics
        avg_strength = sum(s.strength for s in signals) / len(signals)
        avg_confidence = sum(s.confidence_level for s in signals) / len(signals)
        
        # Determine dominant signal type
        signal_types = [s.signal_type for s in signals]
        dominant_type = max(set(signal_types), key=signal_types.count)
        
        # Determine timeframe distribution
        timeframes = [s.timeframe for s in signals]
        dominant_timeframe = max(set(timeframes), key=timeframes.count)
        
        return {
            "signal_count": len(signals),
            "avg_strength": avg_strength,
            "avg_confidence": avg_confidence,
            "dominant_type": dominant_type,
            "dominant_timeframe": dominant_timeframe,
            "assets": list(set(asset for signal in signals for asset in signal.target_assets))
        }
    
    def _select_strategy_template(self, signal_analysis: Dict[str, Any]) -> str:
        """Select appropriate strategy template based on signal analysis"""
        dominant_type = signal_analysis.get("dominant_type", "neutral")
        assets = signal_analysis.get("assets", [])
        
        # Check for meme tokens
        meme_indicators = ["pepe", "doge", "shib", "meme", "bonk"]
        is_meme_strategy = any(
            any(indicator in asset.lower() for indicator in meme_indicators)
            for asset in assets
        )
        
        if is_meme_strategy:
            return "meme_momentum"
        elif dominant_type in ["bullish", "bearish"]:
            return "momentum"
        else:
            return "mean_reversion"
    
    async def _generate_strategy_with_llm(
        self, 
        group_key: str, 
        signals: List[TrendSignal], 
        template: Dict[str, Any],
        analysis: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Generate detailed strategy using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Generate a detailed trading strategy based on these signals:

Group Key: {group_key}
Signal Analysis: {json.dumps(analysis, indent=2)}
Strategy Template: {json.dumps(template, indent=2)}

Signals Summary:
- Count: {len(signals)}
- Average Strength: {analysis.get('avg_strength', 0):.2f}
- Average Confidence: {analysis.get('avg_confidence', 0):.2f}
- Dominant Type: {analysis.get('dominant_type')}
- Target Assets: {analysis.get('assets')}

Provide strategy specification in JSON format with:
1. name: string (descriptive strategy name)
2. entry_conditions: list of specific entry conditions with parameters
3. exit_conditions: list of specific exit conditions with parameters
4. risk_parameters: dict with position_size, stop_loss, take_profit, max_exposure
5. expected_return: float (expected annual return)
6. max_drawdown: float (maximum acceptable drawdown)
7. strategy_code: string (Python pseudocode for the strategy logic)
8. backtesting_parameters: dict with lookback_period, rebalance_frequency
9. performance_metrics: list of metrics to track
10. market_conditions: list of suitable market conditions

Focus on creating a robust, backtestable strategy with clear risk management.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    strategy_spec = json.loads(response.content)
                    return strategy_spec
                except json.JSONDecodeError:
                    return self._basic_strategy_spec(group_key, analysis, template)
            
            return self._basic_strategy_spec(group_key, analysis, template)
            
        except Exception as e:
            self._logger.error(f"Error in LLM strategy generation: {e}")
            return self._basic_strategy_spec(group_key, analysis, template)
    
    def _basic_strategy_spec(self, group_key: str, analysis: Dict[str, Any], template: Dict[str, Any]) -> Dict[str, Any]:
        """Basic strategy specification fallback"""
        return {
            "name": f"Basic_{group_key}_Strategy",
            "entry_conditions": template.get("entry_conditions", []),
            "exit_conditions": template.get("exit_conditions", []),
            "risk_parameters": {
                "position_size": 0.02,  # 2% of portfolio
                "stop_loss": 0.05,      # 5% stop loss
                "take_profit": 0.15,    # 15% take profit
                "max_exposure": 0.1     # 10% max exposure per asset
            },
            "expected_return": 0.2,     # 20% annual return
            "max_drawdown": 0.15,       # 15% max drawdown
            "strategy_code": "# Basic strategy implementation needed",
            "backtesting_parameters": {
                "lookback_period": "6M",
                "rebalance_frequency": "daily"
            },
            "performance_metrics": ["sharpe_ratio", "max_drawdown", "win_rate"],
            "market_conditions": ["trending", "volatile"]
        }
    
    async def _publish_strategy(self, strategy: StrategyDefinition):
        """Publish generated strategy"""
        try:
            # Send to strategy coding agent
            await self.publish_message(
                strategy,
                topic_id=TopicId("StrategyCodingAgent", source=self.id.key)
            )
            
            # Send to backtesting agent
            await self.publish_message(
                strategy,
                topic_id=TopicId("StrategyBacktestingAgent", source=self.id.key)
            )
            
            self._logger.info(
                f"Published strategy: {strategy.strategy_name} "
                f"for assets {strategy.target_assets}"
            )
            
        except Exception as e:
            self._logger.error(f"Error publishing strategy: {e}")
    
    @message_handler
    async def handle_trend_signal(self, message: TrendSignal, ctx: MessageContext) -> None:
        """Handle trend signals from analysis layer"""
        self._trend_signals.append(message)
        self._logger.debug(f"Received trend signal: {message.signal_type} for {message.target_assets}")
    
    @message_handler
    async def handle_contract_analysis(self, message: ContractAnalysisData, ctx: MessageContext) -> None:
        """Handle contract analysis data"""
        self._contract_analyses.append(message)
        self._logger.debug(f"Received contract analysis for {message.contract_address}")
    
    @message_handler
    async def handle_strategy_request(self, message: StrategyRequest, ctx: MessageContext) -> None:
        """Handle manual strategy generation requests"""
        try:
            # Generate strategy based on request
            strategy_spec = await self._generate_custom_strategy(message)
            if strategy_spec:
                await self._publish_strategy(strategy_spec)
        except Exception as e:
            self._logger.error(f"Error handling strategy request: {e}")
    
    async def _generate_custom_strategy(self, request: StrategyRequest) -> Optional[StrategyDefinition]:
        """Generate custom strategy based on request"""
        # Implementation for custom strategy generation
        # This would be similar to the signal-based generation but with custom parameters
        return None
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Strategy generation started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Strategy generation stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_signals":
            self._trend_signals.clear()
            self._contract_analyses.clear()
            self._logger.info("Cleared signal buffers")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "strategy_generation":
            updates = message.updates
            
            if "min_signal_confidence" in updates:
                self._min_signal_confidence = updates["min_signal_confidence"]
                self._logger.info(f"Updated min signal confidence to {self._min_signal_confidence}")
            
            if "strategy_generation_interval" in updates:
                self._strategy_generation_interval = updates["strategy_generation_interval"]
                self._logger.info(f"Updated generation interval to {self._strategy_generation_interval} seconds")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "StrategyGenerationAgent",
            "active": self._is_active,
            "trend_signals_count": len(self._trend_signals),
            "contract_analyses_count": len(self._contract_analyses),
            "min_signal_confidence": self._min_signal_confidence,
            "generation_interval": self._strategy_generation_interval,
            "strategy_templates": list(self._strategy_templates.keys()),
            "last_update": datetime.now().isoformat()
        }
