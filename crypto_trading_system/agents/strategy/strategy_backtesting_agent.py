"""
Strategy Backtesting Agent

This agent performs comprehensive backtesting of trading strategies
using historical data and generates performance reports.
"""

import asyncio
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque
import importlib.util
import sys
import tempfile
import os

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    StrategyDefinition,
    StrategyCode,
    BacktestResults,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class StrategyBacktestingAgent(RoutedAgent):
    """
    Agent responsible for backtesting trading strategies and generating performance reports.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Strategy Backtesting Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._backtest_queue = asyncio.Queue()
        self._max_concurrent_backtests = 2
        
        # Backtesting parameters
        self._default_backtest_config = {
            "initial_capital": 10000,
            "commission": 0.001,  # 0.1% commission
            "slippage": 0.0005,   # 0.05% slippage
            "lookback_period": "1Y",
            "rebalance_frequency": "1D",
            "benchmark": "BTC"
        }
        
        # Performance metrics to calculate
        self._performance_metrics = [
            "total_return", "annual_return", "sharpe_ratio", "sortino_ratio",
            "max_drawdown", "calmar_ratio", "win_rate", "profit_factor",
            "avg_trade_duration", "volatility", "beta", "alpha"
        ]
        
        # Historical data cache (in production, this would connect to a data provider)
        self._data_cache = {}
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Strategy Backtesting Agent specialized in analyzing trading strategy performance and generating comprehensive reports.

Your responsibilities:
1. Execute backtests on trading strategies using historical data
2. Calculate comprehensive performance metrics and risk statistics
3. Generate detailed performance reports with insights
4. Identify strategy strengths, weaknesses, and optimization opportunities
5. Provide recommendations for strategy improvements

When analyzing backtest results, consider:
- Risk-adjusted returns (Sharpe, Sortino, Calmar ratios)
- Drawdown analysis and recovery periods
- Trade distribution and consistency
- Market regime performance
- Correlation with benchmarks
- Statistical significance of results

Provide actionable insights and recommendations for strategy optimization."""
        )
        
        # Start background workers
        asyncio.create_task(self._start_backtest_workers())
    
    async def _start_backtest_workers(self):
        """Start background workers for backtesting"""
        workers = []
        for i in range(self._max_concurrent_backtests):
            worker = asyncio.create_task(self._backtest_worker(f"worker_{i}"))
            workers.append(worker)
        
        await asyncio.gather(*workers, return_exceptions=True)
    
    async def _backtest_worker(self, worker_id: str):
        """Background worker for processing backtest requests"""
        while self._is_active:
            try:
                # Get backtest request from queue
                backtest_request = await asyncio.wait_for(
                    self._backtest_queue.get(), timeout=10.0
                )
                
                await self._process_backtest(backtest_request)
                self._backtest_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in backtest worker {worker_id}: {e}")
                await asyncio.sleep(5)
    
    async def _process_backtest(self, request: Dict[str, Any]):
        """Process a backtest request"""
        try:
            strategy_code = request["strategy_code"]
            backtest_config = request.get("config", self._default_backtest_config)
            
            # Execute backtest
            results = await self._execute_backtest(strategy_code, backtest_config)
            
            if results:
                # Analyze results with LLM
                analysis = await self._analyze_backtest_results(results, strategy_code)
                
                # Create backtest results message
                backtest_msg = BacktestResults(
                    timestamp=datetime.now(),
                    source_agent=self.id.type,
                    message_id=f"backtest_{strategy_code.strategy_name}_{datetime.now().timestamp()}",
                    strategy_name=strategy_code.strategy_name,
                    backtest_period=backtest_config.get("lookback_period", "1Y"),
                    performance_metrics=results["metrics"],
                    trade_summary=results["trade_summary"],
                    analysis_report=analysis.get("report", ""),
                    recommendations=analysis.get("recommendations", [])
                )
                
                # Publish results
                await self._publish_backtest_results(backtest_msg)
            
        except Exception as e:
            self._logger.error(f"Error processing backtest: {e}")
    
    async def _execute_backtest(self, strategy_code: StrategyCode, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Execute backtest for a strategy"""
        try:
            # Create temporary file for strategy code
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(strategy_code.code_content)
                temp_file = f.name
            
            try:
                # Load strategy module
                spec = importlib.util.spec_from_file_location("strategy_module", temp_file)
                strategy_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(strategy_module)
                
                # Find strategy class
                strategy_class = None
                for name in dir(strategy_module):
                    obj = getattr(strategy_module, name)
                    if (isinstance(obj, type) and 
                        hasattr(obj, 'execute_strategy') and 
                        name != 'object'):
                        strategy_class = obj
                        break
                
                if not strategy_class:
                    self._logger.error("No strategy class found in code")
                    return None
                
                # Initialize strategy
                strategy_instance = strategy_class(config)
                
                # Get historical data
                historical_data = await self._get_historical_data(
                    strategy_code.strategy_name,
                    config.get("lookback_period", "1Y")
                )
                
                if historical_data is None or historical_data.empty:
                    self._logger.error("No historical data available")
                    return self._generate_mock_backtest_results(strategy_code, config)
                
                # Execute backtest
                backtest_results = self._run_backtest_simulation(
                    strategy_instance, historical_data, config
                )
                
                return backtest_results
                
            finally:
                # Clean up temporary file
                os.unlink(temp_file)
            
        except Exception as e:
            self._logger.error(f"Error executing backtest: {e}")
            return self._generate_mock_backtest_results(strategy_code, config)
    
    async def _get_historical_data(self, strategy_name: str, period: str) -> Optional[pd.DataFrame]:
        """Get historical market data for backtesting"""
        try:
            # In production, this would fetch from a data provider
            # For now, generate synthetic data
            
            cache_key = f"{strategy_name}_{period}"
            if cache_key in self._data_cache:
                return self._data_cache[cache_key]
            
            # Generate synthetic historical data
            days = {"1M": 30, "3M": 90, "6M": 180, "1Y": 365, "2Y": 730}.get(period, 365)
            
            dates = pd.date_range(
                start=datetime.now() - timedelta(days=days),
                end=datetime.now(),
                freq='1H'
            )
            
            # Generate realistic price data
            np.random.seed(42)  # For reproducible results
            returns = np.random.normal(0.0001, 0.02, len(dates))  # Hourly returns
            prices = 50000 * np.exp(np.cumsum(returns))  # Starting at $50k
            
            data = pd.DataFrame({
                'timestamp': dates,
                'open': prices * (1 + np.random.normal(0, 0.001, len(dates))),
                'high': prices * (1 + np.abs(np.random.normal(0, 0.005, len(dates)))),
                'low': prices * (1 - np.abs(np.random.normal(0, 0.005, len(dates)))),
                'close': prices,
                'volume': np.random.lognormal(10, 1, len(dates))
            })
            
            self._data_cache[cache_key] = data
            return data
            
        except Exception as e:
            self._logger.error(f"Error getting historical data: {e}")
            return None
    
    def _run_backtest_simulation(self, strategy, data: pd.DataFrame, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run backtest simulation"""
        try:
            initial_capital = config.get("initial_capital", 10000)
            commission = config.get("commission", 0.001)
            
            # Initialize tracking variables
            portfolio_value = [initial_capital]
            positions = {}
            trades = []
            cash = initial_capital
            
            # Run simulation
            for i in range(1, len(data)):
                current_data = data.iloc[:i+1]
                
                try:
                    # Execute strategy
                    strategy_result = strategy.execute_strategy(current_data)
                    
                    # Process trades
                    for trade in strategy_result.get("trades", []):
                        trade_result = self._execute_trade(
                            trade, data.iloc[i], cash, commission
                        )
                        
                        if trade_result["executed"]:
                            trades.append(trade_result)
                            cash = trade_result["remaining_cash"]
                            
                            # Update positions
                            asset = trade["asset"]
                            if trade["type"] == "buy":
                                positions[asset] = positions.get(asset, 0) + trade["size"]
                            else:
                                positions[asset] = positions.get(asset, 0) - trade["size"]
                    
                    # Calculate portfolio value
                    current_price = data.iloc[i]["close"]
                    position_value = sum(
                        pos_size * current_price for pos_size in positions.values()
                    )
                    total_value = cash + position_value
                    portfolio_value.append(total_value)
                    
                except Exception as e:
                    # If strategy execution fails, maintain current portfolio
                    portfolio_value.append(portfolio_value[-1])
            
            # Calculate performance metrics
            metrics = self._calculate_performance_metrics(
                portfolio_value, trades, initial_capital
            )
            
            # Generate trade summary
            trade_summary = self._generate_trade_summary(trades)
            
            return {
                "portfolio_values": portfolio_value,
                "trades": trades,
                "metrics": metrics,
                "trade_summary": trade_summary,
                "final_value": portfolio_value[-1] if portfolio_value else initial_capital
            }
            
        except Exception as e:
            self._logger.error(f"Error in backtest simulation: {e}")
            return self._generate_mock_backtest_results(None, config)
    
    def _execute_trade(self, trade: Dict[str, Any], market_data: pd.Series, cash: float, commission: float) -> Dict[str, Any]:
        """Execute a single trade"""
        try:
            price = market_data["close"]
            trade_value = abs(trade["size"]) * price
            commission_cost = trade_value * commission
            
            if trade["type"] == "buy":
                total_cost = trade_value + commission_cost
                if cash >= total_cost:
                    return {
                        "executed": True,
                        "asset": trade["asset"],
                        "type": "buy",
                        "size": trade["size"],
                        "price": price,
                        "commission": commission_cost,
                        "remaining_cash": cash - total_cost,
                        "timestamp": market_data["timestamp"] if "timestamp" in market_data else datetime.now()
                    }
            else:  # sell
                return {
                    "executed": True,
                    "asset": trade["asset"],
                    "type": "sell",
                    "size": trade["size"],
                    "price": price,
                    "commission": commission_cost,
                    "remaining_cash": cash + trade_value - commission_cost,
                    "timestamp": market_data["timestamp"] if "timestamp" in market_data else datetime.now()
                }
            
            return {"executed": False, "reason": "insufficient_funds"}
            
        except Exception as e:
            self._logger.error(f"Error executing trade: {e}")
            return {"executed": False, "reason": "execution_error"}
    
    def _calculate_performance_metrics(self, portfolio_values: List[float], trades: List[Dict], initial_capital: float) -> Dict[str, float]:
        """Calculate comprehensive performance metrics"""
        try:
            if len(portfolio_values) < 2:
                return {}
            
            # Convert to numpy array for calculations
            values = np.array(portfolio_values)
            returns = np.diff(values) / values[:-1]
            
            # Basic metrics
            total_return = (values[-1] - initial_capital) / initial_capital
            annual_return = (values[-1] / initial_capital) ** (365 / len(values)) - 1
            
            # Risk metrics
            volatility = np.std(returns) * np.sqrt(365 * 24)  # Annualized (hourly data)
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # Drawdown calculation
            peak = np.maximum.accumulate(values)
            drawdown = (values - peak) / peak
            max_drawdown = np.min(drawdown)
            
            # Trade-based metrics
            winning_trades = [t for t in trades if self._calculate_trade_pnl(t) > 0]
            win_rate = len(winning_trades) / len(trades) if trades else 0
            
            return {
                "total_return": float(total_return),
                "annual_return": float(annual_return),
                "sharpe_ratio": float(sharpe_ratio),
                "max_drawdown": float(max_drawdown),
                "volatility": float(volatility),
                "win_rate": float(win_rate),
                "total_trades": len(trades),
                "final_value": float(values[-1])
            }
            
        except Exception as e:
            self._logger.error(f"Error calculating metrics: {e}")
            return {"error": "calculation_failed"}
    
    def _calculate_trade_pnl(self, trade: Dict[str, Any]) -> float:
        """Calculate P&L for a trade (simplified)"""
        # This is a simplified calculation
        # In practice, you'd need to track entry/exit prices
        return 0.0
    
    def _generate_trade_summary(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary of trades"""
        if not trades:
            return {"total_trades": 0}
        
        buy_trades = [t for t in trades if t.get("type") == "buy"]
        sell_trades = [t for t in trades if t.get("type") == "sell"]
        
        return {
            "total_trades": len(trades),
            "buy_trades": len(buy_trades),
            "sell_trades": len(sell_trades),
            "avg_trade_size": np.mean([t.get("size", 0) for t in trades]),
            "total_commission": sum(t.get("commission", 0) for t in trades)
        }
    
    def _generate_mock_backtest_results(self, strategy_code: Optional[StrategyCode], config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate mock backtest results for testing"""
        initial_capital = config.get("initial_capital", 10000)
        
        # Generate mock performance
        mock_return = np.random.normal(0.15, 0.3)  # 15% average return with 30% volatility
        final_value = initial_capital * (1 + mock_return)
        
        return {
            "portfolio_values": [initial_capital, final_value],
            "trades": [],
            "metrics": {
                "total_return": mock_return,
                "annual_return": mock_return,
                "sharpe_ratio": np.random.uniform(0.5, 2.0),
                "max_drawdown": -np.random.uniform(0.05, 0.25),
                "volatility": np.random.uniform(0.2, 0.5),
                "win_rate": np.random.uniform(0.4, 0.7),
                "total_trades": np.random.randint(10, 100),
                "final_value": final_value
            },
            "trade_summary": {
                "total_trades": np.random.randint(10, 100),
                "buy_trades": np.random.randint(5, 50),
                "sell_trades": np.random.randint(5, 50),
                "avg_trade_size": np.random.uniform(0.01, 0.1),
                "total_commission": np.random.uniform(10, 100)
            },
            "final_value": final_value
        }

    async def _analyze_backtest_results(self, results: Dict[str, Any], strategy_code: StrategyCode) -> Dict[str, Any]:
        """Analyze backtest results using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Analyze these backtest results and provide insights:

Strategy: {strategy_code.strategy_name}
Strategy Type: {strategy_code.strategy_type}

Performance Metrics:
{json.dumps(results['metrics'], indent=2)}

Trade Summary:
{json.dumps(results['trade_summary'], indent=2)}

Final Portfolio Value: ${results['final_value']:,.2f}

Provide analysis in JSON format with:
1. report: string (comprehensive performance analysis)
2. recommendations: list (specific improvement recommendations)
3. risk_assessment: string (risk evaluation)
4. market_suitability: list (suitable market conditions)
5. optimization_suggestions: list (parameter optimization ideas)
6. overall_rating: string (poor/fair/good/excellent)

Focus on actionable insights for strategy improvement.""",
                source="system"
            )

            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )

            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_analysis(results)

            return self._basic_analysis(results)

        except Exception as e:
            self._logger.error(f"Error in backtest analysis: {e}")
            return self._basic_analysis(results)

    def _basic_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Basic analysis fallback"""
        metrics = results.get("metrics", {})
        total_return = metrics.get("total_return", 0)
        sharpe_ratio = metrics.get("sharpe_ratio", 0)
        max_drawdown = metrics.get("max_drawdown", 0)

        # Simple rating logic
        if total_return > 0.2 and sharpe_ratio > 1.5 and max_drawdown > -0.15:
            rating = "excellent"
        elif total_return > 0.1 and sharpe_ratio > 1.0 and max_drawdown > -0.25:
            rating = "good"
        elif total_return > 0.05 and sharpe_ratio > 0.5:
            rating = "fair"
        else:
            rating = "poor"

        return {
            "report": f"Strategy achieved {total_return:.1%} return with {sharpe_ratio:.2f} Sharpe ratio and {max_drawdown:.1%} max drawdown.",
            "recommendations": ["Consider optimizing entry/exit conditions", "Review risk management parameters"],
            "risk_assessment": f"Maximum drawdown of {max_drawdown:.1%} indicates {'high' if max_drawdown < -0.2 else 'moderate'} risk",
            "market_suitability": ["trending markets", "volatile conditions"],
            "optimization_suggestions": ["Adjust position sizing", "Fine-tune stop losses"],
            "overall_rating": rating
        }

    async def _publish_backtest_results(self, results: BacktestResults):
        """Publish backtest results"""
        try:
            # Send to execution layer for deployment consideration
            await self.publish_message(
                results,
                topic_id=TopicId("StrategyDeploymentAgent", source=self.id.key)
            )

            # Send to control layer for notifications
            await self.publish_message(
                results,
                topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
            )

            self._logger.info(
                f"Published backtest results for {results.strategy_name}: "
                f"{results.performance_metrics.get('total_return', 0):.1%} return"
            )

        except Exception as e:
            self._logger.error(f"Error publishing backtest results: {e}")

    @message_handler
    async def handle_strategy_definition(self, message: StrategyDefinition, ctx: MessageContext) -> None:
        """Handle strategy definitions for backtesting"""
        # Create a basic strategy code for backtesting
        basic_code = StrategyCode(
            timestamp=datetime.now(),
            source_agent="StrategyGenerationAgent",
            message_id=f"code_{message.strategy_name}_{datetime.now().timestamp()}",
            strategy_name=message.strategy_name,
            strategy_type=message.strategy_type,
            code_content="# Basic strategy implementation",
            test_code="",
            dependencies=[],
            performance_estimate={}
        )

        await self._backtest_queue.put({
            "strategy_code": basic_code,
            "config": self._default_backtest_config
        })

        self._logger.debug(f"Queued strategy for backtesting: {message.strategy_name}")

    @message_handler
    async def handle_strategy_code(self, message: StrategyCode, ctx: MessageContext) -> None:
        """Handle strategy code for backtesting"""
        await self._backtest_queue.put({
            "strategy_code": message,
            "config": self._default_backtest_config
        })

        self._logger.debug(f"Queued coded strategy for backtesting: {message.strategy_name}")

    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command

        if command == "start":
            self._is_active = True
            self._logger.info("Strategy backtesting started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Strategy backtesting stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_queue":
            while not self._backtest_queue.empty():
                try:
                    self._backtest_queue.get_nowait()
                    self._backtest_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            self._logger.info("Cleared backtest queue")
        elif command == "clear_cache":
            self._data_cache.clear()
            self._logger.info("Cleared data cache")

    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "backtesting":
            updates = message.updates

            if "max_concurrent_backtests" in updates:
                self._max_concurrent_backtests = updates["max_concurrent_backtests"]
                self._logger.info(f"Updated max concurrent backtests to {self._max_concurrent_backtests}")

            if "default_backtest_config" in updates:
                self._default_backtest_config.update(updates["default_backtest_config"])
                self._logger.info("Updated default backtest configuration")

    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "StrategyBacktestingAgent",
            "active": self._is_active,
            "queue_size": self._backtest_queue.qsize(),
            "max_concurrent_backtests": self._max_concurrent_backtests,
            "cached_datasets": len(self._data_cache),
            "default_config": self._default_backtest_config,
            "last_update": datetime.now().isoformat()
        }
