"""
Trend Aggregation Agent

This agent aggregates intelligence from multiple sources and generates
comprehensive trend signals for the strategy generation layer.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import defaultdict, deque

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    WebIntelligenceData,
    WhaleActivityData,
    TVLData,
    MemeTokenTrend,
    TrendSignal,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class TrendAggregationAgent(RoutedAgent):
    """
    Agent responsible for aggregating intelligence data and generating trend signals.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Trend Aggregation Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Data storage for aggregation
        self._web_intelligence_buffer = deque(maxlen=100)
        self._whale_activity_buffer = deque(maxlen=50)
        self._tvl_data_buffer = deque(maxlen=50)
        self._meme_trend_buffer = deque(maxlen=100)
        
        # Aggregation configuration
        self._aggregation_interval = 300  # 5 minutes
        self._signal_threshold = 0.6  # Minimum confidence for signal generation
        self._is_active = True
        
        # Token-specific data aggregation
        self._token_data = defaultdict(lambda: {
            "web_sentiment": [],
            "whale_activity": [],
            "tvl_changes": [],
            "meme_trends": [],
            "last_signal": None,
            "signal_history": deque(maxlen=10)
        })
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Trend Aggregation Agent specialized in synthesizing multiple data sources to generate comprehensive cryptocurrency trading signals.

Your responsibilities:
1. Aggregate intelligence from web sentiment, whale activity, TVL changes, and meme trends
2. Identify correlations and patterns across different data sources
3. Generate high-confidence trading signals with clear rationale
4. Assess signal strength and provide confidence scores
5. Determine optimal timeframes for trading opportunities

When analyzing aggregated data, consider:
- Convergence of signals across multiple sources
- Timing and sequence of events
- Market context and external factors
- Historical patterns and correlations
- Risk-reward ratios and position sizing implications

Provide structured analysis with actionable trading signals and clear reasoning."""
        )
        
        # Start background aggregation
        asyncio.create_task(self._start_aggregation_loop())
    
    async def _start_aggregation_loop(self):
        """Start the continuous trend aggregation loop"""
        while self._is_active:
            try:
                await self._aggregate_and_generate_signals()
                await asyncio.sleep(self._aggregation_interval)
            except Exception as e:
                self._logger.error(f"Error in aggregation loop: {e}")
                await asyncio.sleep(60)
    
    async def _aggregate_and_generate_signals(self):
        """Aggregate data and generate trend signals"""
        try:
            # Get tokens with recent activity
            active_tokens = self._get_active_tokens()
            
            for token in active_tokens:
                signal = await self._generate_token_signal(token)
                if signal and signal.confidence_level >= self._signal_threshold:
                    await self._publish_trend_signal(signal)
            
        except Exception as e:
            self._logger.error(f"Error in signal generation: {e}")
    
    def _get_active_tokens(self) -> List[str]:
        """Get tokens with recent activity across data sources"""
        active_tokens = set()
        
        # From recent data buffers
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        # Web intelligence tokens
        for data in self._web_intelligence_buffer:
            if data.timestamp > cutoff_time:
                active_tokens.update(data.keywords)
        
        # Whale activity tokens
        for data in self._whale_activity_buffer:
            if data.timestamp > cutoff_time:
                active_tokens.add(data.token_symbol)
        
        # Meme trend tokens
        for data in self._meme_trend_buffer:
            if data.timestamp > cutoff_time:
                active_tokens.update(data.token_symbols)
        
        return list(active_tokens)[:20]  # Limit to top 20 active tokens
    
    async def _generate_token_signal(self, token: str) -> Optional[TrendSignal]:
        """Generate trend signal for a specific token"""
        try:
            # Collect recent data for the token
            token_intelligence = self._collect_token_intelligence(token)
            
            if not token_intelligence:
                return None
            
            # Analyze with LLM
            analysis = await self._analyze_token_trends(token, token_intelligence)
            
            if analysis.get("confidence_score", 0) < self._signal_threshold:
                return None
            
            # Create trend signal
            signal = TrendSignal(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"signal_{token}_{datetime.now().timestamp()}",
                signal_type=analysis.get("signal_type", "neutral"),
                strength=analysis.get("strength", 0.5),
                timeframe=analysis.get("timeframe", "medium"),
                supporting_data=analysis.get("supporting_data", []),
                confidence_level=analysis.get("confidence_score", 0.5),
                target_assets=[token]
            )
            
            # Update token signal history
            self._token_data[token]["last_signal"] = signal
            self._token_data[token]["signal_history"].append(signal)
            
            return signal
            
        except Exception as e:
            self._logger.error(f"Error generating signal for {token}: {e}")
            return None
    
    def _collect_token_intelligence(self, token: str) -> Dict[str, Any]:
        """Collect all intelligence data for a specific token"""
        cutoff_time = datetime.now() - timedelta(hours=2)
        intelligence = {
            "web_sentiment": [],
            "whale_activity": [],
            "tvl_changes": [],
            "meme_trends": []
        }
        
        # Web intelligence
        for data in self._web_intelligence_buffer:
            if (data.timestamp > cutoff_time and 
                token.lower() in [k.lower() for k in data.keywords]):
                intelligence["web_sentiment"].append({
                    "sentiment_score": data.sentiment_score,
                    "confidence": data.confidence_score,
                    "platform": data.platform,
                    "timestamp": data.timestamp
                })
        
        # Whale activity
        for data in self._whale_activity_buffer:
            if (data.timestamp > cutoff_time and 
                data.token_symbol.lower() == token.lower()):
                intelligence["whale_activity"].append({
                    "transaction_type": data.transaction_type,
                    "usd_value": data.usd_value,
                    "timestamp": data.timestamp
                })
        
        # TVL changes
        for data in self._tvl_data_buffer:
            if (data.timestamp > cutoff_time and 
                token.lower() in data.protocol_name.lower()):
                intelligence["tvl_changes"].append({
                    "tvl_change_24h": data.tvl_change_24h,
                    "current_tvl": data.current_tvl,
                    "protocol": data.protocol_name,
                    "timestamp": data.timestamp
                })
        
        # Meme trends
        for data in self._meme_trend_buffer:
            if (data.timestamp > cutoff_time and 
                token.upper() in [s.upper() for s in data.token_symbols]):
                intelligence["meme_trends"].append({
                    "viral_score": data.viral_score,
                    "sentiment": data.sentiment,
                    "platform": data.platform,
                    "timestamp": data.timestamp
                })
        
        return intelligence
    
    async def _analyze_token_trends(self, token: str, intelligence: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze token trends using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Analyze the aggregated intelligence data for {token} and generate a trading signal:

Web Sentiment Data: {len(intelligence['web_sentiment'])} entries
- Average sentiment: {self._calculate_average_sentiment(intelligence['web_sentiment']):.2f}

Whale Activity Data: {len(intelligence['whale_activity'])} entries
- Total whale volume: ${sum(w['usd_value'] for w in intelligence['whale_activity']):,.2f}

TVL Changes: {len(intelligence['tvl_changes'])} entries
- Average TVL change: {self._calculate_average_tvl_change(intelligence['tvl_changes']):.2%}

Meme Trends: {len(intelligence['meme_trends'])} entries
- Average viral score: {self._calculate_average_viral_score(intelligence['meme_trends']):.2f}

Provide analysis in JSON format with:
1. signal_type: string (bullish/bearish/neutral)
2. strength: float (0.0 to 1.0, signal strength)
3. timeframe: string (short/medium/long term opportunity)
4. confidence_score: float (0.0 to 1.0, confidence in signal)
5. supporting_data: list of key supporting evidence
6. risk_factors: list of potential risks
7. entry_strategy: string (recommended entry approach)
8. exit_strategy: string (recommended exit approach)
9. position_size: string (small/medium/large recommended position)
10. key_insights: list of key insights from the analysis

Focus on actionable trading signals with clear risk-reward ratios.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_trend_analysis(intelligence)
            
            return self._basic_trend_analysis(intelligence)
            
        except Exception as e:
            self._logger.error(f"Error in trend analysis: {e}")
            return self._basic_trend_analysis(intelligence)
    
    def _calculate_average_sentiment(self, sentiment_data: List[Dict[str, Any]]) -> float:
        """Calculate average sentiment score"""
        if not sentiment_data:
            return 0.0
        return sum(d["sentiment_score"] for d in sentiment_data) / len(sentiment_data)
    
    def _calculate_average_tvl_change(self, tvl_data: List[Dict[str, Any]]) -> float:
        """Calculate average TVL change"""
        if not tvl_data:
            return 0.0
        return sum(d["tvl_change_24h"] for d in tvl_data) / len(tvl_data)
    
    def _calculate_average_viral_score(self, meme_data: List[Dict[str, Any]]) -> float:
        """Calculate average viral score"""
        if not meme_data:
            return 0.0
        return sum(d["viral_score"] for d in meme_data) / len(meme_data)
    
    def _basic_trend_analysis(self, intelligence: Dict[str, Any]) -> Dict[str, Any]:
        """Basic trend analysis fallback"""
        # Simple scoring based on data availability and basic metrics
        score = 0.0
        signal_type = "neutral"
        
        # Web sentiment contribution
        avg_sentiment = self._calculate_average_sentiment(intelligence["web_sentiment"])
        if avg_sentiment > 0.3:
            score += 0.3
            signal_type = "bullish"
        elif avg_sentiment < -0.3:
            score += 0.3
            signal_type = "bearish"
        
        # Whale activity contribution
        if intelligence["whale_activity"]:
            score += 0.2
        
        # TVL changes contribution
        avg_tvl_change = self._calculate_average_tvl_change(intelligence["tvl_changes"])
        if abs(avg_tvl_change) > 0.1:  # >10% change
            score += 0.2
        
        # Meme trends contribution
        if intelligence["meme_trends"]:
            score += 0.3
        
        return {
            "signal_type": signal_type,
            "strength": min(score, 1.0),
            "timeframe": "medium",
            "confidence_score": min(score * 0.7, 0.8),  # Conservative confidence
            "supporting_data": ["Basic aggregation analysis"],
            "risk_factors": ["Limited analysis depth"],
            "entry_strategy": "gradual",
            "exit_strategy": "trailing_stop",
            "position_size": "small",
            "key_insights": [f"Aggregated {sum(len(v) for v in intelligence.values())} data points"]
        }
    
    async def _publish_trend_signal(self, signal: TrendSignal):
        """Publish trend signal to strategy generation layer"""
        try:
            # Publish to strategy generation topic
            await self.publish_message(
                signal,
                topic_id=TopicId("StrategyGenerationAgent", source=self.id.key)
            )
            
            # If high confidence signal, also notify control systems
            if signal.confidence_level > 0.8:
                await self.publish_message(
                    signal,
                    topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
                )
            
            self._logger.info(
                f"Published trend signal for {signal.target_assets}: "
                f"{signal.signal_type} (confidence: {signal.confidence_level:.2f})"
            )
            
        except Exception as e:
            self._logger.error(f"Error publishing trend signal: {e}")
    
    @message_handler
    async def handle_web_intelligence(self, message: WebIntelligenceData, ctx: MessageContext) -> None:
        """Handle web intelligence data"""
        self._web_intelligence_buffer.append(message)
        self._logger.debug(f"Received web intelligence from {message.platform}")
    
    @message_handler
    async def handle_whale_activity(self, message: WhaleActivityData, ctx: MessageContext) -> None:
        """Handle whale activity data"""
        self._whale_activity_buffer.append(message)
        self._logger.debug(f"Received whale activity for {message.token_symbol}")
    
    @message_handler
    async def handle_tvl_data(self, message: TVLData, ctx: MessageContext) -> None:
        """Handle TVL data"""
        self._tvl_data_buffer.append(message)
        self._logger.debug(f"Received TVL data for {message.protocol_name}")
    
    @message_handler
    async def handle_meme_trend(self, message: MemeTokenTrend, ctx: MessageContext) -> None:
        """Handle meme trend data"""
        self._meme_trend_buffer.append(message)
        self._logger.debug(f"Received meme trend for {message.token_symbols}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Trend aggregation started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Trend aggregation stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_buffers":
            self._clear_all_buffers()
            self._logger.info("Cleared all data buffers")
    
    def _clear_all_buffers(self):
        """Clear all data buffers"""
        self._web_intelligence_buffer.clear()
        self._whale_activity_buffer.clear()
        self._tvl_data_buffer.clear()
        self._meme_trend_buffer.clear()
        self._token_data.clear()
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "trend_aggregation":
            updates = message.updates
            
            if "aggregation_interval" in updates:
                self._aggregation_interval = updates["aggregation_interval"]
                self._logger.info(f"Updated aggregation interval to {self._aggregation_interval} seconds")
            
            if "signal_threshold" in updates:
                self._signal_threshold = updates["signal_threshold"]
                self._logger.info(f"Updated signal threshold to {self._signal_threshold}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "TrendAggregationAgent",
            "active": self._is_active,
            "aggregation_interval": self._aggregation_interval,
            "signal_threshold": self._signal_threshold,
            "buffer_sizes": {
                "web_intelligence": len(self._web_intelligence_buffer),
                "whale_activity": len(self._whale_activity_buffer),
                "tvl_data": len(self._tvl_data_buffer),
                "meme_trends": len(self._meme_trend_buffer)
            },
            "tracked_tokens": len(self._token_data),
            "last_update": datetime.now().isoformat()
        }
