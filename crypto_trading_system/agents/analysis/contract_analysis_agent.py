"""
Contract Analysis Agent

This agent analyzes smart contracts for security, functionality, and trading opportunities.
It evaluates contract code, tokenomics, and deployment patterns.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import re

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    ContractAnalysisResult,
    TrendSignal,
    SystemCommand,
    ConfigUpdate
)
from ...core.tools import OnChainDataTool


@default_subscription
class ContractAnalysisAgent(RoutedAgent):
    """
    Agent responsible for analyzing smart contracts and their trading implications.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Contract Analysis Agent",
        etherscan_api_key: Optional[str] = None
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize on-chain data tool
        self._onchain_tool = None
        if etherscan_api_key:
            self._onchain_tool = OnChainDataTool(etherscan_api_key)
        
        # Agent configuration
        self._analysis_queue = asyncio.Queue()
        self._is_active = True
        self._max_concurrent_analyses = 3
        
        # Contract analysis patterns
        self._risk_patterns = {
            "honeypot": [
                r"require\s*\(\s*balanceOf\[.*?\]\s*>=\s*amount",
                r"_transfer.*?require.*?balanceOf",
                r"function\s+transfer.*?require.*?_balances"
            ],
            "rug_pull": [
                r"function\s+withdraw.*?onlyOwner",
                r"function\s+emergencyWithdraw",
                r"selfdestruct\s*\(",
                r"function\s+pause.*?onlyOwner"
            ],
            "mint_exploit": [
                r"function\s+mint.*?public",
                r"_mint\s*\(.*?msg\.sender",
                r"totalSupply\s*\+=.*?amount"
            ]
        }
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Contract Analysis Agent specialized in analyzing smart contracts for security vulnerabilities and trading opportunities.

Your responsibilities:
1. Analyze smart contract code for security vulnerabilities
2. Evaluate tokenomics and economic models
3. Assess contract functionality and features
4. Identify potential risks and red flags
5. Determine trading implications and opportunities

When analyzing contracts, consider:
- Security vulnerabilities (reentrancy, overflow, access control)
- Tokenomics (supply, distribution, fees, burns)
- Ownership and admin functions
- Liquidity mechanisms and trading restrictions
- Historical deployment patterns
- Community and developer activity

Provide structured analysis with risk scores and trading recommendations."""
        )
        
        # Start background processing
        asyncio.create_task(self._start_analysis_workers())
    
    async def _start_analysis_workers(self):
        """Start background workers for contract analysis"""
        workers = []
        for i in range(self._max_concurrent_analyses):
            worker = asyncio.create_task(self._analysis_worker(f"worker_{i}"))
            workers.append(worker)
        
        await asyncio.gather(*workers, return_exceptions=True)
    
    async def _analysis_worker(self, worker_id: str):
        """Background worker for processing contract analysis requests"""
        while self._is_active:
            try:
                # Get analysis request from queue
                analysis_request = await asyncio.wait_for(
                    self._analysis_queue.get(), timeout=10.0
                )
                
                await self._process_contract_analysis(analysis_request)
                self._analysis_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in analysis worker {worker_id}: {e}")
                await asyncio.sleep(5)
    
    async def _process_contract_analysis(self, request: Dict[str, Any]):
        """Process a contract analysis request"""
        try:
            contract_address = request["contract_address"]
            analysis_type = request.get("analysis_type", "full")
            
            # Get contract data
            contract_data = await self._get_contract_data(contract_address)
            
            if not contract_data:
                self._logger.warning(f"Could not retrieve data for contract {contract_address}")
                return
            
            # Perform analysis
            analysis_result = await self._analyze_contract(contract_data, analysis_type)
            
            # Publish results
            await self._publish_contract_analysis(contract_address, analysis_result)
            
        except Exception as e:
            self._logger.error(f"Error processing contract analysis: {e}")
    
    async def _get_contract_data(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """Get contract data from on-chain sources"""
        try:
            if not self._onchain_tool:
                return None
            
            # Get contract info
            contract_info = await self._onchain_tool.run_json({
                "type": "contract_info",
                "contract_address": contract_address
            })
            
            # Get recent token transfers
            token_transfers = await self._onchain_tool.run_json({
                "type": "token_transfers",
                "contract_address": contract_address
            })
            
            return {
                "address": contract_address,
                "info": contract_info,
                "transfers": token_transfers,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            self._logger.error(f"Error getting contract data: {e}")
            return None
    
    async def _analyze_contract(self, contract_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """Analyze contract using LLM and pattern matching"""
        try:
            # Basic pattern-based analysis
            pattern_analysis = self._analyze_contract_patterns(contract_data)
            
            # LLM-based analysis
            llm_analysis = await self._analyze_contract_with_llm(contract_data, analysis_type)
            
            # Combine analyses
            combined_analysis = {
                **llm_analysis,
                "pattern_risks": pattern_analysis["risks"],
                "risk_score": max(
                    pattern_analysis["risk_score"],
                    llm_analysis.get("risk_score", 0)
                ),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            return combined_analysis
            
        except Exception as e:
            self._logger.error(f"Error in contract analysis: {e}")
            return self._basic_contract_analysis(contract_data)
    
    def _analyze_contract_patterns(self, contract_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze contract using predefined risk patterns"""
        risks = []
        risk_score = 0.0
        
        # For now, simulate contract code analysis
        # In a real implementation, you would get the actual contract source code
        contract_info = contract_data.get("info", {})
        
        # Analyze based on available data
        if contract_info.get("contract_name") == "Unknown":
            risks.append("Unverified contract")
            risk_score += 0.3
        
        # Analyze transfer patterns
        transfers = contract_data.get("transfers", {}).get("token_transfers", [])
        if transfers:
            # Check for suspicious transfer patterns
            large_transfers = [t for t in transfers if t.get("value", 0) > 1000000]
            if len(large_transfers) > len(transfers) * 0.5:
                risks.append("High concentration of large transfers")
                risk_score += 0.2
        
        return {
            "risks": risks,
            "risk_score": min(risk_score, 1.0)
        }
    
    async def _analyze_contract_with_llm(self, contract_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """Analyze contract using LLM"""
        try:
            contract_info = contract_data.get("info", {})
            transfers = contract_data.get("transfers", {})
            
            user_message = UserMessage(
                content=f"""Analyze this smart contract for security and trading implications:

Contract Address: {contract_data['address']}
Contract Info: {json.dumps(contract_info, indent=2)}
Recent Transfers: {len(transfers.get('token_transfers', []))} transfers
Analysis Type: {analysis_type}

Provide analysis in JSON format with:
1. risk_score: float (0.0 to 1.0, overall risk level)
2. security_rating: string (safe/moderate/risky/dangerous)
3. tokenomics_score: float (0.0 to 1.0, tokenomics quality)
4. liquidity_assessment: string (high/medium/low/none)
5. trading_recommendation: string (buy/hold/avoid/monitor)
6. key_risks: list of identified risks
7. opportunities: list of potential opportunities
8. confidence_score: float (0.0 to 1.0, analysis confidence)
9. recommended_position_size: string (none/small/medium/large)
10. key_insights: list of key insights about the contract

Focus on actionable trading insights and clear risk assessment.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_contract_analysis(contract_data)
            
            return self._basic_contract_analysis(contract_data)
            
        except Exception as e:
            self._logger.error(f"Error in LLM contract analysis: {e}")
            return self._basic_contract_analysis(contract_data)
    
    def _basic_contract_analysis(self, contract_data: Dict[str, Any]) -> Dict[str, Any]:
        """Basic contract analysis fallback"""
        contract_info = contract_data.get("info", {})
        
        # Basic risk assessment
        risk_score = 0.5  # Default moderate risk
        if contract_info.get("contract_name") == "Unknown":
            risk_score += 0.3
        
        return {
            "risk_score": min(risk_score, 1.0),
            "security_rating": "moderate",
            "tokenomics_score": 0.5,
            "liquidity_assessment": "unknown",
            "trading_recommendation": "monitor",
            "key_risks": ["Limited contract information"],
            "opportunities": [],
            "confidence_score": 0.3,
            "recommended_position_size": "none",
            "key_insights": ["Basic analysis only - insufficient data"]
        }
    
    async def _publish_contract_analysis(self, contract_address: str, analysis: Dict[str, Any]):
        """Publish contract analysis results"""
        try:
            # Create contract analysis message
            analysis_msg = ContractAnalysisResult(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"contract_{contract_address}_{datetime.now().timestamp()}",
                contract_address=contract_address,
                risk_score=analysis.get("risk_score", 0.5),
                security_rating=analysis.get("security_rating", "unknown"),
                trading_recommendation=analysis.get("trading_recommendation", "monitor"),
                key_findings=analysis.get("key_risks", []) + analysis.get("opportunities", [])
            )
            
            # Publish to strategy generation topic
            await self.publish_message(
                analysis_msg,
                topic_id=TopicId("StrategyGenerationAgent", source=self.id.key)
            )
            
            # If high risk, alert control systems
            if analysis.get("risk_score", 0) > 0.7:
                await self.publish_message(
                    analysis_msg,
                    topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
                )
            
            # If good opportunity, generate trading signal
            if (analysis.get("trading_recommendation") == "buy" and 
                analysis.get("confidence_score", 0) > 0.7):
                
                signal = TrendSignal(
                    timestamp=datetime.now(),
                    source_agent=self.id.type,
                    message_id=f"signal_contract_{contract_address}_{datetime.now().timestamp()}",
                    signal_type="bullish",
                    strength=analysis.get("tokenomics_score", 0.5),
                    timeframe="medium",
                    supporting_data=[f"Contract analysis: {analysis.get('security_rating')}"],
                    confidence_level=analysis.get("confidence_score", 0.5),
                    target_assets=[contract_address]
                )
                
                await self.publish_message(
                    signal,
                    topic_id=TopicId("StrategyGenerationAgent", source=self.id.key)
                )
            
            self._logger.info(
                f"Published contract analysis for {contract_address}: "
                f"{analysis.get('security_rating')} (risk: {analysis.get('risk_score', 0):.2f})"
            )
            
        except Exception as e:
            self._logger.error(f"Error publishing contract analysis: {e}")
    
    async def analyze_contract(self, contract_address: str, analysis_type: str = "full"):
        """Public method to request contract analysis"""
        await self._analysis_queue.put({
            "contract_address": contract_address,
            "analysis_type": analysis_type,
            "requested_at": datetime.now()
        })
    
    @message_handler
    async def handle_trend_signal(self, message: TrendSignal, ctx: MessageContext) -> None:
        """Handle trend signals that might contain contract addresses"""
        # Extract potential contract addresses from target assets
        for asset in message.target_assets:
            if self._is_contract_address(asset):
                await self.analyze_contract(asset, "quick")
    
    def _is_contract_address(self, address: str) -> bool:
        """Check if string looks like a contract address"""
        return (len(address) == 42 and 
                address.startswith("0x") and 
                all(c in "0123456789abcdefABCDEF" for c in address[2:]))
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Contract analysis started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Contract analysis stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_queue":
            while not self._analysis_queue.empty():
                try:
                    self._analysis_queue.get_nowait()
                    self._analysis_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            self._logger.info("Cleared analysis queue")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "contract_analysis":
            updates = message.updates
            
            if "max_concurrent_analyses" in updates:
                self._max_concurrent_analyses = updates["max_concurrent_analyses"]
                self._logger.info(f"Updated max concurrent analyses to {self._max_concurrent_analyses}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "ContractAnalysisAgent",
            "active": self._is_active,
            "queue_size": self._analysis_queue.qsize(),
            "max_concurrent_analyses": self._max_concurrent_analyses,
            "onchain_tool_enabled": self._onchain_tool is not None,
            "last_update": datetime.now().isoformat()
        }
