"""
Slack Notification Agent

This agent sends notifications and alerts to Slack channels for team
communication and system monitoring.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque
import aiohttp

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    PerformanceAlert,
    PerformanceReport,
    OptimizationResult,
    SystemCommand,
    SlackMessage,
    ConfigUpdate
)


@default_subscription
class SlackNotificationAgent(RoutedAgent):
    """
    Agent that sends notifications and alerts to Slack channels.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Slack Notification Agent",
        webhook_url: Optional[str] = None,
        bot_token: Optional[str] = None
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._webhook_url = webhook_url
        self._bot_token = bot_token
        self._message_queue = asyncio.Queue()
        
        # Channel configuration
        self._channels = {
            "alerts": "#trading-alerts",
            "performance": "#trading-performance", 
            "optimization": "#trading-optimization",
            "general": "#trading-general",
            "errors": "#trading-errors"
        }
        
        # Message formatting configuration
        self._alert_colors = {
            "info": "#36a64f",      # Green
            "warning": "#ff9500",   # Orange
            "critical": "#ff0000",  # Red
            "emergency": "#8b0000"  # Dark Red
        }
        
        # Rate limiting
        self._rate_limits = {
            "alerts": {"max_per_hour": 20, "current": 0, "reset_time": datetime.now()},
            "performance": {"max_per_hour": 10, "current": 0, "reset_time": datetime.now()},
            "optimization": {"max_per_hour": 5, "current": 0, "reset_time": datetime.now()}
        }
        
        # Message history for deduplication
        self._message_history = deque(maxlen=1000)
        self._duplicate_threshold = 300  # 5 minutes
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Slack Notification Agent that formats trading system messages for team communication.

Your responsibilities:
1. Format trading alerts and notifications for Slack channels
2. Create clear, actionable messages with appropriate urgency
3. Generate structured Slack message blocks with rich formatting
4. Summarize complex trading data into digestible updates
5. Maintain professional tone while highlighting critical information
6. Adapt message format based on alert severity and audience

When formatting messages:
- Use appropriate Slack formatting (bold, italics, code blocks)
- Include relevant emojis for visual clarity
- Structure information hierarchically (most important first)
- Provide context and recommended actions
- Use color coding for different alert levels
- Keep messages concise but informative
- Include timestamps and relevant metrics

For different message types:
- Alerts: Highlight urgency and required actions
- Performance: Focus on key metrics and trends
- Optimization: Emphasize improvements and recommendations
- Errors: Provide clear problem description and next steps"""
        )
        
        # Start message processor
        asyncio.create_task(self._start_message_processor())
        asyncio.create_task(self._start_rate_limit_reset())
    
    async def _start_message_processor(self):
        """Start background message processor"""
        while self._is_active:
            try:
                # Process queued messages
                message = await asyncio.wait_for(self._message_queue.get(), timeout=30.0)
                await self._process_queued_message(message)
                self._message_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in message processor: {e}")
                await asyncio.sleep(10)
    
    async def _start_rate_limit_reset(self):
        """Reset rate limits hourly"""
        while self._is_active:
            try:
                await asyncio.sleep(3600)  # 1 hour
                current_time = datetime.now()
                
                for category in self._rate_limits:
                    self._rate_limits[category]["current"] = 0
                    self._rate_limits[category]["reset_time"] = current_time
                
                self._logger.debug("Rate limits reset")
                
            except Exception as e:
                self._logger.error(f"Error resetting rate limits: {e}")
    
    async def _process_queued_message(self, message: Dict[str, Any]):
        """Process a queued message for sending"""
        try:
            message_type = message.get("type")
            data = message.get("data")
            
            # Check rate limits
            if not self._check_rate_limit(message_type):
                self._logger.warning(f"Rate limit exceeded for {message_type}")
                return
            
            # Check for duplicates
            if self._is_duplicate_message(data):
                self._logger.debug(f"Skipping duplicate message: {message_type}")
                return
            
            # Process based on type
            if message_type == "alert":
                await self._send_alert_notification(data)
            elif message_type == "performance":
                await self._send_performance_notification(data)
            elif message_type == "optimization":
                await self._send_optimization_notification(data)
            elif message_type == "custom":
                await self._send_custom_message(data)
            
            # Update rate limit
            self._update_rate_limit(message_type)
            
            # Store message for duplicate detection
            self._message_history.append({
                "timestamp": datetime.now(),
                "type": message_type,
                "hash": self._get_message_hash(data)
            })
            
        except Exception as e:
            self._logger.error(f"Error processing queued message: {e}")
    
    def _check_rate_limit(self, message_type: str) -> bool:
        """Check if message type is within rate limits"""
        if message_type not in self._rate_limits:
            return True
        
        limit_info = self._rate_limits[message_type]
        current_time = datetime.now()
        
        # Reset if hour has passed
        if (current_time - limit_info["reset_time"]).total_seconds() >= 3600:
            limit_info["current"] = 0
            limit_info["reset_time"] = current_time
        
        return limit_info["current"] < limit_info["max_per_hour"]
    
    def _update_rate_limit(self, message_type: str):
        """Update rate limit counter"""
        if message_type in self._rate_limits:
            self._rate_limits[message_type]["current"] += 1
    
    def _is_duplicate_message(self, data: Any) -> bool:
        """Check if message is a duplicate"""
        try:
            current_hash = self._get_message_hash(data)
            current_time = datetime.now()
            
            for msg in self._message_history:
                if (msg["hash"] == current_hash and 
                    (current_time - msg["timestamp"]).total_seconds() < self._duplicate_threshold):
                    return True
            
            return False
            
        except Exception as e:
            self._logger.error(f"Error checking duplicate message: {e}")
            return False
    
    def _get_message_hash(self, data: Any) -> str:
        """Get hash for message deduplication"""
        try:
            if hasattr(data, '__dict__'):
                # For dataclass objects
                content = str(sorted(data.__dict__.items()))
            else:
                content = str(data)
            
            return str(hash(content))
            
        except Exception as e:
            self._logger.error(f"Error generating message hash: {e}")
            return str(hash(str(data)))
    
    async def _send_slack_message(self, channel: str, blocks: List[Dict[str, Any]], text: str = None) -> bool:
        """Send message to Slack using webhook or bot token"""
        try:
            if not self._webhook_url and not self._bot_token:
                self._logger.error("No Slack webhook URL or bot token configured")
                return False
            
            payload = {
                "channel": channel,
                "blocks": blocks
            }
            
            if text:
                payload["text"] = text
            
            if self._webhook_url:
                # Use webhook
                async with aiohttp.ClientSession() as session:
                    async with session.post(self._webhook_url, json=payload) as response:
                        if response.status == 200:
                            self._logger.debug(f"Message sent to {channel}")
                            return True
                        else:
                            self._logger.error(f"Failed to send message: {response.status}")
                            return False
            
            elif self._bot_token:
                # Use bot token
                headers = {
                    "Authorization": f"Bearer {self._bot_token}",
                    "Content-Type": "application/json"
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "https://slack.com/api/chat.postMessage",
                        json=payload,
                        headers=headers
                    ) as response:
                        result = await response.json()
                        if result.get("ok"):
                            self._logger.debug(f"Message sent to {channel}")
                            return True
                        else:
                            self._logger.error(f"Failed to send message: {result.get('error')}")
                            return False
            
            return False
            
        except Exception as e:
            self._logger.error(f"Error sending Slack message: {e}")
            return False
    
    async def _send_alert_notification(self, alert: PerformanceAlert):
        """Send alert notification to Slack"""
        try:
            # Generate formatted message using LLM
            formatted_message = await self._format_alert_message(alert)
            
            # Create Slack blocks
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"🚨 {alert.alert_level.upper()} ALERT: {alert.strategy_name}"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Type:* {alert.alert_type}"
                        },
                        {
                            "type": "mrkdwn", 
                            "text": f"*Level:* {alert.alert_level.upper()}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Current Value:* {alert.current_value}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Threshold:* {alert.threshold_value}"
                        }
                    ]
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Message:* {alert.message}"
                    }
                }
            ]
            
            # Add recommended actions if available
            if alert.recommended_actions:
                actions_text = "\n".join([f"• {action}" for action in alert.recommended_actions])
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Recommended Actions:*\n{actions_text}"
                    }
                })
            
            # Add context
            blocks.append({
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')} | Source: {alert.source_agent}"
                    }
                ]
            })
            
            # Add color based on alert level
            color = self._alert_colors.get(alert.alert_level, "#36a64f")
            blocks[0]["accessory"] = {
                "type": "image",
                "image_url": f"https://via.placeholder.com/1x1/{color.replace('#', '')}.png",
                "alt_text": alert.alert_level
            }
            
            # Send to alerts channel
            channel = self._channels.get("alerts", "#trading-alerts")
            await self._send_slack_message(channel, blocks, f"Alert: {alert.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error sending alert notification: {e}")
    
    async def _send_performance_notification(self, report: PerformanceReport):
        """Send performance report to Slack"""
        try:
            # Create Slack blocks
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"📊 Performance Report: {report.strategy_name}"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Period:* {report.report_period}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Generated:* {report.timestamp.strftime('%H:%M:%S')}"
                        }
                    ]
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Summary:* {report.analysis_summary}"
                    }
                }
            ]
            
            # Add key metrics if available
            if hasattr(report, 'performance_metrics') and report.performance_metrics:
                metrics_text = ""
                for key, value in list(report.performance_metrics.items())[:5]:  # Limit to 5 metrics
                    if isinstance(value, (int, float)):
                        metrics_text += f"• {key}: {value:.2f}\n"
                    else:
                        metrics_text += f"• {key}: {value}\n"
                
                if metrics_text:
                    blocks.append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Key Metrics:*\n{metrics_text}"
                        }
                    })
            
            # Add insights
            if report.key_insights:
                insights_text = "\n".join([f"• {insight}" for insight in report.key_insights[:3]])
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Key Insights:*\n{insights_text}"
                    }
                })
            
            # Add recommendations
            if report.recommendations:
                recommendations_text = "\n".join([f"• {rec}" for rec in report.recommendations[:3]])
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Recommendations:*\n{recommendations_text}"
                    }
                })
            
            # Send to performance channel
            channel = self._channels.get("performance", "#trading-performance")
            await self._send_slack_message(channel, blocks, f"Performance: {report.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error sending performance notification: {e}")
    
    async def _send_optimization_notification(self, result: OptimizationResult):
        """Send optimization result to Slack"""
        try:
            # Create Slack blocks
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"⚙️ Optimization Complete: {result.strategy_name}"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Algorithm:* {result.optimization_algorithm}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Improvement:* {result.performance_improvement:.2%}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Confidence:* {result.confidence_score:.1%}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Completed:* {result.timestamp.strftime('%H:%M:%S')}"
                        }
                    ]
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Summary:* {result.analysis_summary}"
                    }
                }
            ]
            
            # Add optimized parameters
            if result.optimized_parameters:
                params_text = ""
                for key, value in list(result.optimized_parameters.items())[:5]:  # Limit to 5 parameters
                    if isinstance(value, (int, float)):
                        params_text += f"• {key}: {value:.4f}\n"
                    else:
                        params_text += f"• {key}: {value}\n"
                
                if params_text:
                    blocks.append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Optimized Parameters:*\n{params_text}"
                        }
                    })
            
            # Add recommendations
            if result.recommendations:
                recommendations_text = "\n".join([f"• {rec}" for rec in result.recommendations[:3]])
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Recommendations:*\n{recommendations_text}"
                    }
                })
            
            # Send to optimization channel
            channel = self._channels.get("optimization", "#trading-optimization")
            await self._send_slack_message(channel, blocks, f"Optimization: {result.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error sending optimization notification: {e}")
    
    async def _send_custom_message(self, message_data: Dict[str, Any]):
        """Send custom formatted message"""
        try:
            channel = message_data.get("channel", self._channels.get("general"))
            text = message_data.get("text", "")
            blocks = message_data.get("blocks", [])
            
            if not blocks and text:
                # Create simple text block
                blocks = [{
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": text
                    }
                }]
            
            await self._send_slack_message(channel, blocks, text)
            
        except Exception as e:
            self._logger.error(f"Error sending custom message: {e}")
    
    async def _format_alert_message(self, alert: PerformanceAlert) -> str:
        """Format alert message using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Format this trading alert for Slack notification:

Strategy: {alert.strategy_name}
Alert Type: {alert.alert_type}
Level: {alert.alert_level}
Message: {alert.message}
Current Value: {alert.current_value}
Threshold: {alert.threshold_value}
Recommended Actions: {', '.join(alert.recommended_actions)}

Create a concise, professional Slack message that highlights the key information and urgency.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                return response.content
            
            return f"Alert for {alert.strategy_name}: {alert.message}"
            
        except Exception as e:
            self._logger.error(f"Error formatting alert message: {e}")
            return f"Alert for {alert.strategy_name}: {alert.message}"
    
    @message_handler
    async def handle_performance_alert(self, message: PerformanceAlert, ctx: MessageContext) -> None:
        """Handle performance alerts"""
        try:
            await self._message_queue.put({
                "type": "alert",
                "data": message
            })
            
        except Exception as e:
            self._logger.error(f"Error handling performance alert: {e}")
    
    @message_handler
    async def handle_performance_report(self, message: PerformanceReport, ctx: MessageContext) -> None:
        """Handle performance reports"""
        try:
            # Only send daily and weekly reports to avoid spam
            if message.report_period in ["daily", "weekly"]:
                await self._message_queue.put({
                    "type": "performance",
                    "data": message
                })
            
        except Exception as e:
            self._logger.error(f"Error handling performance report: {e}")
    
    @message_handler
    async def handle_optimization_result(self, message: OptimizationResult, ctx: MessageContext) -> None:
        """Handle optimization results"""
        try:
            await self._message_queue.put({
                "type": "optimization",
                "data": message
            })
            
        except Exception as e:
            self._logger.error(f"Error handling optimization result: {e}")
    
    @message_handler
    async def handle_slack_message(self, message: SlackMessage, ctx: MessageContext) -> None:
        """Handle custom Slack messages"""
        try:
            await self._message_queue.put({
                "type": "custom",
                "data": {
                    "channel": message.channel,
                    "text": message.text,
                    "blocks": message.blocks if hasattr(message, 'blocks') else None
                }
            })
            
        except Exception as e:
            self._logger.error(f"Error handling Slack message: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Slack notifications started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Slack notifications stopped")
        elif command == "test_notification":
            # Send test message
            test_message = {
                "channel": self._channels.get("general"),
                "text": "🧪 Test notification from Crypto Trading System",
                "blocks": [{
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": "🧪 *Test Notification*\n\nThis is a test message to verify Slack integration is working properly."
                    }
                }]
            }
            await self._message_queue.put({"type": "custom", "data": test_message})
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "slack":
            updates = message.updates
            
            if "channels" in updates:
                self._channels.update(updates["channels"])
                self._logger.info("Updated Slack channels")
            
            if "webhook_url" in updates:
                self._webhook_url = updates["webhook_url"]
                self._logger.info("Updated webhook URL")
            
            if "rate_limits" in updates:
                self._rate_limits.update(updates["rate_limits"])
                self._logger.info("Updated rate limits")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "SlackNotificationAgent",
            "active": self._is_active,
            "webhook_configured": self._webhook_url is not None,
            "bot_token_configured": self._bot_token is not None,
            "message_queue_size": self._message_queue.qsize(),
            "channels": self._channels,
            "rate_limits": self._rate_limits,
            "message_history_size": len(self._message_history),
            "last_update": datetime.now().isoformat()
        }
