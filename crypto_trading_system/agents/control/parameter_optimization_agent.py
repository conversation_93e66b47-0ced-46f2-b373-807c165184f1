"""
Parameter Optimization Agent

This agent optimizes strategy parameters based on performance feedback,
using various optimization algorithms and machine learning techniques.
"""

import asyncio
import json
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging
from collections import deque, defaultdict
from dataclasses import asdict

# Optional dependencies - graceful degradation if not available
try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

try:
    from scipy.optimize import differential_evolution, minimize
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

try:
    from sklearn.gaussian_process import GaussianProcessRegressor
    from sklearn.gaussian_process.kernels import Matern
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    PerformanceReport,
    StrategyDefinition,
    OptimizationRequest,
    OptimizationResult,
    SystemCommand,
    ConfigUpdate
)


@default_subscription
class ParameterOptimizationAgent(RoutedAgent):
    """
    Agent responsible for optimizing strategy parameters based on performance feedback.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Parameter Optimization Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._optimization_queue = asyncio.Queue()
        
        # Optimization data storage
        self._strategy_performance = defaultdict(lambda: deque(maxlen=1000))
        self._optimization_history = defaultdict(lambda: deque(maxlen=100))
        self._current_optimizations = {}  # strategy_name -> optimization_state
        
        # Optimization configuration
        self._optimization_config = {
            "min_samples_for_optimization": 20,
            "optimization_frequency": 86400,  # Daily optimization
            "max_concurrent_optimizations": 3,
            "optimization_timeout": 3600,  # 1 hour timeout
            "convergence_threshold": 0.01,  # 1% improvement threshold
            "max_iterations": 100
        }
        
        # Optimization algorithms (with availability checks)
        self._algorithms = {
            "grid_search": self._grid_search_optimization,  # Always available
            "random_search": self._random_search_optimization  # Always available
        }

        # Add optional algorithms if dependencies are available
        if SKLEARN_AVAILABLE:
            self._algorithms["bayesian"] = self._bayesian_optimization
        if SCIPY_AVAILABLE:
            self._algorithms["differential_evolution"] = self._differential_evolution_optimization
        if OPTUNA_AVAILABLE:
            self._algorithms["optuna"] = self._optuna_optimization
        
        # Parameter bounds and constraints
        self._parameter_bounds = {
            "lookback_period": (5, 100),
            "threshold": (0.01, 0.5),
            "stop_loss": (0.01, 0.1),
            "take_profit": (0.02, 0.5),
            "position_size": (0.01, 0.1),
            "volatility_window": (10, 50),
            "momentum_period": (5, 30),
            "rsi_period": (10, 30),
            "ma_period": (10, 200)
        }
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Parameter Optimization Agent specialized in optimizing trading strategy parameters for maximum risk-adjusted returns.

Your responsibilities:
1. Analyze strategy performance data to identify optimization opportunities
2. Select appropriate optimization algorithms based on parameter characteristics
3. Define parameter bounds and constraints based on market knowledge
4. Interpret optimization results and provide actionable recommendations
5. Detect overfitting and ensure parameter stability across market conditions
6. Provide insights on parameter sensitivity and robustness

When optimizing parameters, consider:
- Risk-adjusted returns (Sharpe ratio, Sortino ratio) as primary objectives
- Parameter stability across different market regimes
- Transaction costs and implementation constraints
- Overfitting prevention through cross-validation
- Statistical significance of performance improvements
- Correlation between parameters and their interactions

Provide detailed analysis and recommendations for parameter adjustments."""
        )
        
        # Start background processes
        asyncio.create_task(self._start_optimization_worker())
        asyncio.create_task(self._start_periodic_optimization())
    
    async def _start_optimization_worker(self):
        """Start background worker for parameter optimization"""
        while self._is_active:
            try:
                # Get optimization request from queue
                optimization_request = await asyncio.wait_for(
                    self._optimization_queue.get(), timeout=30.0
                )
                
                await self._process_optimization_request(optimization_request)
                self._optimization_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in optimization worker: {e}")
                await asyncio.sleep(10)
    
    async def _start_periodic_optimization(self):
        """Start periodic optimization for all strategies"""
        while self._is_active:
            try:
                await self._trigger_periodic_optimizations()
                await asyncio.sleep(self._optimization_config["optimization_frequency"])
            except Exception as e:
                self._logger.error(f"Error in periodic optimization: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour on error
    
    async def _process_optimization_request(self, request: OptimizationRequest):
        """Process a parameter optimization request"""
        try:
            strategy_name = getattr(request, 'strategy_name', getattr(request, 'strategy_id', 'unknown'))
            
            # Check if optimization is already running
            if strategy_name in self._current_optimizations:
                self._logger.warning(f"Optimization already running for {strategy_name}")
                return
            
            # Check if we have enough performance data
            if len(self._strategy_performance[strategy_name]) < self._optimization_config["min_samples_for_optimization"]:
                self._logger.warning(f"Insufficient data for optimization: {strategy_name}")
                return
            
            # Start optimization
            self._current_optimizations[strategy_name] = {
                "start_time": datetime.now(),
                "status": "running",
                "algorithm": request.optimization_algorithm or "bayesian"
            }
            
            # Run optimization
            result = await self._run_optimization(request)
            
            # Store result
            self._optimization_history[strategy_name].append(result)
            
            # Publish result
            await self._publish_optimization_result(result)
            
            # Clean up
            del self._current_optimizations[strategy_name]
            
        except Exception as e:
            self._logger.error(f"Error processing optimization request: {e}")
            if strategy_name in self._current_optimizations:
                del self._current_optimizations[strategy_name]
    
    async def _run_optimization(self, request: OptimizationRequest) -> OptimizationResult:
        """Run parameter optimization using specified algorithm"""
        try:
            strategy_name = request.strategy_name
            algorithm = request.optimization_algorithm or "bayesian"
            
            # Get performance data
            performance_data = list(self._strategy_performance[strategy_name])
            
            # Define optimization objective
            def objective_function(params: Dict[str, float]) -> float:
                return self._evaluate_parameters(strategy_name, params, performance_data)
            
            # Get parameter space
            parameter_space = self._get_parameter_space(request.parameters_to_optimize)
            
            # Run optimization algorithm
            if algorithm in self._algorithms:
                best_params, best_score, optimization_details = await self._algorithms[algorithm](
                    objective_function, parameter_space, request
                )
            else:
                raise ValueError(f"Unknown optimization algorithm: {algorithm}")
            
            # Generate LLM analysis
            analysis = await self._generate_optimization_analysis(
                strategy_name, best_params, best_score, optimization_details
            )
            
            # Create result
            result = OptimizationResult(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"optimization_{strategy_name}_{datetime.now().timestamp()}",
                strategy_name=strategy_name,
                optimization_algorithm=algorithm,
                original_parameters=request.current_parameters,
                optimized_parameters=best_params,
                performance_improvement=best_score,
                optimization_details=optimization_details,
                analysis_summary=analysis.get("summary", ""),
                recommendations=analysis.get("recommendations", []),
                confidence_score=analysis.get("confidence", 0.5)
            )
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error running optimization: {e}")
            # Return error result
            return OptimizationResult(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"optimization_error_{datetime.now().timestamp()}",
                strategy_name=request.strategy_name,
                optimization_algorithm=request.optimization_algorithm or "unknown",
                original_parameters=request.current_parameters,
                optimized_parameters=request.current_parameters,
                performance_improvement=0.0,
                optimization_details={"error": str(e)},
                analysis_summary=f"Optimization failed: {str(e)}",
                recommendations=["Review optimization parameters", "Check data quality"],
                confidence_score=0.0
            )
    
    def _evaluate_parameters(self, strategy_name: str, params: Dict[str, float], performance_data: List[Dict[str, Any]]) -> float:
        """Evaluate parameter set using historical performance data"""
        try:
            # This is a simplified evaluation - in practice, you would:
            # 1. Apply parameters to historical data
            # 2. Simulate strategy performance
            # 3. Calculate risk-adjusted returns
            
            # For now, use a simplified scoring based on parameter relationships
            score = 0.0
            
            # Penalize extreme parameter values
            for param_name, value in params.items():
                if param_name in self._parameter_bounds:
                    min_val, max_val = self._parameter_bounds[param_name]
                    normalized_value = (value - min_val) / (max_val - min_val)
                    
                    # Prefer values closer to the middle of the range
                    penalty = abs(normalized_value - 0.5) * 0.1
                    score -= penalty
            
            # Add some randomness to simulate backtesting results
            base_performance = np.random.normal(0.1, 0.05)  # 10% average return with 5% std
            parameter_effect = np.random.normal(0, 0.02)  # Parameter effect
            
            score += base_performance + parameter_effect
            
            # Penalize high volatility parameters
            if "stop_loss" in params and params["stop_loss"] < 0.02:
                score -= 0.05  # Penalty for very tight stops
            
            if "position_size" in params and params["position_size"] > 0.08:
                score -= 0.03  # Penalty for large positions
            
            return score
            
        except Exception as e:
            self._logger.error(f"Error evaluating parameters: {e}")
            return -1.0  # Return poor score on error
    
    def _get_parameter_space(self, parameters_to_optimize: List[str]) -> Dict[str, Tuple[float, float]]:
        """Get parameter bounds for optimization"""
        parameter_space = {}
        
        for param_name in parameters_to_optimize:
            if param_name in self._parameter_bounds:
                parameter_space[param_name] = self._parameter_bounds[param_name]
            else:
                # Default bounds for unknown parameters
                parameter_space[param_name] = (0.01, 1.0)
                self._logger.warning(f"Using default bounds for parameter: {param_name}")
        
        return parameter_space
    
    async def _bayesian_optimization(self, objective_function, parameter_space: Dict[str, Tuple[float, float]], request: OptimizationRequest) -> Tuple[Dict[str, float], float, Dict[str, Any]]:
        """Bayesian optimization using Gaussian Process"""
        try:
            if not SKLEARN_AVAILABLE:
                self._logger.warning("sklearn not available, falling back to random search")
                return await self._random_search_optimization(objective_function, parameter_space, request)

            # Prepare data for Gaussian Process
            param_names = list(parameter_space.keys())
            bounds = np.array([parameter_space[name] for name in param_names])

            # Initial random samples
            n_initial = min(10, self._optimization_config["max_iterations"] // 4)
            X_samples = []
            y_samples = []

            for _ in range(n_initial):
                # Random sample within bounds
                sample = {}
                for i, param_name in enumerate(param_names):
                    min_val, max_val = bounds[i]
                    sample[param_name] = np.random.uniform(min_val, max_val)

                score = objective_function(sample)
                X_samples.append([sample[name] for name in param_names])
                y_samples.append(score)

            X_samples = np.array(X_samples)
            y_samples = np.array(y_samples)

            # Gaussian Process optimization
            gp = GaussianProcessRegressor(kernel=Matern(length_scale=1.0, nu=2.5))

            best_score = np.max(y_samples)
            best_params_array = X_samples[np.argmax(y_samples)]

            # Bayesian optimization loop
            for iteration in range(n_initial, self._optimization_config["max_iterations"]):
                # Fit GP
                gp.fit(X_samples, y_samples)

                # Acquisition function (Upper Confidence Bound)
                def acquisition(x):
                    x = x.reshape(1, -1)
                    mean, std = gp.predict(x, return_std=True)
                    return -(mean + 2.0 * std)  # Negative for minimization

                # Optimize acquisition function (fallback to random if scipy not available)
                if SCIPY_AVAILABLE:
                    result = minimize(
                        acquisition,
                        x0=best_params_array,
                        bounds=bounds,
                        method='L-BFGS-B'
                    )

                    if result.success:
                        next_params_array = result.x
                    else:
                        # Random fallback
                        next_params_array = np.array([np.random.uniform(bounds[i][0], bounds[i][1]) for i in range(len(param_names))])
                else:
                    # Random fallback
                    next_params_array = np.array([np.random.uniform(bounds[i][0], bounds[i][1]) for i in range(len(param_names))])

                next_params = {param_names[i]: next_params_array[i] for i in range(len(param_names))}
                next_score = objective_function(next_params)

                # Update samples
                X_samples = np.vstack([X_samples, next_params_array])
                y_samples = np.append(y_samples, next_score)

                # Update best
                if next_score > best_score:
                    best_score = next_score
                    best_params_array = next_params_array

            # Convert best parameters back to dict
            best_params = {param_names[i]: best_params_array[i] for i in range(len(param_names))}

            optimization_details = {
                "algorithm": "bayesian",
                "iterations": len(y_samples),
                "initial_samples": n_initial,
                "final_score": best_score,
                "score_history": y_samples.tolist()
            }

            return best_params, best_score, optimization_details

        except Exception as e:
            self._logger.error(f"Error in Bayesian optimization: {e}")
            # Fallback to random search
            return await self._random_search_optimization(objective_function, parameter_space, request)
    
    async def _differential_evolution_optimization(self, objective_function, parameter_space: Dict[str, Tuple[float, float]], request: OptimizationRequest) -> Tuple[Dict[str, float], float, Dict[str, Any]]:
        """Differential Evolution optimization"""
        try:
            if not SCIPY_AVAILABLE:
                self._logger.warning("scipy not available, falling back to random search")
                return await self._random_search_optimization(objective_function, parameter_space, request)

            param_names = list(parameter_space.keys())
            bounds = [parameter_space[name] for name in param_names]

            def objective_wrapper(x):
                params = {param_names[i]: x[i] for i in range(len(param_names))}
                return -objective_function(params)  # Negative for minimization

            result = differential_evolution(
                objective_wrapper,
                bounds,
                maxiter=self._optimization_config["max_iterations"],
                seed=42
            )

            if result.success:
                best_params = {param_names[i]: result.x[i] for i in range(len(param_names))}
                best_score = -result.fun  # Convert back to maximization

                optimization_details = {
                    "algorithm": "differential_evolution",
                    "iterations": result.nit,
                    "function_evaluations": result.nfev,
                    "final_score": best_score,
                    "success": result.success
                }

                return best_params, best_score, optimization_details
            else:
                raise Exception(f"Optimization failed: {result.message}")

        except Exception as e:
            self._logger.error(f"Error in Differential Evolution optimization: {e}")
            return await self._random_search_optimization(objective_function, parameter_space, request)

    async def _random_search_optimization(self, objective_function, parameter_space: Dict[str, Tuple[float, float]], request: OptimizationRequest) -> Tuple[Dict[str, float], float, Dict[str, Any]]:
        """Random search optimization (always available fallback)"""
        try:
            param_names = list(parameter_space.keys())

            best_params = None
            best_score = float('-inf')
            evaluations = 0
            score_history = []

            for _ in range(self._optimization_config["max_iterations"]):
                # Generate random parameters
                params = {}
                for param_name in param_names:
                    min_val, max_val = parameter_space[param_name]
                    params[param_name] = np.random.uniform(min_val, max_val)

                # Evaluate
                score = objective_function(params)
                score_history.append(score)
                evaluations += 1

                # Update best
                if score > best_score:
                    best_score = score
                    best_params = params.copy()

            optimization_details = {
                "algorithm": "random_search",
                "evaluations": evaluations,
                "final_score": best_score,
                "score_history": score_history
            }

            return best_params, best_score, optimization_details

        except Exception as e:
            self._logger.error(f"Error in Random Search optimization: {e}")
            # Return current parameters as ultimate fallback
            current_params = getattr(request, 'current_parameters', {})
            if not current_params:
                # Generate default parameters
                current_params = {}
                for param_name, (min_val, max_val) in parameter_space.items():
                    current_params[param_name] = (min_val + max_val) / 2
            return current_params, 0.0, {"error": str(e)}
    
    async def _optuna_optimization(self, objective_function, parameter_space: Dict[str, Tuple[float, float]], request: OptimizationRequest) -> Tuple[Dict[str, float], float, Dict[str, Any]]:
        """Optuna-based optimization"""
        try:
            if not OPTUNA_AVAILABLE:
                self._logger.warning("optuna not available, falling back to random search")
                return await self._random_search_optimization(objective_function, parameter_space, request)

            def optuna_objective(trial):
                params = {}
                for param_name, (min_val, max_val) in parameter_space.items():
                    params[param_name] = trial.suggest_float(param_name, min_val, max_val)
                return objective_function(params)

            study = optuna.create_study(direction='maximize')
            study.optimize(optuna_objective, n_trials=self._optimization_config["max_iterations"])

            best_params = study.best_params
            best_score = study.best_value

            optimization_details = {
                "algorithm": "optuna",
                "trials": len(study.trials),
                "best_trial": study.best_trial.number,
                "final_score": best_score
            }

            return best_params, best_score, optimization_details

        except Exception as e:
            self._logger.error(f"Error in Optuna optimization: {e}")
            return await self._random_search_optimization(objective_function, parameter_space, request)
    
    async def _grid_search_optimization(self, objective_function, parameter_space: Dict[str, Tuple[float, float]], request: OptimizationRequest) -> Tuple[Dict[str, float], float, Dict[str, Any]]:
        """Grid search optimization"""
        try:
            param_names = list(parameter_space.keys())
            
            # Create grid points (limited to prevent explosion)
            grid_points_per_param = max(3, int(self._optimization_config["max_iterations"] ** (1/len(param_names))))
            
            param_grids = {}
            for param_name, (min_val, max_val) in parameter_space.items():
                param_grids[param_name] = np.linspace(min_val, max_val, grid_points_per_param)
            
            best_params = None
            best_score = float('-inf')
            evaluations = 0
            
            # Generate all combinations
            def generate_combinations(param_names, param_grids, current_params={}):
                if len(current_params) == len(param_names):
                    yield current_params.copy()
                else:
                    param_name = param_names[len(current_params)]
                    for value in param_grids[param_name]:
                        current_params[param_name] = value
                        yield from generate_combinations(param_names, param_grids, current_params)
                        del current_params[param_name]
            
            for params in generate_combinations(param_names, param_grids):
                score = objective_function(params)
                evaluations += 1
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
                if evaluations >= self._optimization_config["max_iterations"]:
                    break
            
            optimization_details = {
                "algorithm": "grid_search",
                "evaluations": evaluations,
                "grid_points_per_param": grid_points_per_param,
                "final_score": best_score
            }
            
            return best_params, best_score, optimization_details
            
        except Exception as e:
            self._logger.error(f"Error in Grid Search optimization: {e}")
            return request.current_parameters, 0.0, {"error": str(e)}
    
    async def _generate_optimization_analysis(self, strategy_name: str, best_params: Dict[str, float], best_score: float, optimization_details: Dict[str, Any]) -> Dict[str, Any]:
        """Generate LLM analysis of optimization results"""
        try:
            user_message = UserMessage(
                content=f"""Analyze parameter optimization results for strategy '{strategy_name}':

Best Parameters:
{json.dumps(best_params, indent=2)}

Performance Score: {best_score:.4f}

Optimization Details:
{json.dumps(optimization_details, indent=2, default=str)}

Provide analysis in JSON format with:
1. summary: string (executive summary of optimization results)
2. recommendations: list (specific recommendations for parameter usage)
3. confidence: float (confidence in optimization results, 0-1)
4. parameter_insights: dict (insights about individual parameters)
5. risk_assessment: string (assessment of parameter stability)
6. implementation_notes: list (notes for implementation)

Focus on practical insights and parameter stability.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_analysis_fallback(best_params, best_score)
            
            return self._basic_analysis_fallback(best_params, best_score)
            
        except Exception as e:
            self._logger.error(f"Error in LLM analysis: {e}")
            return self._basic_analysis_fallback(best_params, best_score)
    
    def _basic_analysis_fallback(self, best_params: Dict[str, float], best_score: float) -> Dict[str, Any]:
        """Basic analysis fallback when LLM fails"""
        return {
            "summary": f"Optimization completed with score {best_score:.4f}",
            "recommendations": ["Test parameters in paper trading", "Monitor performance closely"],
            "confidence": 0.6,
            "parameter_insights": {param: f"Optimized to {value:.4f}" for param, value in best_params.items()},
            "risk_assessment": "Moderate confidence in parameter stability",
            "implementation_notes": ["Gradual rollout recommended", "Monitor for overfitting"]
        }
    
    async def _trigger_periodic_optimizations(self):
        """Trigger periodic optimizations for strategies with sufficient data"""
        try:
            for strategy_name, performance_data in self._strategy_performance.items():
                if (len(performance_data) >= self._optimization_config["min_samples_for_optimization"] and
                    strategy_name not in self._current_optimizations):
                    
                    # Check if optimization is due
                    last_optimization = None
                    if self._optimization_history[strategy_name]:
                        last_optimization = self._optimization_history[strategy_name][-1].timestamp
                    
                    if (last_optimization is None or 
                        (datetime.now() - last_optimization).total_seconds() >= self._optimization_config["optimization_frequency"]):
                        
                        # Create optimization request
                        request = OptimizationRequest(
                            timestamp=datetime.now(),
                            source_agent=self.id.type,
                            message_id=f"periodic_opt_{strategy_name}_{datetime.now().timestamp()}",
                            strategy_name=strategy_name,
                            optimization_algorithm="bayesian",
                            parameters_to_optimize=list(self._parameter_bounds.keys()),
                            current_parameters={param: 0.5 for param in self._parameter_bounds.keys()},  # Default values
                            optimization_objective="sharpe_ratio"
                        )
                        
                        await self._optimization_queue.put(request)
                        self._logger.info(f"Queued periodic optimization for {strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error triggering periodic optimizations: {e}")
    
    async def _publish_optimization_result(self, result: OptimizationResult):
        """Publish optimization result"""
        try:
            # Send to strategy agents for parameter updates
            await self.publish_message(
                result,
                topic_id=TopicId("StrategyGenerationAgent", source=self.id.key)
            )
            
            # Send to notification agents
            await self.publish_message(
                result,
                topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
            )
            
            self._logger.info(f"Published optimization result for {result.strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error publishing optimization result: {e}")
    
    @message_handler
    async def handle_performance_report(self, message: PerformanceReport, ctx: MessageContext) -> None:
        """Handle performance reports for optimization data"""
        try:
            strategy_name = message.strategy_name
            
            # Store performance data for optimization
            performance_entry = {
                "timestamp": message.timestamp,
                "performance_metrics": message.performance_metrics,
                "analysis_summary": message.analysis_summary
            }
            
            self._strategy_performance[strategy_name].append(performance_entry)
            
            self._logger.debug(f"Stored performance data for optimization: {strategy_name}")
            
        except Exception as e:
            self._logger.error(f"Error handling performance report: {e}")
    
    @message_handler
    async def handle_optimization_request(self, message: OptimizationRequest, ctx: MessageContext) -> None:
        """Handle optimization requests"""
        await self._optimization_queue.put(message)
        self._logger.debug(f"Queued optimization request for {message.strategy_name}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Parameter optimization started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Parameter optimization stopped")
        elif command == "optimize_all":
            await self._trigger_periodic_optimizations()
            self._logger.info("Triggered optimization for all eligible strategies")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "optimization":
            updates = message.updates
            
            if "optimization_config" in updates:
                self._optimization_config.update(updates["optimization_config"])
                self._logger.info("Updated optimization configuration")
            
            if "parameter_bounds" in updates:
                self._parameter_bounds.update(updates["parameter_bounds"])
                self._logger.info("Updated parameter bounds")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "ParameterOptimizationAgent",
            "active": self._is_active,
            "queue_size": self._optimization_queue.qsize(),
            "current_optimizations": len(self._current_optimizations),
            "tracked_strategies": len(self._strategy_performance),
            "optimization_config": self._optimization_config,
            "total_optimizations_completed": sum(
                len(history) for history in self._optimization_history.values()
            ),
            "last_update": datetime.now().isoformat()
        }
