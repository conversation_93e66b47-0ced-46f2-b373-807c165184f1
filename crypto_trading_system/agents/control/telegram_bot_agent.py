"""
Telegram Bot Agent

This agent provides a Telegram bot interface for monitoring and controlling
the crypto trading system remotely.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from collections import deque
import re

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    PerformanceAlert,
    PerformanceReport,
    OptimizationResult,
    SystemCommand,
    TelegramMessage,
    ConfigUpdate
)

# Telegram bot imports (would need to install python-telegram-bot)
try:
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
    from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    # Mock classes for development
    class Update:
        pass
    class InlineKeyboardButton:
        def __init__(self, text, callback_data):
            self.text = text
            self.callback_data = callback_data
    class InlineKeyboardMarkup:
        def __init__(self, buttons):
            self.buttons = buttons


@default_subscription
class TelegramBotAgent(RoutedAgent):
    """
    Agent that provides Telegram bot interface for system monitoring and control.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Telegram Bot Agent",
        bot_token: Optional[str] = None
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration
        self._is_active = True
        self._bot_token = bot_token
        self._authorized_users = set()  # Chat IDs of authorized users
        self._admin_users = set()  # Chat IDs of admin users
        
        # Bot state
        self._bot_application = None
        self._message_queue = asyncio.Queue()
        self._alert_history = deque(maxlen=100)
        self._user_sessions = {}  # user_id -> session_data
        
        # Command configuration
        self._commands = {
            "start": "Start bot and show main menu",
            "status": "Show system status",
            "alerts": "Show recent alerts",
            "performance": "Show performance summary",
            "strategies": "List active strategies",
            "optimize": "Trigger parameter optimization",
            "stop_strategy": "Stop a specific strategy",
            "help": "Show available commands"
        }
        
        # System message for LLM
        self._system_message = SystemMessage(
            content="""You are a Telegram Bot Agent that provides a conversational interface for a crypto trading system.

Your responsibilities:
1. Provide clear, concise responses to user queries about system status
2. Format trading data and alerts in user-friendly messages
3. Handle user commands and translate them to system actions
4. Provide helpful explanations of trading concepts and system behavior
5. Maintain security by verifying user authorization
6. Generate appropriate keyboard layouts for interactive responses

When responding to users:
- Use clear, non-technical language when possible
- Provide actionable information and next steps
- Use emojis appropriately to enhance readability
- Keep messages concise but informative
- Always prioritize security and risk management
- Format numbers and percentages clearly

For alerts and notifications:
- Highlight critical information
- Provide context for the alert
- Suggest appropriate actions
- Use appropriate urgency indicators"""
        )
        
        # Initialize bot if token is provided
        if self._bot_token and TELEGRAM_AVAILABLE:
            asyncio.create_task(self._initialize_bot())
        
        # Start message processor
        asyncio.create_task(self._start_message_processor())
    
    async def _initialize_bot(self):
        """Initialize Telegram bot"""
        try:
            if not TELEGRAM_AVAILABLE:
                self._logger.error("Telegram bot libraries not available. Install python-telegram-bot.")
                return
            
            # Create bot application
            self._bot_application = Application.builder().token(self._bot_token).build()
            
            # Add command handlers
            self._bot_application.add_handler(CommandHandler("start", self._handle_start_command))
            self._bot_application.add_handler(CommandHandler("status", self._handle_status_command))
            self._bot_application.add_handler(CommandHandler("alerts", self._handle_alerts_command))
            self._bot_application.add_handler(CommandHandler("performance", self._handle_performance_command))
            self._bot_application.add_handler(CommandHandler("strategies", self._handle_strategies_command))
            self._bot_application.add_handler(CommandHandler("optimize", self._handle_optimize_command))
            self._bot_application.add_handler(CommandHandler("help", self._handle_help_command))
            
            # Add callback query handler for inline keyboards
            self._bot_application.add_handler(CallbackQueryHandler(self._handle_callback_query))
            
            # Add message handler for text messages
            self._bot_application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_text_message))
            
            # Start bot
            await self._bot_application.initialize()
            await self._bot_application.start()
            
            self._logger.info("Telegram bot initialized and started")
            
        except Exception as e:
            self._logger.error(f"Error initializing Telegram bot: {e}")
    
    async def _start_message_processor(self):
        """Start background message processor"""
        while self._is_active:
            try:
                # Process queued messages
                message = await asyncio.wait_for(self._message_queue.get(), timeout=30.0)
                await self._process_queued_message(message)
                self._message_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error in message processor: {e}")
                await asyncio.sleep(10)
    
    async def _process_queued_message(self, message: Dict[str, Any]):
        """Process a queued message for sending"""
        try:
            message_type = message.get("type")
            
            if message_type == "alert":
                await self._send_alert_notification(message["data"])
            elif message_type == "report":
                await self._send_performance_report(message["data"])
            elif message_type == "optimization":
                await self._send_optimization_result(message["data"])
            
        except Exception as e:
            self._logger.error(f"Error processing queued message: {e}")
    
    def _is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized"""
        return user_id in self._authorized_users or user_id in self._admin_users
    
    def _is_admin(self, user_id: int) -> bool:
        """Check if user is admin"""
        return user_id in self._admin_users
    
    async def _handle_start_command(self, update: Update, context) -> None:
        """Handle /start command"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_authorized(user_id):
                await update.message.reply_text(
                    "🚫 You are not authorized to use this bot. Please contact an administrator."
                )
                return
            
            # Create main menu keyboard
            keyboard = [
                [InlineKeyboardButton("📊 System Status", callback_data="status")],
                [InlineKeyboardButton("🚨 Recent Alerts", callback_data="alerts")],
                [InlineKeyboardButton("📈 Performance", callback_data="performance")],
                [InlineKeyboardButton("🎯 Strategies", callback_data="strategies")],
                [InlineKeyboardButton("⚙️ Optimize", callback_data="optimize")],
                [InlineKeyboardButton("❓ Help", callback_data="help")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            welcome_message = """
🤖 **Crypto Trading System Bot**

Welcome! I'm your trading system assistant. Use the buttons below to:

• Monitor system status and performance
• View alerts and notifications  
• Check active strategies
• Trigger optimizations
• Get help and support

What would you like to do?
"""
            
            await update.message.reply_text(welcome_message, reply_markup=reply_markup, parse_mode='Markdown')
            
        except Exception as e:
            self._logger.error(f"Error handling start command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def _handle_status_command(self, update: Update, context) -> None:
        """Handle /status command"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_authorized(user_id):
                await update.message.reply_text("🚫 Not authorized.")
                return
            
            # Get system status (would query actual system)
            status_message = await self._generate_status_message()
            
            await update.message.reply_text(status_message, parse_mode='Markdown')
            
        except Exception as e:
            self._logger.error(f"Error handling status command: {e}")
            await update.message.reply_text("❌ Error retrieving status.")
    
    async def _handle_alerts_command(self, update: Update, context) -> None:
        """Handle /alerts command"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_authorized(user_id):
                await update.message.reply_text("🚫 Not authorized.")
                return
            
            alerts_message = await self._generate_alerts_message()
            
            await update.message.reply_text(alerts_message, parse_mode='Markdown')
            
        except Exception as e:
            self._logger.error(f"Error handling alerts command: {e}")
            await update.message.reply_text("❌ Error retrieving alerts.")
    
    async def _handle_performance_command(self, update: Update, context) -> None:
        """Handle /performance command"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_authorized(user_id):
                await update.message.reply_text("🚫 Not authorized.")
                return
            
            performance_message = await self._generate_performance_message()
            
            await update.message.reply_text(performance_message, parse_mode='Markdown')
            
        except Exception as e:
            self._logger.error(f"Error handling performance command: {e}")
            await update.message.reply_text("❌ Error retrieving performance data.")
    
    async def _handle_strategies_command(self, update: Update, context) -> None:
        """Handle /strategies command"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_authorized(user_id):
                await update.message.reply_text("🚫 Not authorized.")
                return
            
            strategies_message = await self._generate_strategies_message()
            
            await update.message.reply_text(strategies_message, parse_mode='Markdown')
            
        except Exception as e:
            self._logger.error(f"Error handling strategies command: {e}")
            await update.message.reply_text("❌ Error retrieving strategies.")
    
    async def _handle_optimize_command(self, update: Update, context) -> None:
        """Handle /optimize command"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_admin(user_id):
                await update.message.reply_text("🚫 Admin access required for optimization.")
                return
            
            # Trigger optimization
            await self._trigger_optimization()
            
            await update.message.reply_text("⚙️ Parameter optimization triggered for all eligible strategies.")
            
        except Exception as e:
            self._logger.error(f"Error handling optimize command: {e}")
            await update.message.reply_text("❌ Error triggering optimization.")
    
    async def _handle_help_command(self, update: Update, context) -> None:
        """Handle /help command"""
        try:
            help_message = "🤖 **Available Commands:**\n\n"
            
            for command, description in self._commands.items():
                help_message += f"/{command} - {description}\n"
            
            help_message += "\n💡 **Tips:**\n"
            help_message += "• Use inline buttons for easier navigation\n"
            help_message += "• Check alerts regularly for important updates\n"
            help_message += "• Contact admin for authorization issues\n"
            
            await update.message.reply_text(help_message, parse_mode='Markdown')
            
        except Exception as e:
            self._logger.error(f"Error handling help command: {e}")
            await update.message.reply_text("❌ Error displaying help.")
    
    async def _handle_callback_query(self, update: Update, context) -> None:
        """Handle inline keyboard callbacks"""
        try:
            query = update.callback_query
            user_id = query.from_user.id
            
            if not self._is_authorized(user_id):
                await query.answer("🚫 Not authorized.")
                return
            
            await query.answer()  # Acknowledge the callback
            
            callback_data = query.data
            
            if callback_data == "status":
                message = await self._generate_status_message()
                await query.edit_message_text(message, parse_mode='Markdown')
            elif callback_data == "alerts":
                message = await self._generate_alerts_message()
                await query.edit_message_text(message, parse_mode='Markdown')
            elif callback_data == "performance":
                message = await self._generate_performance_message()
                await query.edit_message_text(message, parse_mode='Markdown')
            elif callback_data == "strategies":
                message = await self._generate_strategies_message()
                await query.edit_message_text(message, parse_mode='Markdown')
            elif callback_data == "optimize":
                if self._is_admin(user_id):
                    await self._trigger_optimization()
                    await query.edit_message_text("⚙️ Parameter optimization triggered.")
                else:
                    await query.edit_message_text("🚫 Admin access required.")
            elif callback_data == "help":
                await self._handle_help_command(update, context)
            
        except Exception as e:
            self._logger.error(f"Error handling callback query: {e}")
    
    async def _handle_text_message(self, update: Update, context) -> None:
        """Handle text messages"""
        try:
            user_id = update.effective_user.id
            
            if not self._is_authorized(user_id):
                await update.message.reply_text("🚫 Not authorized.")
                return
            
            text = update.message.text.lower()
            
            # Simple command matching
            if "status" in text:
                await self._handle_status_command(update, context)
            elif "alert" in text:
                await self._handle_alerts_command(update, context)
            elif "performance" in text or "profit" in text:
                await self._handle_performance_command(update, context)
            elif "strategy" in text or "strategies" in text:
                await self._handle_strategies_command(update, context)
            elif "help" in text:
                await self._handle_help_command(update, context)
            else:
                await update.message.reply_text(
                    "🤔 I didn't understand that. Try /help for available commands or use the menu buttons."
                )
            
        except Exception as e:
            self._logger.error(f"Error handling text message: {e}")
    
    async def _generate_status_message(self) -> str:
        """Generate system status message"""
        try:
            # In production, this would query actual system status
            status_message = """
📊 **System Status**

🟢 **Overall Status:** Operational
⏰ **Uptime:** 2d 14h 32m
🔄 **Active Strategies:** 3
💰 **Total Portfolio:** $12,450.67
📈 **Today's P&L:** +$234.12 (*****%)

**Layer Status:**
• Intelligence Collection: 🟢 Active
• Market Analysis: 🟢 Active  
• Strategy Generation: 🟢 Active
• Execution & Risk Mgmt: 🟢 Active
• Control & Interaction: 🟢 Active

**Recent Activity:**
• Last trade: 15 minutes ago
• Last optimization: 2 hours ago
• Last alert: 45 minutes ago

*Updated: {datetime.now().strftime('%H:%M:%S')}*
"""
            return status_message
            
        except Exception as e:
            self._logger.error(f"Error generating status message: {e}")
            return "❌ Error retrieving system status."
    
    async def _generate_alerts_message(self) -> str:
        """Generate alerts summary message"""
        try:
            if not self._alert_history:
                return "✅ **No Recent Alerts**\n\nAll systems operating normally."
            
            alerts_message = "🚨 **Recent Alerts**\n\n"
            
            # Show last 5 alerts
            recent_alerts = list(self._alert_history)[-5:]
            
            for alert in recent_alerts:
                alert_emoji = self._get_alert_emoji(alert.get("level", "info"))
                timestamp = alert.get("timestamp", datetime.now()).strftime('%H:%M')
                strategy = alert.get("strategy", "Unknown")
                message = alert.get("message", "No details")
                
                alerts_message += f"{alert_emoji} **{timestamp}** - {strategy}\n"
                alerts_message += f"   {message}\n\n"
            
            return alerts_message
            
        except Exception as e:
            self._logger.error(f"Error generating alerts message: {e}")
            return "❌ Error retrieving alerts."
    
    async def _generate_performance_message(self) -> str:
        """Generate performance summary message"""
        try:
            # In production, this would query actual performance data
            performance_message = """
📈 **Performance Summary**

**Overall Portfolio:**
💰 Total Value: $12,450.67
📊 Today: +$234.12 (*****%)
📅 This Week: +$567.89 (+4.78%)
📆 This Month: +$1,234.56 (+11.02%)

**Top Performing Strategies:**
🥇 Momentum Strategy: +15.2% (30d)
🥈 Mean Reversion: +8.7% (30d)  
🥉 Arbitrage Bot: +5.3% (30d)

**Risk Metrics:**
📉 Max Drawdown: -3.2%
📊 Sharpe Ratio: 2.14
⚖️ Current Exposure: 67%

**Recent Trades:**
• BTC/USDT: +0.8% (15m ago)
• ETH/USDT: +1.2% (1h ago)
• SOL/USDT: -0.3% (2h ago)

*Updated: {datetime.now().strftime('%H:%M:%S')}*
"""
            return performance_message
            
        except Exception as e:
            self._logger.error(f"Error generating performance message: {e}")
            return "❌ Error retrieving performance data."
    
    async def _generate_strategies_message(self) -> str:
        """Generate strategies summary message"""
        try:
            strategies_message = """
🎯 **Active Strategies**

**Live Trading:**
🟢 Momentum Strategy
   • Status: Active
   • Capital: $4,000
   • 24h P&L: +$89.45 (+2.24%)

🟢 Mean Reversion Bot  
   • Status: Active
   • Capital: $3,500
   • 24h P&L: +$67.23 (*****%)

**Paper Trading:**
🟡 Arbitrage Scanner
   • Status: Testing
   • Virtual Capital: $5,000
   • 24h P&L: +$123.67 (+2.47%)

**Optimization Queue:**
⚙️ DCA Strategy - Pending optimization
⚙️ Grid Trading - Parameter tuning

**Strategy Health:**
• All strategies within risk limits
• No emergency stops triggered
• Performance tracking active

*Updated: {datetime.now().strftime('%H:%M:%S')}*
"""
            return strategies_message
            
        except Exception as e:
            self._logger.error(f"Error generating strategies message: {e}")
            return "❌ Error retrieving strategies data."
    
    def _get_alert_emoji(self, level: str) -> str:
        """Get emoji for alert level"""
        emoji_map = {
            "info": "ℹ️",
            "warning": "⚠️", 
            "critical": "🚨",
            "emergency": "🆘"
        }
        return emoji_map.get(level.lower(), "ℹ️")
    
    async def _trigger_optimization(self):
        """Trigger system optimization"""
        try:
            # Send optimization command to system
            command = SystemCommand(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"telegram_optimize_{datetime.now().timestamp()}",
                command="optimize_all",
                parameters={}
            )
            
            await self.publish_message(
                command,
                topic_id=TopicId("ParameterOptimizationAgent", source=self.id.key)
            )
            
        except Exception as e:
            self._logger.error(f"Error triggering optimization: {e}")
    
    async def _send_alert_notification(self, alert: PerformanceAlert):
        """Send alert notification to authorized users"""
        try:
            if not self._bot_application:
                return
            
            alert_emoji = self._get_alert_emoji(alert.alert_level)
            
            message = f"""
{alert_emoji} **ALERT: {alert.strategy_name}**

**Type:** {alert.alert_type}
**Level:** {alert.alert_level.upper()}
**Message:** {alert.message}

**Current Value:** {alert.current_value}
**Threshold:** {alert.threshold_value}

**Recommended Actions:**
"""
            
            for action in alert.recommended_actions:
                message += f"• {action}\n"
            
            message += f"\n*Time: {alert.timestamp.strftime('%H:%M:%S')}*"
            
            # Send to all authorized users
            for user_id in self._authorized_users.union(self._admin_users):
                try:
                    await self._bot_application.bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    self._logger.error(f"Error sending alert to user {user_id}: {e}")
            
        except Exception as e:
            self._logger.error(f"Error sending alert notification: {e}")
    
    async def _send_performance_report(self, report: PerformanceReport):
        """Send performance report to authorized users"""
        try:
            if not self._bot_application:
                return
            
            message = f"""
📊 **Performance Report: {report.strategy_name}**

**Period:** {report.report_period}
**Summary:** {report.analysis_summary}

**Key Insights:**
"""
            
            for insight in report.key_insights[:3]:  # Limit to 3 insights
                message += f"• {insight}\n"
            
            if report.recommendations:
                message += f"\n**Recommendations:**\n"
                for rec in report.recommendations[:2]:  # Limit to 2 recommendations
                    message += f"• {rec}\n"
            
            message += f"\n*Generated: {report.timestamp.strftime('%H:%M:%S')}*"
            
            # Send to authorized users (not all users to avoid spam)
            for user_id in self._admin_users:
                try:
                    await self._bot_application.bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    self._logger.error(f"Error sending report to user {user_id}: {e}")
            
        except Exception as e:
            self._logger.error(f"Error sending performance report: {e}")
    
    async def _send_optimization_result(self, result: OptimizationResult):
        """Send optimization result to admin users"""
        try:
            if not self._bot_application:
                return
            
            message = f"""
⚙️ **Optimization Complete: {result.strategy_name}**

**Algorithm:** {result.optimization_algorithm}
**Performance Improvement:** {result.performance_improvement:.2%}
**Confidence:** {result.confidence_score:.1%}

**Summary:** {result.analysis_summary}

**Top Recommendations:**
"""
            
            for rec in result.recommendations[:2]:  # Limit to 2 recommendations
                message += f"• {rec}\n"
            
            message += f"\n*Completed: {result.timestamp.strftime('%H:%M:%S')}*"
            
            # Send to admin users only
            for user_id in self._admin_users:
                try:
                    await self._bot_application.bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    self._logger.error(f"Error sending optimization result to user {user_id}: {e}")
            
        except Exception as e:
            self._logger.error(f"Error sending optimization result: {e}")
    
    @message_handler
    async def handle_performance_alert(self, message: PerformanceAlert, ctx: MessageContext) -> None:
        """Handle performance alerts"""
        try:
            # Store alert
            self._alert_history.append({
                "timestamp": message.timestamp,
                "strategy": message.strategy_name,
                "level": message.alert_level,
                "message": message.message,
                "type": message.alert_type
            })
            
            # Queue for sending
            await self._message_queue.put({
                "type": "alert",
                "data": message
            })
            
        except Exception as e:
            self._logger.error(f"Error handling performance alert: {e}")
    
    @message_handler
    async def handle_performance_report(self, message: PerformanceReport, ctx: MessageContext) -> None:
        """Handle performance reports"""
        try:
            # Queue for sending (only daily reports to avoid spam)
            if message.report_period == "daily":
                await self._message_queue.put({
                    "type": "report",
                    "data": message
                })
            
        except Exception as e:
            self._logger.error(f"Error handling performance report: {e}")
    
    @message_handler
    async def handle_optimization_result(self, message: OptimizationResult, ctx: MessageContext) -> None:
        """Handle optimization results"""
        try:
            # Queue for sending
            await self._message_queue.put({
                "type": "optimization",
                "data": message
            })
            
        except Exception as e:
            self._logger.error(f"Error handling optimization result: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Telegram bot started")
        elif command == "stop":
            self._is_active = False
            if self._bot_application:
                await self._bot_application.stop()
            self._logger.info("Telegram bot stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "telegram":
            updates = message.updates
            
            if "authorized_users" in updates:
                self._authorized_users.update(updates["authorized_users"])
                self._logger.info("Updated authorized users")
            
            if "admin_users" in updates:
                self._admin_users.update(updates["admin_users"])
                self._logger.info("Updated admin users")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "TelegramBotAgent",
            "active": self._is_active,
            "bot_initialized": self._bot_application is not None,
            "authorized_users": len(self._authorized_users),
            "admin_users": len(self._admin_users),
            "message_queue_size": self._message_queue.qsize(),
            "alert_history_size": len(self._alert_history),
            "telegram_available": TELEGRAM_AVAILABLE,
            "last_update": datetime.now().isoformat()
        }
