"""
Web Intelligence Agent

This agent monitors social media platforms (Twitter, Reddit) and news sources
to collect market sentiment and trending information for cryptocurrency trading.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage,
    AssistantMessage
)

from ...core.messages import (
    WebIntelligenceData,
    SystemCommand,
    ConfigUpdate
)
from ...core.tools import TwitterAPITool


@default_subscription
class WebIntelligenceAgent(RoutedAgent):
    """
    Agent responsible for collecting and analyzing web intelligence
    from social media and news sources.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Web Intelligence Collection Agent",
        twitter_config: Optional[Dict[str, str]] = None
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize tools
        self._twitter_tool = None
        if twitter_config:
            self._twitter_tool = TwitterAPITool(
                api_key=twitter_config["api_key"],
                api_secret=twitter_config["api_secret"],
                bearer_token=twitter_config["bearer_token"]
            )
        
        # Agent configuration
        self._collection_interval = 300  # 5 minutes
        self._is_active = True
        self._monitored_keywords = [
            "bitcoin", "btc", "ethereum", "eth", "crypto", "defi", "nft",
            "airdrop", "meme", "pump", "dump", "moon", "bearish", "bullish"
        ]
        
        # System message for LLM interactions
        self._system_message = SystemMessage(
            content="""You are a Web Intelligence Agent specialized in analyzing cryptocurrency-related social media content and news.

Your responsibilities:
1. Monitor Twitter, Reddit, and news sources for crypto-related content
2. Analyze sentiment and extract key insights
3. Identify trending topics and emerging narratives
4. Detect potential market-moving events or rumors
5. Assess the credibility and impact potential of information

When analyzing content, consider:
- Sentiment (bullish, bearish, neutral)
- Engagement metrics (likes, retweets, comments)
- Source credibility and influence
- Potential market impact
- Trending keywords and hashtags

Provide structured analysis with confidence scores and actionable insights."""
        )
        
        # Start background collection task
        asyncio.create_task(self._start_collection_loop())
    
    async def _start_collection_loop(self):
        """Start the continuous data collection loop"""
        while self._is_active:
            try:
                await self._collect_web_intelligence()
                await asyncio.sleep(self._collection_interval)
            except Exception as e:
                self._logger.error(f"Error in collection loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _collect_web_intelligence(self):
        """Collect intelligence from web sources"""
        intelligence_data = []
        
        # Collect Twitter data
        if self._twitter_tool:
            twitter_data = await self._collect_twitter_data()
            intelligence_data.extend(twitter_data)
        
        # Collect Reddit data (placeholder - implement Reddit API)
        reddit_data = await self._collect_reddit_data()
        intelligence_data.extend(reddit_data)
        
        # Collect news data (placeholder - implement news API)
        news_data = await self._collect_news_data()
        intelligence_data.extend(news_data)
        
        # Process and publish collected data
        for data in intelligence_data:
            await self._process_and_publish_data(data)
    
    async def _collect_twitter_data(self) -> List[Dict[str, Any]]:
        """Collect data from Twitter"""
        try:
            twitter_results = []
            
            for keyword in self._monitored_keywords:
                query = f"{keyword} crypto -is:retweet lang:en"
                result = await self._twitter_tool.run_json({
                    "query": query,
                    "max_results": 50
                })
                
                if "tweets" in result:
                    for tweet in result["tweets"]:
                        twitter_results.append({
                            "platform": "twitter",
                            "content": tweet["text"],
                            "sentiment_score": tweet["sentiment"],
                            "engagement_metrics": tweet["metrics"],
                            "keywords": tweet["keywords"],
                            "created_at": tweet["created_at"],
                            "tweet_id": tweet["id"]
                        })
            
            return twitter_results
            
        except Exception as e:
            self._logger.error(f"Error collecting Twitter data: {e}")
            return []
    
    async def _collect_reddit_data(self) -> List[Dict[str, Any]]:
        """Collect data from Reddit (placeholder implementation)"""
        # TODO: Implement Reddit API integration
        return []
    
    async def _collect_news_data(self) -> List[Dict[str, Any]]:
        """Collect data from news sources (placeholder implementation)"""
        # TODO: Implement news API integration
        return []
    
    async def _process_and_publish_data(self, raw_data: Dict[str, Any]):
        """Process raw data and publish intelligence message"""
        try:
            # Analyze content with LLM
            analysis = await self._analyze_content_with_llm(raw_data)
            
            # Create intelligence message
            intelligence_msg = WebIntelligenceData(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"web_intel_{datetime.now().timestamp()}",
                platform=raw_data["platform"],
                content=raw_data["content"],
                sentiment_score=analysis.get("sentiment_score", raw_data.get("sentiment_score", 0.0)),
                engagement_metrics=raw_data.get("engagement_metrics", {}),
                keywords=analysis.get("keywords", raw_data.get("keywords", [])),
                confidence_score=analysis.get("confidence_score", 0.5)
            )
            
            # Publish to trend aggregation topic
            await self.publish_message(
                intelligence_msg,
                topic_id=TopicId("TrendAggregationAgent", source=self.id.key)
            )
            
            self._logger.info(f"Published intelligence data from {raw_data['platform']}")
            
        except Exception as e:
            self._logger.error(f"Error processing data: {e}")
    
    async def _analyze_content_with_llm(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content using LLM for deeper insights"""
        try:
            content = raw_data["content"]
            platform = raw_data["platform"]
            
            user_message = UserMessage(
                content=f"""Analyze this {platform} content for cryptocurrency trading insights:

Content: "{content}"

Provide analysis in JSON format with:
1. sentiment_score: float (-1.0 to 1.0, where -1 is very bearish, 1 is very bullish)
2. confidence_score: float (0.0 to 1.0, confidence in the analysis)
3. keywords: list of relevant crypto keywords found
4. market_impact: string (low/medium/high potential market impact)
5. credibility: string (low/medium/high source credibility)
6. key_insights: list of key insights or signals
7. recommended_action: string (monitor/investigate/alert)

Focus on actionable trading intelligence and market sentiment.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            # Parse LLM response
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    # Fallback to basic analysis
                    return self._basic_content_analysis(content)
            
            return self._basic_content_analysis(content)
            
        except Exception as e:
            self._logger.error(f"Error in LLM analysis: {e}")
            return self._basic_content_analysis(raw_data["content"])
    
    def _basic_content_analysis(self, content: str) -> Dict[str, Any]:
        """Basic content analysis fallback"""
        content_lower = content.lower()
        
        # Simple sentiment analysis
        bullish_words = ["moon", "pump", "bullish", "buy", "long", "up", "gain", "rocket"]
        bearish_words = ["dump", "bearish", "sell", "short", "down", "crash", "loss"]
        
        bullish_count = sum(1 for word in bullish_words if word in content_lower)
        bearish_count = sum(1 for word in bearish_words if word in content_lower)
        
        if bullish_count + bearish_count == 0:
            sentiment_score = 0.0
        else:
            sentiment_score = (bullish_count - bearish_count) / (bullish_count + bearish_count)
        
        # Extract keywords
        keywords = []
        for keyword in self._monitored_keywords:
            if keyword in content_lower:
                keywords.append(keyword)
        
        return {
            "sentiment_score": sentiment_score,
            "confidence_score": 0.3,  # Low confidence for basic analysis
            "keywords": keywords,
            "market_impact": "low",
            "credibility": "medium",
            "key_insights": [],
            "recommended_action": "monitor"
        }
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Web intelligence collection started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Web intelligence collection stopped")
        elif command == "status":
            status = {
                "active": self._is_active,
                "collection_interval": self._collection_interval,
                "monitored_keywords": len(self._monitored_keywords),
                "twitter_enabled": self._twitter_tool is not None
            }
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "web_intelligence":
            updates = message.updates
            
            if "collection_interval" in updates:
                self._collection_interval = updates["collection_interval"]
                self._logger.info(f"Updated collection interval to {self._collection_interval} seconds")
            
            if "monitored_keywords" in updates:
                self._monitored_keywords = updates["monitored_keywords"]
                self._logger.info(f"Updated monitored keywords: {self._monitored_keywords}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "WebIntelligenceAgent",
            "active": self._is_active,
            "collection_interval": self._collection_interval,
            "monitored_keywords_count": len(self._monitored_keywords),
            "twitter_enabled": self._twitter_tool is not None,
            "last_update": datetime.now().isoformat()
        }
