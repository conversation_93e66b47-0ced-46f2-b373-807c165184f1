"""
Whale Tracking Agent

This agent monitors large on-chain transactions and whale activity
to identify potential market-moving events and trading opportunities.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    WhaleActivityData,
    SystemCommand,
    ConfigUpdate
)
from ...core.tools import OnChainDataTool


@default_subscription
class WhaleTrackingAgent(RoutedAgent):
    """
    Agent responsible for tracking whale transactions and large on-chain movements.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Whale Activity Tracking Agent",
        etherscan_api_key: Optional[str] = None
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize on-chain data tool
        self._onchain_tool = None
        if etherscan_api_key:
            self._onchain_tool = OnChainDataTool(etherscan_api_key)
        
        # Agent configuration
        self._monitoring_interval = 180  # 3 minutes
        self._is_active = True
        self._whale_threshold_usd = 1000000  # $1M minimum for whale classification
        self._known_whale_addresses = set()
        self._tracked_tokens = [
            "ETH", "BTC", "USDT", "USDC", "BNB", "ADA", "SOL", "DOT", "MATIC"
        ]
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Whale Tracking Agent specialized in analyzing large cryptocurrency transactions and whale behavior.

Your responsibilities:
1. Monitor large on-chain transactions (>$1M USD value)
2. Identify whale addresses and track their activity patterns
3. Analyze transaction patterns for market impact potential
4. Detect unusual whale behavior that might signal market moves
5. Assess the significance of whale movements for trading strategies

When analyzing whale transactions, consider:
- Transaction size and frequency
- Source and destination addresses (exchange vs. wallet)
- Timing patterns and market context
- Historical behavior of the whale address
- Potential market impact and sentiment implications

Provide structured analysis with confidence scores and trading implications."""
        )
        
        # Start background monitoring
        asyncio.create_task(self._start_monitoring_loop())
    
    async def _start_monitoring_loop(self):
        """Start the continuous whale monitoring loop"""
        while self._is_active:
            try:
                await self._monitor_whale_activity()
                await asyncio.sleep(self._monitoring_interval)
            except Exception as e:
                self._logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _monitor_whale_activity(self):
        """Monitor whale activity across different chains and tokens"""
        try:
            # Monitor Ethereum whale transactions
            if self._onchain_tool:
                eth_whales = await self._monitor_ethereum_whales()
                for whale_data in eth_whales:
                    await self._process_whale_transaction(whale_data)
            
            # TODO: Add monitoring for other chains (BSC, Polygon, etc.)
            
        except Exception as e:
            self._logger.error(f"Error monitoring whale activity: {e}")
    
    async def _monitor_ethereum_whales(self) -> List[Dict[str, Any]]:
        """Monitor whale transactions on Ethereum"""
        try:
            result = await self._onchain_tool.run_json({
                "type": "whale_transactions",
                "min_value": self._whale_threshold_usd
            })
            
            if "whale_transactions" in result:
                whale_transactions = []
                for tx in result["whale_transactions"]:
                    # Analyze transaction with LLM
                    analysis = await self._analyze_whale_transaction(tx)
                    
                    whale_data = {
                        "transaction_hash": tx["hash"],
                        "from_address": tx["from"],
                        "to_address": tx["to"],
                        "token_symbol": "ETH",
                        "amount": tx["value_eth"],
                        "usd_value": tx["value_usd"],
                        "chain": "ethereum",
                        "transaction_type": analysis.get("transaction_type", "transfer"),
                        "timestamp": tx["timestamp"],
                        "analysis": analysis
                    }
                    whale_transactions.append(whale_data)
                
                return whale_transactions
            
            return []
            
        except Exception as e:
            self._logger.error(f"Error monitoring Ethereum whales: {e}")
            return []
    
    async def _analyze_whale_transaction(self, transaction: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze whale transaction using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Analyze this whale transaction for trading significance:

Transaction Details:
- Hash: {transaction['hash']}
- From: {transaction['from']}
- To: {transaction['to']}
- Value: {transaction['value_eth']} ETH (${transaction['value_usd']:,.2f})
- Timestamp: {transaction['timestamp']}

Provide analysis in JSON format with:
1. transaction_type: string (buy/sell/transfer/accumulation/distribution)
2. market_impact: string (low/medium/high potential impact)
3. whale_behavior: string (accumulating/distributing/moving/unknown)
4. exchange_involvement: boolean (involves known exchange addresses)
5. significance_score: float (0.0 to 1.0, trading significance)
6. confidence_score: float (0.0 to 1.0, confidence in analysis)
7. trading_implications: list of potential trading implications
8. recommended_action: string (monitor/investigate/alert/trade)

Focus on actionable insights for cryptocurrency trading.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_transaction_analysis(transaction)
            
            return self._basic_transaction_analysis(transaction)
            
        except Exception as e:
            self._logger.error(f"Error in whale transaction analysis: {e}")
            return self._basic_transaction_analysis(transaction)
    
    def _basic_transaction_analysis(self, transaction: Dict[str, Any]) -> Dict[str, Any]:
        """Basic transaction analysis fallback"""
        value_usd = transaction.get("value_usd", 0)
        
        # Determine significance based on transaction size
        if value_usd > 10000000:  # $10M+
            significance_score = 0.9
            market_impact = "high"
        elif value_usd > 5000000:  # $5M+
            significance_score = 0.7
            market_impact = "medium"
        else:
            significance_score = 0.4
            market_impact = "low"
        
        return {
            "transaction_type": "transfer",
            "market_impact": market_impact,
            "whale_behavior": "unknown",
            "exchange_involvement": False,
            "significance_score": significance_score,
            "confidence_score": 0.3,
            "trading_implications": ["Large transaction detected"],
            "recommended_action": "monitor"
        }
    
    async def _process_whale_transaction(self, whale_data: Dict[str, Any]):
        """Process and publish whale transaction data"""
        try:
            # Create whale activity message
            whale_msg = WhaleActivityData(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"whale_{whale_data['transaction_hash']}",
                transaction_hash=whale_data["transaction_hash"],
                from_address=whale_data["from_address"],
                to_address=whale_data["to_address"],
                token_symbol=whale_data["token_symbol"],
                amount=whale_data["amount"],
                usd_value=whale_data["usd_value"],
                chain=whale_data["chain"],
                transaction_type=whale_data["transaction_type"]
            )
            
            # Publish to trend aggregation topic
            await self.publish_message(
                whale_msg,
                topic_id=TopicId("TrendAggregationAgent", source=self.id.key)
            )
            
            # If high significance, also alert control systems
            analysis = whale_data.get("analysis", {})
            if analysis.get("significance_score", 0) > 0.7:
                await self.publish_message(
                    whale_msg,
                    topic_id=TopicId("TelegramBotAgent", source=self.id.key)
                )
            
            self._logger.info(
                f"Processed whale transaction: {whale_data['usd_value']:,.2f} USD "
                f"({whale_data['transaction_type']})"
            )
            
        except Exception as e:
            self._logger.error(f"Error processing whale transaction: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Whale tracking started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Whale tracking stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "whale_tracking":
            updates = message.updates
            
            if "monitoring_interval" in updates:
                self._monitoring_interval = updates["monitoring_interval"]
                self._logger.info(f"Updated monitoring interval to {self._monitoring_interval} seconds")
            
            if "whale_threshold_usd" in updates:
                self._whale_threshold_usd = updates["whale_threshold_usd"]
                self._logger.info(f"Updated whale threshold to ${self._whale_threshold_usd:,}")
            
            if "tracked_tokens" in updates:
                self._tracked_tokens = updates["tracked_tokens"]
                self._logger.info(f"Updated tracked tokens: {self._tracked_tokens}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "WhaleTrackingAgent",
            "active": self._is_active,
            "monitoring_interval": self._monitoring_interval,
            "whale_threshold_usd": self._whale_threshold_usd,
            "tracked_tokens": self._tracked_tokens,
            "known_whale_addresses": len(self._known_whale_addresses),
            "onchain_tool_enabled": self._onchain_tool is not None,
            "last_update": datetime.now().isoformat()
        }
