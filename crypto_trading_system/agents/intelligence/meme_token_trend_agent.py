"""
Meme Token Trend Agent

This agent monitors meme token trends, viral content, and social sentiment
to identify potential meme coin opportunities and risks.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import re

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    MemeTokenTrendData,
    SystemCommand,
    ConfigUpdate
)
from ...core.tools import TwitterAPITool


@default_subscription
class MemeTokenTrendAgent(RoutedAgent):
    """
    Agent responsible for tracking meme token trends and viral content.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "Meme Token Trend Agent",
        twitter_config: Optional[Dict[str, str]] = None
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize Twitter tool
        self._twitter_tool = None
        if twitter_config:
            self._twitter_tool = Twitter<PERSON>ITool(
                api_key=twitter_config["api_key"],
                api_secret=twitter_config["api_secret"],
                bearer_token=twitter_config["bearer_token"]
            )
        
        # Agent configuration
        self._monitoring_interval = 120  # 2 minutes (meme trends move fast)
        self._is_active = True
        self._viral_threshold = 1000  # Minimum engagement for viral content
        
        # Meme token patterns and keywords
        self._meme_keywords = [
            "meme", "pepe", "doge", "shib", "floki", "bonk", "wojak",
            "chad", "moon", "diamond hands", "hodl", "ape", "rocket",
            "lambo", "tendies", "stonks", "pump", "gem", "x100", "x1000"
        ]
        
        self._meme_patterns = [
            r'\$[A-Z]{2,10}',  # Token symbols like $PEPE
            r'#[A-Za-z0-9_]+',  # Hashtags
            r'🚀+',  # Rocket emojis
            r'💎+',  # Diamond emojis
            r'🌙+',  # Moon emojis
        ]
        
        # Trending data storage
        self._trending_tokens = {}
        self._viral_content = []
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a Meme Token Trend Agent specialized in analyzing meme cryptocurrency trends and viral content.

Your responsibilities:
1. Monitor social media for meme token mentions and trends
2. Identify viral content and emerging meme narratives
3. Analyze meme token sentiment and community engagement
4. Detect potential pump and dump schemes
5. Assess the longevity and legitimacy of meme token trends

When analyzing meme content, consider:
- Viral potential and engagement metrics
- Community size and authenticity
- Narrative strength and memetic value
- Market timing and context
- Risk factors and red flags
- Historical patterns of similar memes

Provide structured analysis with risk assessments and trading implications."""
        )
        
        # Start background monitoring
        asyncio.create_task(self._start_monitoring_loop())
    
    async def _start_monitoring_loop(self):
        """Start the continuous meme trend monitoring loop"""
        while self._is_active:
            try:
                await self._monitor_meme_trends()
                await asyncio.sleep(self._monitoring_interval)
            except Exception as e:
                self._logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(30)  # Shorter retry for meme trends
    
    async def _monitor_meme_trends(self):
        """Monitor meme token trends across platforms"""
        try:
            # Monitor Twitter for meme content
            if self._twitter_tool:
                twitter_memes = await self._monitor_twitter_memes()
                for meme_data in twitter_memes:
                    await self._process_meme_content(meme_data)
            
            # TODO: Add Reddit monitoring for r/CryptoMoonShots, r/SatoshiStreetBets
            # TODO: Add Telegram channel monitoring
            # TODO: Add TikTok trend monitoring
            
        except Exception as e:
            self._logger.error(f"Error monitoring meme trends: {e}")
    
    async def _monitor_twitter_memes(self) -> List[Dict[str, Any]]:
        """Monitor Twitter for meme token content"""
        try:
            meme_content = []
            
            # Search for meme-related content
            for keyword in self._meme_keywords[:5]:  # Limit API calls
                query = f"{keyword} crypto OR {keyword} token -is:retweet lang:en"
                result = await self._twitter_tool.run_json({
                    "query": query,
                    "max_results": 30
                })
                
                if "tweets" in result:
                    for tweet in result["tweets"]:
                        # Extract potential token symbols
                        token_symbols = self._extract_token_symbols(tweet["text"])
                        
                        if token_symbols or self._is_meme_content(tweet["text"]):
                            meme_data = {
                                "platform": "twitter",
                                "content": tweet["text"],
                                "engagement": tweet["metrics"],
                                "token_symbols": token_symbols,
                                "created_at": tweet["created_at"],
                                "tweet_id": tweet["id"],
                                "meme_score": self._calculate_meme_score(tweet)
                            }
                            meme_content.append(meme_data)
            
            return meme_content
            
        except Exception as e:
            self._logger.error(f"Error monitoring Twitter memes: {e}")
            return []
    
    def _extract_token_symbols(self, text: str) -> List[str]:
        """Extract potential token symbols from text"""
        symbols = []
        
        # Find $SYMBOL patterns
        symbol_matches = re.findall(r'\$([A-Z]{2,10})', text.upper())
        symbols.extend(symbol_matches)
        
        # Find common meme token names
        text_upper = text.upper()
        common_meme_tokens = ["PEPE", "DOGE", "SHIB", "FLOKI", "BONK", "WOJAK"]
        for token in common_meme_tokens:
            if token in text_upper:
                symbols.append(token)
        
        return list(set(symbols))  # Remove duplicates
    
    def _is_meme_content(self, text: str) -> bool:
        """Check if content is meme-related"""
        text_lower = text.lower()
        
        # Check for meme keywords
        meme_keyword_count = sum(1 for keyword in self._meme_keywords if keyword in text_lower)
        
        # Check for meme patterns (emojis, etc.)
        pattern_count = 0
        for pattern in self._meme_patterns:
            if re.search(pattern, text):
                pattern_count += 1
        
        return meme_keyword_count >= 2 or pattern_count >= 1
    
    def _calculate_meme_score(self, tweet: Dict[str, Any]) -> float:
        """Calculate meme potential score"""
        metrics = tweet.get("metrics", {})
        text = tweet.get("text", "")
        
        # Base engagement score
        likes = metrics.get("like_count", 0)
        retweets = metrics.get("retweet_count", 0)
        replies = metrics.get("reply_count", 0)
        
        engagement_score = (likes * 1 + retweets * 3 + replies * 2) / 100
        
        # Meme content multipliers
        text_lower = text.lower()
        meme_multiplier = 1.0
        
        # Rocket emojis boost
        if "🚀" in text:
            meme_multiplier += 0.3
        
        # Diamond hands boost
        if "💎" in text or "diamond" in text_lower:
            meme_multiplier += 0.2
        
        # Moon references boost
        if "🌙" in text or "moon" in text_lower:
            meme_multiplier += 0.2
        
        # Multiple token symbols boost
        token_count = len(self._extract_token_symbols(text))
        if token_count > 1:
            meme_multiplier += 0.1 * token_count
        
        return min(engagement_score * meme_multiplier, 10.0)  # Cap at 10
    
    async def _process_meme_content(self, meme_data: Dict[str, Any]):
        """Process and analyze meme content"""
        try:
            # Skip low-engagement content
            if meme_data["meme_score"] < 1.0:
                return
            
            # Analyze with LLM for deeper insights
            analysis = await self._analyze_meme_content(meme_data)
            
            # Update trending tokens
            for token_symbol in meme_data["token_symbols"]:
                self._update_trending_token(token_symbol, meme_data, analysis)
            
            # Publish if significant
            if analysis.get("viral_potential", 0) > 0.6:
                await self._publish_meme_trend_data(meme_data, analysis)
            
        except Exception as e:
            self._logger.error(f"Error processing meme content: {e}")
    
    async def _analyze_meme_content(self, meme_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze meme content using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Analyze this meme cryptocurrency content for trading significance:

Platform: {meme_data['platform']}
Content: "{meme_data['content']}"
Engagement: {meme_data['engagement']}
Token Symbols: {meme_data['token_symbols']}
Meme Score: {meme_data['meme_score']:.2f}

Provide analysis in JSON format with:
1. viral_potential: float (0.0 to 1.0, potential to go viral)
2. sentiment: string (bullish/bearish/neutral)
3. authenticity: string (genuine/suspicious/pump_dump)
4. community_strength: string (weak/moderate/strong)
5. narrative_quality: string (poor/average/excellent)
6. risk_level: string (low/medium/high/extreme)
7. confidence_score: float (0.0 to 1.0, confidence in analysis)
8. trading_implications: list of potential trading implications
9. recommended_action: string (ignore/monitor/investigate/alert)
10. key_insights: list of key insights about the meme trend

Focus on identifying legitimate opportunities vs pump and dump schemes.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_meme_analysis(meme_data)
            
            return self._basic_meme_analysis(meme_data)
            
        except Exception as e:
            self._logger.error(f"Error in meme content analysis: {e}")
            return self._basic_meme_analysis(meme_data)
    
    def _basic_meme_analysis(self, meme_data: Dict[str, Any]) -> Dict[str, Any]:
        """Basic meme analysis fallback"""
        meme_score = meme_data.get("meme_score", 0)
        
        if meme_score > 5:
            viral_potential = 0.8
            risk_level = "high"
        elif meme_score > 2:
            viral_potential = 0.5
            risk_level = "medium"
        else:
            viral_potential = 0.2
            risk_level = "low"
        
        return {
            "viral_potential": viral_potential,
            "sentiment": "neutral",
            "authenticity": "suspicious",
            "community_strength": "moderate",
            "narrative_quality": "average",
            "risk_level": risk_level,
            "confidence_score": 0.3,
            "trading_implications": ["Meme content detected"],
            "recommended_action": "monitor",
            "key_insights": [f"Meme score: {meme_score:.2f}"]
        }
    
    def _update_trending_token(self, token_symbol: str, meme_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Update trending token data"""
        if token_symbol not in self._trending_tokens:
            self._trending_tokens[token_symbol] = {
                "symbol": token_symbol,
                "first_seen": datetime.now(),
                "mention_count": 0,
                "total_engagement": 0,
                "sentiment_scores": [],
                "viral_scores": [],
                "risk_flags": []
            }
        
        token_data = self._trending_tokens[token_symbol]
        token_data["mention_count"] += 1
        token_data["total_engagement"] += sum(meme_data["engagement"].values())
        token_data["sentiment_scores"].append(analysis.get("viral_potential", 0))
        token_data["viral_scores"].append(meme_data["meme_score"])
        
        if analysis.get("authenticity") == "pump_dump":
            token_data["risk_flags"].append("pump_dump_suspected")
        
        token_data["last_seen"] = datetime.now()
    
    async def _publish_meme_trend_data(self, meme_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Publish meme trend data"""
        try:
            # Create meme trend message
            meme_msg = MemeTokenTrendData(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"meme_{meme_data.get('tweet_id', datetime.now().timestamp())}",
                token_symbols=meme_data["token_symbols"],
                platform=meme_data["platform"],
                content_snippet=meme_data["content"][:200],  # Truncate for message
                viral_score=analysis.get("viral_potential", 0),
                engagement_metrics=meme_data["engagement"],
                sentiment=analysis.get("sentiment", "neutral")
            )
            
            # Publish to trend aggregation topic
            await self.publish_message(
                meme_msg,
                topic_id=TopicId("TrendAggregationAgent", source=self.id.key)
            )
            
            # If high risk, alert control systems
            if analysis.get("risk_level") in ["high", "extreme"]:
                await self.publish_message(
                    meme_msg,
                    topic_id=TopicId("TelegramBotAgent", source=self.id.key)
                )
            
            self._logger.info(
                f"Published meme trend data for {meme_data['token_symbols']}: "
                f"viral_potential={analysis.get('viral_potential', 0):.2f}"
            )
            
        except Exception as e:
            self._logger.error(f"Error publishing meme trend data: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("Meme trend monitoring started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("Meme trend monitoring stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_trending":
            self._trending_tokens.clear()
            self._viral_content.clear()
            self._logger.info("Cleared trending data")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "meme_trends":
            updates = message.updates
            
            if "monitoring_interval" in updates:
                self._monitoring_interval = updates["monitoring_interval"]
                self._logger.info(f"Updated monitoring interval to {self._monitoring_interval} seconds")
            
            if "viral_threshold" in updates:
                self._viral_threshold = updates["viral_threshold"]
                self._logger.info(f"Updated viral threshold to {self._viral_threshold}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "MemeTokenTrendAgent",
            "active": self._is_active,
            "monitoring_interval": self._monitoring_interval,
            "viral_threshold": self._viral_threshold,
            "trending_tokens": len(self._trending_tokens),
            "twitter_enabled": self._twitter_tool is not None,
            "last_update": datetime.now().isoformat()
        }
