"""
TVL Monitoring Agent

This agent monitors Total Value Locked (TVL) changes across DeFi protocols
to identify trending chains and protocols for trading opportunities.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from autogen_core import (
    RoutedAgent,
    MessageContext,
    TopicId,
    message_handler,
    default_subscription
)
from autogen_core.models import (
    ChatCompletionClient,
    SystemMessage,
    UserMessage
)

from ...core.messages import (
    TVLData,
    SystemCommand,
    ConfigUpdate
)
from ...core.tools import DeFiDataTool


@default_subscription
class TVLMonitoringAgent(RoutedAgent):
    """
    Agent responsible for monitoring TVL changes across DeFi protocols and chains.
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        description: str = "TVL Monitoring Agent"
    ):
        super().__init__(description)
        self._model_client = model_client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize DeFi data tool
        self._defi_tool = DeFiDataTool()
        
        # Agent configuration
        self._monitoring_interval = 600  # 10 minutes
        self._is_active = True
        self._tvl_change_threshold = 0.05  # 5% change threshold for alerts
        self._min_tvl_threshold = 10000000  # $10M minimum TVL to track
        
        # Historical data for change calculation
        self._historical_tvl_data = {}
        
        # System message for LLM analysis
        self._system_message = SystemMessage(
            content="""You are a TVL Monitoring Agent specialized in analyzing DeFi protocol Total Value Locked changes and their market implications.

Your responsibilities:
1. Monitor TVL changes across DeFi protocols and chains
2. Identify significant TVL movements and trends
3. Analyze the implications of TVL changes for token prices
4. Detect emerging protocols and hot chains
5. Assess the sustainability and significance of TVL growth/decline

When analyzing TVL data, consider:
- Magnitude and speed of TVL changes
- Protocol category and market position
- Chain distribution and migration patterns
- Market context and external factors
- Historical patterns and seasonality
- Correlation with token price movements

Provide structured analysis with confidence scores and trading implications."""
        )
        
        # Start background monitoring
        asyncio.create_task(self._start_monitoring_loop())
    
    async def _start_monitoring_loop(self):
        """Start the continuous TVL monitoring loop"""
        while self._is_active:
            try:
                await self._monitor_tvl_changes()
                await asyncio.sleep(self._monitoring_interval)
            except Exception as e:
                self._logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _monitor_tvl_changes(self):
        """Monitor TVL changes across DeFi protocols"""
        try:
            # Get current TVL data
            current_data = await self._defi_tool.run_json({"type": "tvl_data"})
            
            if "protocols" in current_data:
                for protocol in current_data["protocols"]:
                    if protocol["tvl"] >= self._min_tvl_threshold:
                        await self._process_protocol_tvl(protocol)
            
        except Exception as e:
            self._logger.error(f"Error monitoring TVL changes: {e}")
    
    async def _process_protocol_tvl(self, protocol_data: Dict[str, Any]):
        """Process individual protocol TVL data"""
        try:
            protocol_name = protocol_data["name"]
            current_tvl = protocol_data["tvl"]
            
            # Calculate changes from historical data
            historical_data = self._historical_tvl_data.get(protocol_name, {})
            
            # Check for significant changes
            significant_change = False
            change_analysis = {}
            
            if "tvl" in historical_data:
                previous_tvl = historical_data["tvl"]
                if previous_tvl > 0:
                    change_24h = (current_tvl - previous_tvl) / previous_tvl
                    if abs(change_24h) >= self._tvl_change_threshold:
                        significant_change = True
                        change_analysis = await self._analyze_tvl_change(protocol_data, change_24h)
            
            # Update historical data
            self._historical_tvl_data[protocol_name] = {
                "tvl": current_tvl,
                "timestamp": datetime.now(),
                "data": protocol_data
            }
            
            # Publish data if significant or first time
            if significant_change or protocol_name not in self._historical_tvl_data:
                await self._publish_tvl_data(protocol_data, change_analysis)
            
        except Exception as e:
            self._logger.error(f"Error processing protocol TVL: {e}")
    
    async def _analyze_tvl_change(self, protocol_data: Dict[str, Any], change_24h: float) -> Dict[str, Any]:
        """Analyze TVL change using LLM"""
        try:
            user_message = UserMessage(
                content=f"""Analyze this DeFi protocol TVL change for trading significance:

Protocol Details:
- Name: {protocol_data['name']}
- Symbol: {protocol_data.get('symbol', 'N/A')}
- Current TVL: ${protocol_data['tvl']:,.2f}
- 24h Change: {change_24h:.2%}
- Category: {protocol_data.get('category', 'Unknown')}
- Chains: {', '.join(protocol_data.get('chains', []))}

Provide analysis in JSON format with:
1. change_significance: string (low/medium/high significance)
2. trend_direction: string (bullish/bearish/neutral)
3. market_impact: string (low/medium/high potential market impact)
4. sustainability: string (sustainable/temporary/uncertain)
5. confidence_score: float (0.0 to 1.0, confidence in analysis)
6. trading_implications: list of potential trading implications
7. recommended_tokens: list of tokens that might be affected
8. recommended_action: string (monitor/investigate/alert/trade)
9. key_insights: list of key insights about the TVL change

Focus on actionable insights for cryptocurrency trading and DeFi opportunities.""",
                source="system"
            )
            
            response = await self._model_client.create(
                messages=[self._system_message, user_message]
            )
            
            if isinstance(response.content, str):
                try:
                    analysis = json.loads(response.content)
                    return analysis
                except json.JSONDecodeError:
                    return self._basic_tvl_analysis(change_24h)
            
            return self._basic_tvl_analysis(change_24h)
            
        except Exception as e:
            self._logger.error(f"Error in TVL change analysis: {e}")
            return self._basic_tvl_analysis(change_24h)
    
    def _basic_tvl_analysis(self, change_24h: float) -> Dict[str, Any]:
        """Basic TVL analysis fallback"""
        if abs(change_24h) > 0.2:  # >20% change
            significance = "high"
            market_impact = "high"
        elif abs(change_24h) > 0.1:  # >10% change
            significance = "medium"
            market_impact = "medium"
        else:
            significance = "low"
            market_impact = "low"
        
        trend_direction = "bullish" if change_24h > 0 else "bearish"
        
        return {
            "change_significance": significance,
            "trend_direction": trend_direction,
            "market_impact": market_impact,
            "sustainability": "uncertain",
            "confidence_score": 0.3,
            "trading_implications": [f"TVL {trend_direction} trend detected"],
            "recommended_tokens": [],
            "recommended_action": "monitor",
            "key_insights": [f"{change_24h:.2%} TVL change in 24h"]
        }
    
    async def _publish_tvl_data(self, protocol_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Publish TVL data message"""
        try:
            # Create TVL data message
            tvl_msg = TVLData(
                timestamp=datetime.now(),
                source_agent=self.id.type,
                message_id=f"tvl_{protocol_data['name']}_{datetime.now().timestamp()}",
                protocol_name=protocol_data["name"],
                chain=", ".join(protocol_data.get("chains", ["Unknown"])),
                current_tvl=protocol_data["tvl"],
                tvl_change_24h=protocol_data.get("change_1d", 0),
                tvl_change_7d=protocol_data.get("change_7d", 0),
                category=protocol_data.get("category", "Unknown")
            )
            
            # Publish to trend aggregation topic
            await self.publish_message(
                tvl_msg,
                topic_id=TopicId("TrendAggregationAgent", source=self.id.key)
            )
            
            # If high significance, also alert control systems
            if analysis.get("change_significance") == "high":
                await self.publish_message(
                    tvl_msg,
                    topic_id=TopicId("SlackNotificationAgent", source=self.id.key)
                )
            
            self._logger.info(
                f"Published TVL data for {protocol_data['name']}: "
                f"${protocol_data['tvl']:,.2f} ({analysis.get('trend_direction', 'unknown')})"
            )
            
        except Exception as e:
            self._logger.error(f"Error publishing TVL data: {e}")
    
    @message_handler
    async def handle_system_command(self, message: SystemCommand, ctx: MessageContext) -> None:
        """Handle system commands"""
        command = message.command
        
        if command == "start":
            self._is_active = True
            self._logger.info("TVL monitoring started")
        elif command == "stop":
            self._is_active = False
            self._logger.info("TVL monitoring stopped")
        elif command == "status":
            status = await self.get_status()
            self._logger.info(f"Status: {status}")
        elif command == "clear_history":
            self._historical_tvl_data.clear()
            self._logger.info("Cleared historical TVL data")
    
    @message_handler
    async def handle_config_update(self, message: ConfigUpdate, ctx: MessageContext) -> None:
        """Handle configuration updates"""
        if message.config_section == "tvl_monitoring":
            updates = message.updates
            
            if "monitoring_interval" in updates:
                self._monitoring_interval = updates["monitoring_interval"]
                self._logger.info(f"Updated monitoring interval to {self._monitoring_interval} seconds")
            
            if "tvl_change_threshold" in updates:
                self._tvl_change_threshold = updates["tvl_change_threshold"]
                self._logger.info(f"Updated TVL change threshold to {self._tvl_change_threshold:.2%}")
            
            if "min_tvl_threshold" in updates:
                self._min_tvl_threshold = updates["min_tvl_threshold"]
                self._logger.info(f"Updated minimum TVL threshold to ${self._min_tvl_threshold:,}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "TVLMonitoringAgent",
            "active": self._is_active,
            "monitoring_interval": self._monitoring_interval,
            "tvl_change_threshold": self._tvl_change_threshold,
            "min_tvl_threshold": self._min_tvl_threshold,
            "tracked_protocols": len(self._historical_tvl_data),
            "last_update": datetime.now().isoformat()
        }
