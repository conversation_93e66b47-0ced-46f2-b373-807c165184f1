"""
Main Application Entry Point for Multi-Agent Crypto Trading System
Initializes and runs the complete system with database integration
"""

import asyncio
import logging
import signal
import sys
from typing import Optional
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crypto_trading_system.core.system_manager import SystemManager
from crypto_trading_system.config.system_config import (
    get_system_config, get_development_config, get_production_config, validate_config
)


class CryptoTradingSystemApp:
    """Main application class for the crypto trading system"""
    
    def __init__(self, environment: str = "development"):
        """Initialize the application"""
        self.environment = environment
        self.system_manager: Optional[SystemManager] = None
        self.logger = self._setup_logging()
        
        # Load configuration based on environment
        self.config = self._load_config()
        
        # Validate configuration
        validate_config(self.config)
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('crypto_trading_system.log')
            ]
        )
        return logging.getLogger(__name__)
    
    def _load_config(self) -> dict:
        """Load configuration based on environment"""
        if self.environment == "production":
            return get_production_config()
        elif self.environment == "development":
            return get_development_config()
        else:
            return get_system_config()
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """Initialize the system"""
        try:
            self.logger.info(f"Initializing Crypto Trading System in {self.environment} mode...")
            
            # Check required environment variables
            self._check_environment_variables()
            
            # Create system manager
            self.system_manager = SystemManager(self.config)
            
            # Initialize system
            await self.system_manager.initialize()
            
            self.logger.info("System initialization completed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize system: {e}")
            raise
    
    def _check_environment_variables(self):
        """Check that required environment variables are set"""
        required_vars = []
        
        # Check Supabase configuration
        if not self.config.get("supabase_key"):
            required_vars.append("SUPABASE_ANON_KEY")
        
        # Check model API keys
        for model_name, model_config in self.config["model_configs"].items():
            provider = model_config.get("provider")
            if provider == "anthropic" and not model_config.get("api_key"):
                required_vars.append("ANTHROPIC_API_KEY")
            elif provider == "google" and not model_config.get("api_key"):
                required_vars.append("GOOGLE_API_KEY")
            elif provider == "openai" and not model_config.get("api_key"):
                required_vars.append("OPENAI_API_KEY")
        
        if required_vars:
            self.logger.warning(f"Missing environment variables: {', '.join(required_vars)}")
            self.logger.warning("Some features may not work without proper API keys")
    
    async def start(self):
        """Start the system"""
        try:
            if not self.system_manager:
                raise RuntimeError("System not initialized. Call initialize() first.")
            
            self.logger.info("Starting Crypto Trading System...")
            
            # Start the system
            await self.system_manager.start()
            
            self.logger.info("System started successfully")
            self.logger.info("System is now running. Press Ctrl+C to stop.")
            
            # Print system status
            await self._print_system_status()
            
        except Exception as e:
            self.logger.error(f"Failed to start system: {e}")
            raise
    
    async def _print_system_status(self):
        """Print current system status"""
        if self.system_manager:
            status = self.system_manager.get_system_status()
            
            self.logger.info("=== SYSTEM STATUS ===")
            self.logger.info(f"Initialized: {status['initialized']}")
            self.logger.info(f"Running: {status['running']}")
            self.logger.info(f"Agent Count: {status['agent_count']}")
            self.logger.info(f"Database Connected: {status['database_connected']}")
            self.logger.info(f"Runtime Active: {status['runtime_active']}")
            
            self.logger.info("=== ACTIVE AGENTS ===")
            for agent_name, agent_info in status['agents'].items():
                self.logger.info(f"  {agent_name}: {agent_info['layer']} ({agent_info['agent_type']})")
            
            self.logger.info("=====================")
    
    async def run(self):
        """Run the system indefinitely"""
        try:
            # Keep the system running
            while True:
                await asyncio.sleep(60)  # Check every minute
                
                # Optionally print periodic status updates
                if self.environment == "development":
                    await self._print_periodic_status()
                
        except asyncio.CancelledError:
            self.logger.info("System run loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in run loop: {e}")
            raise
    
    async def _print_periodic_status(self):
        """Print periodic status updates in development mode"""
        if self.system_manager:
            db_manager = self.system_manager.get_database_manager()
            if db_manager:
                try:
                    # Get recent intelligence data
                    intelligence = await db_manager.get_recent_intelligence(limit=5)
                    
                    # Get active deployments
                    deployments = await db_manager.get_active_deployments()
                    
                    self.logger.info(f"Recent intelligence entries: {sum(len(data) for data in intelligence.values())}")
                    self.logger.info(f"Active deployments: {len(deployments)}")
                    
                except Exception as e:
                    self.logger.debug(f"Error getting periodic status: {e}")
    
    async def shutdown(self):
        """Shutdown the system gracefully"""
        try:
            self.logger.info("Shutting down Crypto Trading System...")
            
            if self.system_manager:
                await self.system_manager.cleanup()
            
            self.logger.info("System shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
        finally:
            # Force exit if needed
            sys.exit(0)
    
    async def run_system(self):
        """Complete system lifecycle: initialize, start, run, shutdown"""
        try:
            await self.initialize()
            await self.start()
            await self.run()
            
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"System error: {e}")
            raise
        finally:
            await self.shutdown()


async def main():
    """Main entry point"""
    # Get environment from command line or environment variable
    environment = os.getenv("ENVIRONMENT", "development")
    if len(sys.argv) > 1:
        environment = sys.argv[1]
    
    # Create and run the application
    app = CryptoTradingSystemApp(environment=environment)
    await app.run_system()


def cli_main():
    """CLI entry point for package installation"""
    asyncio.run(main())


if __name__ == "__main__":
    asyncio.run(main())
