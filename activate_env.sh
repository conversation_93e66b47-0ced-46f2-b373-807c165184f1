#!/bin/bash
# Activate the crypto trading system development environment

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Activating Crypto Trading System Environment${NC}"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ Virtual environment not found. Run setup_dev_environment.sh first.${NC}"
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CRYPTO_TRADING_DEV=1

echo -e "${GREEN}✅ Environment activated!${NC}"
echo -e "${GREEN}📁 Project root: $(pwd)${NC}"
echo -e "${GREEN}🐍 Python: $(which python)${NC}"
echo -e "${GREEN}📦 Pip: $(which pip)${NC}"

# Show available commands
echo ""
echo -e "${BLUE}Available commands:${NC}"
echo "  python crypto_trading_system/scripts/validate_apis.py    - Validate API configurations"
echo "  python crypto_trading_system/scripts/init_database.py   - Initialize database"
echo "  python crypto_trading_system/scripts/test_system.py     - Run system tests"
echo "  python crypto_trading_system/main.py                    - Start the trading system"
echo ""
echo "To deactivate: deactivate"
