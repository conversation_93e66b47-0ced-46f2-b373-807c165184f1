#!/bin/bash

# Multi-Agent Crypto Trading System - Development Environment Setup
# This script creates an isolated Python environment and installs all dependencies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="crypto-trading-system"
PYTHON_VERSION="3.11"
VENV_NAME="venv"

echo -e "${BLUE}🚀 Multi-Agent Crypto Trading System - Development Environment Setup${NC}"
echo -e "${BLUE}=================================================================${NC}"

# Function to print status messages
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Python 3.11+ is available
check_python() {
    print_info "Checking Python installation..."
    
    if command -v python3.11 &> /dev/null; then
        PYTHON_CMD="python3.11"
    elif command -v python3 &> /dev/null; then
        PYTHON_VERSION_INSTALLED=$(python3 --version | cut -d' ' -f2)
        MAJOR_VERSION=$(echo $PYTHON_VERSION_INSTALLED | cut -d'.' -f1)
        MINOR_VERSION=$(echo $PYTHON_VERSION_INSTALLED | cut -d'.' -f2)

        if [[ $MAJOR_VERSION -eq 3 && $MINOR_VERSION -ge 9 ]]; then
            PYTHON_CMD="python3"
        else
            print_error "Python 3.9+ is required. Found: $PYTHON_VERSION_INSTALLED"
            exit 1
        fi
    else
        print_error "Python 3.9+ is not installed. Please install Python first."
        exit 1
    fi
    
    print_status "Python found: $($PYTHON_CMD --version)"
}

# Create virtual environment
create_venv() {
    print_info "Creating virtual environment..."
    
    if [ -d "$VENV_NAME" ]; then
        print_warning "Virtual environment already exists. Removing old environment..."
        rm -rf "$VENV_NAME"
    fi
    
    $PYTHON_CMD -m venv "$VENV_NAME"
    print_status "Virtual environment created: $VENV_NAME"
}

# Activate virtual environment
activate_venv() {
    print_info "Activating virtual environment..."
    source "$VENV_NAME/bin/activate"
    print_status "Virtual environment activated"
}

# Upgrade pip and install wheel
upgrade_pip() {
    print_info "Upgrading pip and installing wheel..."
    pip install --upgrade pip wheel setuptools
    print_status "Pip upgraded and wheel installed"
}

# Install dependencies
install_dependencies() {
    print_info "Installing project dependencies..."

    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt not found!"
        exit 1
    fi

    # Install dependencies with trusted hosts to handle SSL issues
    pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
    print_status "All dependencies installed successfully"
}

# Verify installation
verify_installation() {
    print_info "Verifying installation..."
    
    # Check key packages
    python -c "import autogen_core; print(f'AutoGen Core: {autogen_core.__version__}')" 2>/dev/null || print_warning "AutoGen Core not found"
    python -c "import supabase; print('Supabase: OK')" 2>/dev/null || print_warning "Supabase client not found"
    python -c "import anthropic; print('Anthropic: OK')" 2>/dev/null || print_warning "Anthropic client not found"
    python -c "import asyncio; print('AsyncIO: OK')" 2>/dev/null || print_error "AsyncIO not available"
    
    print_status "Installation verification completed"
}

# Create activation script
create_activation_script() {
    print_info "Creating activation script..."
    
    cat > activate_env.sh << 'EOF'
#!/bin/bash
# Activate the crypto trading system development environment

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Activating Crypto Trading System Environment${NC}"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ Virtual environment not found. Run setup_dev_environment.sh first.${NC}"
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CRYPTO_TRADING_DEV=1

echo -e "${GREEN}✅ Environment activated!${NC}"
echo -e "${GREEN}📁 Project root: $(pwd)${NC}"
echo -e "${GREEN}🐍 Python: $(which python)${NC}"
echo -e "${GREEN}📦 Pip: $(which pip)${NC}"

# Show available commands
echo ""
echo -e "${BLUE}Available commands:${NC}"
echo "  python crypto_trading_system/scripts/validate_apis.py    - Validate API configurations"
echo "  python crypto_trading_system/scripts/init_database.py   - Initialize database"
echo "  python crypto_trading_system/scripts/test_system.py     - Run system tests"
echo "  python crypto_trading_system/main.py                    - Start the trading system"
echo ""
echo "To deactivate: deactivate"
EOF

    chmod +x activate_env.sh
    print_status "Activation script created: activate_env.sh"
}

# Create development configuration
create_dev_config() {
    print_info "Creating development configuration..."
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_status "Created .env from .env.example"
            print_warning "Please configure your API keys in .env file"
        else
            print_warning ".env.example not found. Please create .env manually"
        fi
    else
        print_status ".env file already exists"
    fi
}

# Main setup function
main() {
    echo ""
    print_info "Starting development environment setup..."
    echo ""
    
    check_python
    create_venv
    activate_venv
    upgrade_pip
    install_dependencies
    verify_installation
    create_activation_script
    create_dev_config
    
    echo ""
    echo -e "${GREEN}🎉 Development environment setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Configure your API keys in the .env file"
    echo "2. Run: source activate_env.sh"
    echo "3. Validate APIs: python crypto_trading_system/scripts/validate_apis.py"
    echo "4. Initialize database: python crypto_trading_system/scripts/init_database.py"
    echo "5. Run tests: python crypto_trading_system/scripts/test_system.py"
    echo ""
    echo -e "${YELLOW}⚠️  Remember to keep your .env file secure and never commit it to version control!${NC}"
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
